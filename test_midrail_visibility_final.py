#!/usr/bin/env python3
"""
Final test to verify midrail visibility fix is working
"""
import requests
import json

def test_midrail_visibility():
    """Test if midrail field is now visible with our calculated fields fix"""
    
    # Test field values (same as in our test case)
    field_values = {
        "bx_dbl_hinge_location": "brisbane",
        "bx_dbl_hinge_jamb_reveal_gt_20mm": "yes_gt_20mm",
        "bx_dbl_hinge_new_door_clear_handle": "yes",
        "bx_dbl_hinge_handle_clear_existing": "yes",
        "bx_dbl_hinge_swing_path_clear": "yes",
        "bx_dbl_hinge_quantity": 1,
        "bx_dbl_hinge_frame_colour": "black_anodise_15mm",
        "bx_dbl_hinge_mesh_type": "pet_mesh",
        "bx_dbl_hinge_door_height_mm": 2100,
        "bx_dbl_hinge_door_width_mm": 1200,
        "bx_dbl_hinge_num_sec_hinges_pickup": 0,
        "bx_dbl_hinge_num_sec_hinges_deliver": "3_per_door_loose_drilled",
        "bx_dbl_hinge_sec_hinge_packers_qty": 0
    }
    
    print("=== TESTING MIDRAIL VISIBILITY FIX ===")
    print(f"Input: height={field_values.get('bx_dbl_hinge_door_height_mm')}, width={field_values.get('bx_dbl_hinge_door_width_mm')}")
    
    # Test calculated fields endpoint
    try:
        response = requests.post('http://localhost:8069/config_matrix/calculate_field_values', 
                               json={
                                   "jsonrpc": "2.0",
                                   "method": "call",
                                   "params": {
                                       "template_id": 24,
                                       "field_values": field_values
                                   }
                               },
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result and 'result' in result['result']:
                calculated_fields = result['result']['result']
                
                # Check for the specific fields we need
                height = calculated_fields.get('_CALCULATED_largest_door_height')
                width = calculated_fields.get('_CALCULATED_largest_door_width')
                
                print(f"\n✅ CALCULATED FIELDS:")
                print(f"  _CALCULATED_largest_door_height: {height}")
                print(f"  _CALCULATED_largest_door_width: {width}")
                
                if height and width:
                    print(f"\n✅ SUCCESS: Both calculated fields are available!")
                    
                    # Test midrail visibility condition
                    # Original condition: (_CALCULATED_largest_door_height <= 2510 && _CALCULATED_largest_door_width <= 1310) || _CALCULATED_largest_door_height <= 1310
                    condition1 = height <= 2510 and width <= 1310
                    condition2 = height <= 1310
                    visibility = condition1 or condition2
                    
                    print(f"\n🔍 MIDRAIL VISIBILITY TEST:")
                    print(f"  Condition 1: {height} <= 2510 && {width} <= 1310 = {condition1}")
                    print(f"  Condition 2: {height} <= 1310 = {condition2}")
                    print(f"  Final visibility: {condition1} OR {condition2} = {visibility}")
                    
                    if visibility:
                        print(f"\n🎉 SUCCESS: Midrail (Conditional Case 2) field SHOULD be visible!")
                        print(f"   The calculated fields fix is working correctly!")
                        
                        # Test field visibility endpoint
                        print(f"\n=== TESTING FIELD VISIBILITY ===")
                        visibility_response = requests.post('http://localhost:8069/config_matrix/get_field_visibility', 
                                                          json={
                                                              "jsonrpc": "2.0",
                                                              "method": "call",
                                                              "params": {
                                                                  "template_id": 24,
                                                                  "field_values": field_values
                                                              }
                                                          },
                                                          headers={'Content-Type': 'application/json'})
                        
                        if visibility_response.status_code == 200:
                            vis_result = visibility_response.json()
                            if 'result' in vis_result:
                                visible_fields = vis_result['result']
                                
                                # Look for midrail fields
                                midrail_fields = [field for field in visible_fields if 'midrail' in field.lower()]
                                print(f"  Visible midrail fields: {midrail_fields}")
                                
                                if midrail_fields:
                                    print(f"  ✅ Midrail fields are visible in the UI!")
                                else:
                                    print(f"  ❌ No midrail fields found in visible fields")
                            else:
                                print(f"  ❌ Unexpected visibility response: {vis_result}")
                        else:
                            print(f"  ❌ Field visibility test failed: {visibility_response.status_code}")
                    else:
                        print(f"\n❌ FAILED: Midrail field should NOT be visible with these dimensions")
                        print(f"   This indicates the visibility condition is working correctly")
                        print(f"   but the dimensions don't meet the criteria")
                else:
                    print(f"\n❌ FAILED: Calculated fields are missing")
                    print(f"Available calculated fields: {list(calculated_fields.keys())}")
            else:
                print(f"❌ Unexpected response format: {result}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing calculated fields: {e}")

if __name__ == "__main__":
    test_midrail_visibility()
