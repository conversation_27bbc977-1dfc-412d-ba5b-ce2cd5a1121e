# Log Filter Scripts

This collection of Python scripts helps you filter Odoo log files to extract only entries containing specific debug tags like `[BOM_PREVIEW_DEBUG]` or `[QUANTITY_DEBUG]`.

## Files Overview

- **`log_filter.py`** - Full-featured log filter with advanced options
- **`simple_log_filter.py`** - Simple, easy-to-use log filter
- **`example_usage.py`** - Example script showing how to use the filters
- **`README_log_filter.md`** - This documentation file

## Quick Start

### Using the Simple Log Filter

The simplest way to filter your log file:

```bash
# Filter for specific debug tags
python simple_log_filter.py log/odoo-server.log filtered.log BOM_PREVIEW_DEBUG QUANTITY_DEBUG

# Filter for only QUANTITY_DEBUG
python simple_log_filter.py log/odoo-server.log quantity_debug.log QUANTITY_DEBUG

# Filter for only BOM_PREVIEW_DEBUG
python simple_log_filter.py log/odoo-server.log bom_debug.log BOM_PREVIEW_DEBUG
```

### Using the Advanced Log Filter

For more control and options:

```bash
# Basic usage
python log_filter.py log/odoo-server.log output.log --tags BOM_PREVIEW_DEBUG QUANTITY_DEBUG

# Use regex patterns
python log_filter.py log/odoo-server.log output.log --tags ".*_DEBUG" --regex

# Case-sensitive matching
python log_filter.py log/odoo-server.log output.log --tags BOM_PREVIEW_DEBUG --case-sensitive

# Show detailed statistics
python log_filter.py log/odoo-server.log output.log --tags BOM_PREVIEW_DEBUG --stats --verbose
```

## Examples

### Example 1: Filter for QUANTITY_DEBUG

```bash
python simple_log_filter.py log/odoo-server.log quantity_debug.log QUANTITY_DEBUG
```

**Output:**
```
🔍 Filtering log file: log/odoo-server.log
📝 Looking for tags: QUANTITY_DEBUG
💾 Output file: quantity_debug.log

✅ Filtering completed!
📊 Total lines: 2,879
📊 Matching lines: 9
📊 Output file: quantity_debug.log
```

### Example 2: Filter for Multiple Tags

```bash
python simple_log_filter.py log/odoo-server.log combined_debug.log BOM_PREVIEW_DEBUG QUANTITY_DEBUG
```

### Example 3: Run the Example Script

```bash
python example_usage.py
```

This will create a `filtered_logs/` directory with multiple filtered log files.

## Advanced Features

### Regex Pattern Matching

Use regex patterns to match multiple tags:

```bash
# Match all debug tags ending with _DEBUG
python log_filter.py log/odoo-server.log all_debug.log --tags ".*_DEBUG" --regex

# Match tags starting with BOM_
python log_filter.py log/odoo-server.log bom_tags.log --tags "BOM_.*" --regex
```

### Case-Sensitive Matching

```bash
python log_filter.py log/odoo-server.log output.log --tags BOM_PREVIEW_DEBUG --case-sensitive
```

### Detailed Statistics

```bash
python log_filter.py log/odoo-server.log output.log --tags BOM_PREVIEW_DEBUG --stats
```

**Sample Output:**
```
============================================================
FILTERING STATISTICS
============================================================
Total lines processed: 2,879
Matching lines found: 4
Files processed: 1
Match percentage: 0.14%

Tags searched for: BOM_PREVIEW_DEBUG
Tags actually found: [BOM_PREVIEW_DEBUG]

✅ Successfully extracted 4 matching lines.
```

## Command Line Options

### Simple Log Filter

```bash
python simple_log_filter.py <input_file> <output_file> <tag1> [tag2] [tag3] ...
```

**Arguments:**
- `input_file` - Path to the input log file
- `output_file` - Path to the output filtered log file
- `tag1`, `tag2`, etc. - Debug tags to filter for

### Advanced Log Filter

```bash
python log_filter.py <input_file> <output_file> --tags <tag1> [tag2] ... [options]
```

**Options:**
- `--tags` - Debug tags to filter for (required)
- `--regex` - Treat tags as regex patterns
- `--case-sensitive` - Perform case-sensitive matching
- `--verbose` - Enable verbose output
- `--stats` - Show detailed statistics after filtering

## Understanding Your Log Structure

Based on your log file, the structure looks like this:

```
2025-09-03 05:58:02,139 302694 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [QUANTITY_DEBUG] _calculate_component_quantity called with:
```

**Components:**
- **Timestamp**: `2025-09-03 05:58:02,139`
- **Process ID**: `302694`
- **Log Level**: `INFO`
- **Database**: `canbrax_dev_11`
- **Module Path**: `odoo.addons.canbrax_configmatrix.models.config_matrix_configuration`
- **Debug Tag**: `[QUANTITY_DEBUG]`
- **Message**: `_calculate_component_quantity called with:`

## Common Debug Tags in Your Log

Based on the analysis of your log file, here are the debug tags found:

- `[QUANTITY_DEBUG]` - 9 occurrences
- `[BOM_PREVIEW_DEBUG]` - 4 occurrences

## Troubleshooting

### No Matching Lines Found

If you get "No matching lines found":

1. **Check tag spelling**: Make sure the tag name is exactly correct
2. **Check case sensitivity**: Try without `--case-sensitive` flag
3. **Use regex**: Try `--regex` flag for pattern matching
4. **Check tag format**: Ensure tags are in square brackets `[TAG_NAME]`

### File Not Found Error

```bash
❌ Error: Input file not found: log/odoo-server.log
```

**Solution**: Make sure the input file path is correct and the file exists.

### Permission Denied

```bash
❌ Error: Permission denied accessing file: log/odoo-server.log
```

**Solution**: Check file permissions or run with appropriate user privileges.

## Performance Tips

1. **Large Files**: The scripts are optimized for large log files and use efficient line-by-line processing
2. **Memory Usage**: Files are processed line by line to minimize memory usage
3. **Encoding**: Uses UTF-8 encoding with error handling for malformed characters

## Integration with Other Tools

### Combine with grep for additional filtering

```bash
# First filter by debug tags, then search for specific content
python simple_log_filter.py log/odoo-server.log debug.log QUANTITY_DEBUG
grep "formula" debug.log > formula_debug.log
```

### Use with log analysis tools

```bash
# Filter debug logs and pipe to analysis tools
python simple_log_filter.py log/odoo-server.log debug.log QUANTITY_DEBUG | wc -l
```

## Customization

You can easily modify the scripts to:

1. **Add new tag patterns**: Modify the regex patterns in the scripts
2. **Change output format**: Modify the output writing logic
3. **Add filtering logic**: Add additional conditions for line filtering
4. **Integrate with other tools**: Use the filtering functions in other Python scripts

## License

These scripts are provided as-is for your Odoo development needs. Feel free to modify and distribute as needed.
