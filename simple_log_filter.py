#!/usr/bin/env python3
"""
Simple Log Filter Script

A simplified version for quick log filtering by debug tags.
"""

import sys
import re
from pathlib import Path


def filter_log_by_tags(input_file, output_file, tags):
    """
    Simple function to filter log file by debug tags.
    
    Args:
        input_file: Path to input log file
        output_file: Path to output log file  
        tags: List of tags to filter for (e.g., ['BOM_PREVIEW_DEBUG', 'QUANTITY_DEBUG'])
    """
    # Compile patterns for the tags
    patterns = []
    for tag in tags:
        # Escape special characters and create pattern
        escaped_tag = re.escape(tag)
        pattern = re.compile(escaped_tag, re.IGNORECASE)
        patterns.append(pattern)
    
    total_lines = 0
    matching_lines = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as infile:
            with open(output_file, 'w', encoding='utf-8') as outfile:
                for line in infile:
                    total_lines += 1
                    
                    # Check if line matches any of the tags
                    for pattern in patterns:
                        if pattern.search(line):
                            outfile.write(line)
                            matching_lines += 1
                            break
        
        print(f"✅ Filtering completed!")
        print(f"📊 Total lines: {total_lines:,}")
        print(f"📊 Matching lines: {matching_lines:,}")
        print(f"📊 Output file: {output_file}")
        
        if matching_lines == 0:
            print("⚠️  No matching lines found. Check your tags.")
        
    except FileNotFoundError:
        print(f"❌ Error: Input file not found: {input_file}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


def main():
    """Main function for simple usage."""
    if len(sys.argv) < 4:
        print("Usage: python simple_log_filter.py <input_file> <output_file> <tag1> [tag2] [tag3] ...")
        print("Example: python simple_log_filter.py log/odoo-server.log filtered.log BOM_PREVIEW_DEBUG QUANTITY_DEBUG")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    tags = sys.argv[3:]
    
    # Validate input file exists
    if not Path(input_file).exists():
        print(f"❌ Error: Input file does not exist: {input_file}")
        sys.exit(1)
    
    print(f"🔍 Filtering log file: {input_file}")
    print(f"📝 Looking for tags: {', '.join(tags)}")
    print(f"💾 Output file: {output_file}")
    print()
    
    filter_log_by_tags(input_file, output_file, tags)


if __name__ == "__main__":
    main()
