#!/usr/bin/env python3
"""
Example Usage Script for Log Filtering

This script demonstrates how to use the log filter with your Odoo log file.
"""

from pathlib import Path
from simple_log_filter import filter_log_by_tags


def main():
    """Demonstrate log filtering examples."""
    
    # Define file paths
    input_log = Path("log/odoo-server.log")
    output_dir = Path("filtered_logs")
    output_dir.mkdir(exist_ok=True)
    
    print("🔍 Log Filtering Examples")
    print("=" * 50)
    
    # Example 1: Filter for QUANTITY_DEBUG only
    print("\n1️⃣ Filtering for QUANTITY_DEBUG tags...")
    quantity_output = output_dir / "quantity_debug.log"
    filter_log_by_tags(
        input_file=input_log,
        output_file=quantity_output,
        tags=["QUANTITY_DEBUG"]
    )
    
    # Example 2: Filter for BOM_PREVIEW_DEBUG only
    print("\n2️⃣ Filtering for BOM_PREVIEW_DEBUG tags...")
    bom_output = output_dir / "bom_preview_debug.log"
    filter_log_by_tags(
        input_file=input_log,
        output_file=bom_output,
        tags=["BOM_PREVIEW_DEBUG"]
    )
    
    # Example 3: Filter for both debug tags
    print("\n3️⃣ Filtering for both BOM_PREVIEW_DEBUG and QUANTITY_DEBUG tags...")
    combined_output = output_dir / "combined_debug.log"
    filter_log_by_tags(
        input_file=input_log,
        output_file=combined_output,
        tags=["BOM_PREVIEW_DEBUG", "QUANTITY_DEBUG"]
    )
    
    # Example 4: Filter for all debug tags (using regex-like pattern)
    print("\n4️⃣ Filtering for all debug tags ending with _DEBUG...")
    all_debug_output = output_dir / "all_debug.log"
    filter_log_by_tags(
        input_file=input_log,
        output_file=all_debug_output,
        tags=["_DEBUG"]  # This will match any tag containing _DEBUG
    )
    
    print("\n✅ All filtering examples completed!")
    print(f"📁 Check the '{output_dir}' directory for filtered log files.")
    
    # Show file sizes
    print("\n📊 Filtered file sizes:")
    for file_path in output_dir.glob("*.log"):
        size = file_path.stat().st_size
        print(f"   {file_path.name}: {size:,} bytes")


if __name__ == "__main__":
    main()
