<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Configuration Matrix Configuration Mesh Extensions -->
    
    <!-- Extend Configuration Form View -->
    <record id="view_config_matrix_configuration_form_mesh" model="ir.ui.view">
        <field name="name">config.matrix.configuration.form.mesh</field>
        <field name="model">config.matrix.configuration</field>
        <field name="inherit_id" ref="canbrax_configmatrix.view_config_matrix_configuration_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@class='oe_title']" position="before">
                <div class="oe_button_box" name="button_box">
                    <button name="action_view_mesh_cut_operation" type="object"
                            class="oe_stat_button" icon="fa-scissors"
                            invisible="not requires_mesh">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Mesh Cut</span>
                            <span class="o_stat_text">Operation</span>
                        </div>
                    </button>
                </div>
            </xpath>
            
            <xpath expr="//notebook" position="inside">
                <page string="Mesh Requirements" name="mesh_requirements" 
                      invisible="not requires_mesh">
                    <div class="alert alert-info" invisible="not requires_mesh">
                        <p><i class="fa fa-info-circle"/> This configuration requires mesh cutting</p>
                    </div>
                    
                    <group>
                        <group string="Mesh Requirements">
                            <field name="requires_mesh" readonly="1"/>
                            <field name="mesh_width_required" readonly="1"/>
                            <field name="mesh_height_required" readonly="1"/>
                            <field name="mesh_series_required" readonly="1"/>
                        </group>
                        <group string="Cut Operation">
                            <field name="mesh_cut_operation_id" readonly="1"/>
                        </group>
                    </group>
                    
                    <div class="oe_button_box" name="mesh_action_box">
                        <button name="action_create_mesh_cut_operation" type="object" 
                                string="Create Cut Operation" class="btn-primary"
                                invisible="mesh_cut_operation_id"/>
                        <button name="action_view_mesh_cut_operation" type="object" 
                                string="View Cut Operation" class="btn-secondary"
                                invisible="not mesh_cut_operation_id"/>
                    </div>
                </page>
            </xpath>
        </field>
    </record>
    
    <!-- Extend Configuration Tree View -->
    <record id="view_config_matrix_configuration_tree_mesh" model="ir.ui.view">
        <field name="name">config.matrix.configuration.tree.mesh</field>
        <field name="model">config.matrix.configuration</field>
        <field name="inherit_id" ref="canbrax_configmatrix.view_config_matrix_configuration_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="before">
                <field name="requires_mesh" optional="hide"/>
                <field name="mesh_width_required" optional="hide"/>
                <field name="mesh_height_required" optional="hide"/>
                <field name="mesh_series_required" optional="hide"/>
            </xpath>
        </field>
    </record>
    
    <!-- Extend Configuration Search View -->
    <record id="view_config_matrix_configuration_search_mesh" model="ir.ui.view">
        <field name="name">config.matrix.configuration.search.mesh</field>
        <field name="model">config.matrix.configuration</field>
        <field name="inherit_id" ref="canbrax_configmatrix.view_config_matrix_configuration_search"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <separator/>
                <filter string="Requires Mesh" name="requires_mesh" 
                        domain="[('requires_mesh', '=', True)]"/>
                <filter string="Has Cut Operation" name="has_cut_operation" 
                        domain="[('mesh_cut_operation_id', '!=', False)]"/>
                <separator/>
                <filter string="Saltwater Mesh" name="saltwater_mesh" 
                        domain="[('mesh_series_required', '=', 'saltwater')]"/>
                <filter string="Diamond Mesh" name="diamond_mesh" 
                        domain="[('mesh_series_required', '=', 'diamond')]"/>
                <filter string="Flyscreen Mesh" name="flyscreen_mesh" 
                        domain="[('mesh_series_required', '=', 'flyscreen')]"/>
                <group expand="0" string="Group By">
                    <filter string="Requires Mesh" name="group_requires_mesh" 
                            context="{'group_by': 'requires_mesh'}"/>
                    <filter string="Mesh Series" name="group_mesh_series" 
                            context="{'group_by': 'mesh_series_required'}"/>
                </group>
            </xpath>
        </field>
    </record>
    
</odoo>
