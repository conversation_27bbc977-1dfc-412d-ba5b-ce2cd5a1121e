<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- List View -->
    <record id="mesh_matrix_cell_assignment_list" model="ir.ui.view">
        <field name="name">mesh.matrix.cell.assignment.list</field>
        <field name="model">mesh.matrix.cell.assignment</field>
        <field name="arch" type="xml">
            <list string="Matrix Cell Assignments" decoration-success="has_assignment" decoration-muted="not has_assignment">
                <field name="matrix_id"/>
                <field name="matrix_series"/>
                <field name="orientation_methodology" optional="show"/>
                <field name="cell_reference"/>
                <field name="height" string="Height/Largest"/>
                <field name="width" string="Width/Smallest"/>
                <field name="primary_cut_plan_id"/>
                <field name="secondary_cut_plan_id"/>
                <field name="arrow_direction"/>
                <field name="cut_to_size"/>
                <field name="primary_master_product"/>
                <field name="primary_master_dimensions"/>
                <field name="has_assignment" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="mesh_matrix_cell_assignment_form" model="ir.ui.view">
        <field name="name">mesh.matrix.cell.assignment.form</field>
        <field name="model">mesh.matrix.cell.assignment</field>
        <field name="arch" type="xml">
            <form string="Matrix Cell Assignment">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_cut_plan" type="object"
                                class="oe_stat_button" icon="fa-scissors"
                                invisible="not primary_cut_plan_id">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">View Cut Plan</span>
                            </div>
                        </button>
                    </div>

                    <div class="oe_title">
                        <label for="display_name" class="oe_edit_only"/>
                        <h1><field name="display_name" readonly="1"/></h1>
                    </div>

                    <group>
                        <group name="matrix_info">
                            <field name="matrix_id" required="1"/>
                            <field name="matrix_series" readonly="1"/>
                            <field name="cell_reference" readonly="1"/>
                        </group>
                        <group name="dimensions">
                            <!-- Show current orientation methodology -->
                            <field name="orientation_methodology" readonly="1" string="Matrix Orientation"/>

                            <!-- Dynamic dimension labels -->
                            <label for="height" string="Height (mm)"
                                   invisible="orientation_methodology == 'largest_smallest'"/>
                            <label for="height" string="Largest Size (mm)"
                                   invisible="orientation_methodology != 'largest_smallest'"/>
                            <field name="height" required="1" nolabel="1"/>

                            <label for="width" string="Width (mm)"
                                   invisible="orientation_methodology == 'largest_smallest'"/>
                            <label for="width" string="Smallest Size (mm)"
                                   invisible="orientation_methodology != 'largest_smallest'"/>
                            <field name="width" required="1" nolabel="1"/>
                        </group>
                    </group>

                    <group string="Cut Plan Assignments">
                        <group name="primary_plan">
                            <field name="primary_cut_plan_id" 
                                   domain="[('matrix_id', '=', matrix_id)]"
                                   context="{'default_matrix_id': matrix_id}"/>
                            <field name="primary_master_product" readonly="1"/>
                            <field name="primary_master_dimensions" readonly="1"/>
                        </group>
                        <group name="secondary_plan">
                            <field name="secondary_cut_plan_id" 
                                   domain="[('matrix_id', '=', matrix_id), ('id', '!=', primary_cut_plan_id)]"
                                   context="{'default_matrix_id': matrix_id}"/>
                        </group>
                    </group>

                    <group string="Additional Information">
                        <field name="arrow_direction" placeholder="Navigation direction for matrix lookup"/>
                        <field name="cut_to_size" string="Cut to Size"/>
                        <field name="notes" placeholder="Additional notes about this cell assignment"/>
                    </group>

                    <group string="Status">
                        <field name="has_assignment" readonly="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="mesh_matrix_cell_assignment_search" model="ir.ui.view">
        <field name="name">mesh.matrix.cell.assignment.search</field>
        <field name="model">mesh.matrix.cell.assignment</field>
        <field name="arch" type="xml">
            <search string="Search Cell Assignments">
                <field name="matrix_id"/>
                <field name="matrix_series"/>
                <field name="cell_reference"/>
                <field name="height"/>
                <field name="width"/>
                <field name="primary_cut_plan_id"/>
                <field name="arrow_direction"/>
                
                <filter string="Has Assignment" name="has_assignment" domain="[('has_assignment', '=', True)]"/>
                <filter string="No Assignment" name="no_assignment" domain="[('has_assignment', '=', False)]"/>
                <filter string="Has Cut Plan" name="has_cut_plan" domain="[('primary_cut_plan_id', '!=', False)]"/>
                <filter string="Has Arrow" name="has_arrow" domain="[('arrow_direction', '!=', False)]"/>
                
                <separator/>
                <filter string="Right Arrow" name="right_arrow" domain="[('arrow_direction', '=', '→')]"/>
                <filter string="Down Arrow" name="down_arrow" domain="[('arrow_direction', '=', '↓')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Matrix" name="group_matrix" context="{'group_by': 'matrix_id'}"/>
                    <filter string="Matrix Series" name="group_series" context="{'group_by': 'matrix_series'}"/>
                    <filter string="Height" name="group_height" context="{'group_by': 'height'}"/>
                    <filter string="Width" name="group_width" context="{'group_by': 'width'}"/>
                    <filter string="Arrow Direction" name="group_arrow" context="{'group_by': 'arrow_direction'}"/>
                    <filter string="Primary Cut Plan" name="group_cut_plan" context="{'group_by': 'primary_cut_plan_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_mesh_matrix_cell_assignment" model="ir.actions.act_window">
        <field name="name">Matrix Cell Assignments</field>
        <field name="res_model">mesh.matrix.cell.assignment</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="mesh_matrix_cell_assignment_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first matrix cell assignment!
            </p>
            <p>
                Matrix cell assignments define which cut plans should be used
                for specific dimensions in your cutting matrix.
            </p>
        </field>
    </record>



</odoo>
