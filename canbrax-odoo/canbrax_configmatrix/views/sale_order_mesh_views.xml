<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Sale Order Mesh Extensions -->
    
    <!-- Note: Sale order line form is embedded in sale order form,
         so mesh cutting info will be shown in the sale order form extensions below -->
    
    <!-- Extend Sale Order Line Tree View -->
    <record id="view_sale_order_line_tree_mesh" model="ir.ui.view">
        <field name="name">sale.order.line.tree.mesh</field>
        <field name="model">sale.order.line</field>
        <field name="inherit_id" ref="sale.view_order_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="before">
                <field name="requires_mesh_cutting" optional="hide"/>
                <field name="mesh_cut_count" optional="hide"/>
            </xpath>
        </field>
    </record>
    
    <!-- Extend Sale Order Form View -->
    <record id="view_sale_order_form_mesh" model="ir.ui.view">
        <field name="name">sale.order.form.mesh</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Add mesh cutting info to order line list -->
            <xpath expr="//field[@name='order_line']/list" position="attributes">
                <attribute name="decoration-warning">requires_mesh_cutting and mesh_cut_count == 0</attribute>
                <attribute name="decoration-success">requires_mesh_cutting and mesh_cut_count > 0</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='price_subtotal']" position="after">
                <field name="requires_mesh_cutting" optional="hide"/>
                <field name="mesh_cut_count" optional="hide"/>
            </xpath>

            <!-- Add mesh cutting info to embedded order line form -->
            <xpath expr="//field[@name='order_line']/form//field[@name='config_id']" position="after">
                <field name="requires_mesh_cutting" readonly="1" invisible="not requires_mesh_cutting"/>
                <field name="mesh_cut_count" readonly="1" invisible="not requires_mesh_cutting"/>
            </xpath>

            <!-- Add mesh operations button to embedded form -->
            <xpath expr="//field[@name='order_line']/form//button[@name='action_view_configuration']" position="after">
                <button name="action_view_mesh_operations" type="object"
                        string="Mesh Operations" class="btn-sm btn-secondary" icon="fa-scissors"
                        invisible="not requires_mesh_cutting or mesh_cut_count == 0"/>
            </xpath>
        </field>
    </record>
    
</odoo>
