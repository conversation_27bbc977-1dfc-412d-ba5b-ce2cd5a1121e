<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Products Action -->
    <record id="action_mesh_products" model="ir.actions.act_window">
        <field name="name">Mesh Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('is_mesh_product', '=', True)]</field>
        <field name="context">{
            'search_default_mesh_products': 1,
            'default_is_mesh_product': True
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No mesh products found
            </p>
            <p>
                Create products and enable the "Is Mesh Product" checkbox to see them here.
                Mesh products include Master Sheets, Planned Off-cuts, and Unplanned Off-cuts.
            </p>
        </field>
    </record>

    <!-- Mesh Products Tree View Reference -->
    <record id="action_mesh_products_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="view_product_template_tree_mesh"/>
        <field name="act_window_id" ref="action_mesh_products"/>
    </record>

    <!-- Mesh Inventory Management Menu Structure -->
    
    <!-- ========================================= -->
    <!-- MESH MENUS INTEGRATED INTO MAIN STRUCTURE -->
    <!-- See menu_views.xml for the new integrated structure: -->
    <!-- - Mesh Products: Products → Mesh Products -->
    <!-- - Mesh Operations: Mesh Operations → Cut Operations, Cut Matrices, Cut Plans -->
    <!-- ========================================= -->
    
</odoo>
