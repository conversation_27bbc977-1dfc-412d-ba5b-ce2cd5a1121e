<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Visual Matrix Editor Form View -->
    <record id="view_config_matrix_visual_editor_form" model="ir.ui.view">
        <field name="name">config.matrix.visual.editor.form</field>
        <field name="model">config.matrix.visual.editor</field>
        <field name="arch" type="xml">
            <form string="Visual Matrix Editor">
                <header>
                    <button name="action_save_matrix" string="Save Matrix" type="object" class="btn-primary"/>
                    <button name="action_test_lookup" string="Test Lookup" type="object" class="btn-secondary"/>
                    <button name="action_fill_sample_data" string="Fill Sample Data" type="object" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Matrix Name"/></h1>
                    </div>

                    <group>
                        <group>
                            <field name="matrix_type" widget="radio"/>
                            <field name="product_template_id" options="{'no_create': True}"/>
                        </group>
                    </group>

                    <group string="Matrix Dimensions">
                        <div class="alert alert-info" role="alert">
                            <p><strong>Set Your Dimensions</strong></p>
                            <p>Enter comma-separated values for heights and widths. The matrix will be generated automatically.</p>
                            <p><strong>Example:</strong> 1000,1200,1500,1800,2000</p>
                        </div>
                        <group>
                            <field name="height_values" placeholder="e.g., 1000,1200,1500,1800,2000"/>
                            <field name="width_values" placeholder="e.g., 600,800,1000,1200,1500"/>
                        </group>
                        <group>
                            <field name="matrix_rows" readonly="1"/>
                            <field name="matrix_cols" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Visual Matrix Editor" name="visual_editor">
                            <div class="alert alert-success" role="alert">
                                <p><strong>Excel-Like Matrix Editor</strong></p>
                                <p>Click on any cell to enter values. Changes are saved automatically to the JSON data below.</p>
                                <ul>
                                    <li>💡 <strong>Tip:</strong> Use Tab to move between cells</li>
                                    <li>💡 <strong>Tip:</strong> Leave cells empty for "no price/time"</li>
                                    <li>💡 <strong>Tip:</strong> Use "Fill Sample Data" to generate test values</li>
                                </ul>
                            </div>
                            <field name="matrix_html" nolabel="1" widget="html"/>
                        </page>

                        <page string="JSON Data" name="json_data">
                            <div class="alert alert-warning" role="alert">
                                <p><strong>Advanced: Direct JSON Editing</strong></p>
                                <p>This is the raw matrix data. Format: {"height_width": value}</p>
                                <p><strong>Example:</strong> {"1200_800": 211.48, "1500_1000": 275.60}</p>
                            </div>
                            <field name="matrix_data_json" widget="code" options="{'mode': 'javascript'}" nolabel="1"/>
                        </page>

                        <page string="Preview &amp; Test" name="preview">
                            <div class="alert alert-info" role="alert">
                                <p><strong>Matrix Preview</strong></p>
                                <p>Current matrix: <strong><field name="matrix_rows"/> x <field name="matrix_cols"/></strong> cells</p>
                            </div>

                            <group string="Test Lookup">
                                <div class="alert alert-primary" role="alert">
                                    <p>Use "Test Lookup" button to verify your matrix data is working correctly.</p>
                                </div>
                            </group>
                        </page>
                    </notebook>

                    <!-- Hidden technical fields -->
                    <field name="matrix_id" invisible="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Visual Editor Actions -->
    <record id="action_config_matrix_visual_editor_price" model="ir.actions.act_window">
        <field name="name">Create Price Matrix (Visual)</field>
        <field name="res_model">config.matrix.visual.editor</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'default_matrix_type': 'price'}</field>
    </record>



    <!-- Server Actions for Menu Items -->
    <record id="action_create_price_matrix_visual" model="ir.actions.server">
        <field name="name">Create Price Matrix (Visual)</field>
        <field name="model_id" ref="model_config_matrix_visual_editor"/>
        <field name="state">code</field>
        <field name="code">
# Create new visual editor for price matrix
editor = model.create_new_matrix('price')
action = {
    'type': 'ir.actions.act_window',
    'name': 'Visual Price Matrix Editor',
    'res_model': 'config.matrix.visual.editor',
    'res_id': editor.id,
    'view_mode': 'form',
    'target': 'current',
}
        </field>
    </record>



    <!-- Full Screen Visual Matrix Editor Action -->
    <record id="action_config_matrix_visual_editor_fullscreen" model="ir.actions.act_window">
        <field name="name">Visual Matrix Editor</field>
        <field name="res_model">config.matrix.visual.editor</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
        <field name="context">{
            'default_matrix_type': 'price'
        }</field>
    </record>
</odoo>
