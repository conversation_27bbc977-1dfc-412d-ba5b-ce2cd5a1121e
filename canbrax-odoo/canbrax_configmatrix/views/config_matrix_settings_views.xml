<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Configuration Matrix Settings Form View -->
        <record id="view_config_matrix_settings_form" model="ir.ui.view">
            <field name="name">config.matrix.settings.form</field>
            <field name="model">config.matrix.settings</field>
            <field name="arch" type="xml">
                <form string="Matrix Configuration Settings">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="Default Work Centers">
                                <field name="default_mesh_workcenter_id"
                                       options="{'no_create': True, 'no_open': True}"/>
                                <field name="default_cutting_workcenter_id"
                                       options="{'no_create': True, 'no_open': True}"/>
                                <field name="default_assembly_workcenter_id"
                                       options="{'no_create': True, 'no_open': True}"/>
                            </group>
                            
                            <group string="Mesh Operation Settings">
                                <field name="mesh_default_duration"/>
                                <field name="mesh_cost_per_sqm"/>
                            </group>
                        </group>
                        
                        <group>
                            <group string="General Settings">
                                <field name="default_operation_duration"/>
                                <field name="auto_assign_workcenters"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Configuration Matrix Settings Server Action -->
        <record id="action_config_matrix_settings_server" model="ir.actions.server">
            <field name="name">Matrix Configuration</field>
            <field name="model_id" ref="model_config_matrix_settings"/>
            <field name="state">code</field>
            <field name="code">
# Get or create the singleton settings record
settings = env['config.matrix.settings'].get_settings()
action = {
    'type': 'ir.actions.act_window',
    'name': 'Matrix Configuration',
    'res_model': 'config.matrix.settings',
    'view_mode': 'form',
    'res_id': settings.id,
    'target': 'current',
    'context': {'create': False},
}
            </field>
        </record>

        <!-- Configuration Matrix Settings Window Action (fallback) -->
        <record id="action_config_matrix_settings" model="ir.actions.act_window">
            <field name="name">Matrix Configuration</field>
            <field name="res_model">config.matrix.settings</field>
            <field name="view_mode">form</field>
            <field name="target">current</field>
            <field name="context">{'create': False}</field>
        </record>

        <!-- Menu Item - Moved to menu_views.xml for consolidated menu structure -->
    </data>
</odoo>
