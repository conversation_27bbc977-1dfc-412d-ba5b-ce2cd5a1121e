<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Sales Order Form View Extension for Review State -->
    <record id="view_order_form_review_state" model="ir.ui.view">
        <field name="name">sale.order.form.review.state</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Add Review button after Send by Email button -->
            <xpath expr="//button[@name='action_quotation_send']" position="after">
                <button name="action_set_review" type="object" string="Set to Review" 
                        class="btn-primary" invisible="state not in ['draft', 'sent']"/>
            </xpath>
            
            <!-- Modify existing Confirm Order button to work from sent and review states -->
            <xpath expr="//button[@name='action_confirm']" position="attributes">
                <attribute name="invisible">state not in ['sent', 'review']</attribute>
                <attribute name="string">Confirm Order</attribute>
            </xpath>
            
            <!-- Update state field to show Review state in correct order -->
            <xpath expr="//field[@name='state']" position="attributes">
                <attribute name="statusbar_visible">draft,sent,review,sale,done</attribute>
            </xpath>
        </field>
    </record>

    <!-- Sales Order Tree View Extension -->
    <record id="view_quotation_tree_review_state" model="ir.ui.view">
        <field name="name">sale.order.tree.review.state</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_quotation_tree"/>
        <field name="arch" type="xml">
            <!-- Ensure state field shows Review option -->
            <xpath expr="//field[@name='state']" position="attributes">
                <attribute name="decoration-info">state == 'review'</attribute>
            </xpath>
        </field>
    </record>

    <!-- Sales Order Search View Extension -->
    <record id="view_sales_order_filter_review" model="ir.ui.view">
        <field name="name">sale.order.search.review</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_sales_order_filter"/>
        <field name="arch" type="xml">
            <!-- Add Review filter after existing filters -->
            <xpath expr="//search" position="inside">
                <filter string="Review" name="review" domain="[('state','=','review')]"/>
            </xpath>
        </field>
    </record>
</odoo>
