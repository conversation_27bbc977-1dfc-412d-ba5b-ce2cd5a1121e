<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Operation Template Form View -->
    <record id="view_config_matrix_operation_template_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.form</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <form string="Operation Template">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_usage" type="object" class="oe_stat_button" icon="fa-list">
                            <field name="usage_count" widget="statinfo" string="Used"/>
                        </button>
                    </div>

                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Operation Name"/>
                        </h1>
                    </div>

                    <group>
                        <group>
                            <field name="active"/>
                            <field name="sequence"/>
                            <field name="category"/>
                        </group>
                        <group>
                            <field name="config_template_id" string="Configuration Template"
                                   help="Select the configuration template to access field values in formulas"/>
                            <field name="workcenter_id" required="1"/>
                            <field name="matrix_type" string="Matrix Type"/>
                            <field name="price_matrix_id" string="Price Matrix" invisible="matrix_type != 'price'" required="matrix_type == 'price'"/>
                        </group>
                    </group>



                    <group string="Matrix Configuration" invisible="matrix_type == 'fixed' or not matrix_type">
                        <group string="X-Axis (Width) Field">
                            <field name="x_axis_field_type" string="X-Axis Field Type"/>
                            <field name="x_axis_calculated_field_id" string="X-Axis Calculated Field"
                                   invisible="x_axis_field_type != 'calculated'"
                                   required="matrix_type != 'fixed' and matrix_type and x_axis_field_type == 'calculated'"/>
                            <field name="x_axis_question_field_id" string="X-Axis Question Field"
                                   invisible="x_axis_field_type != 'question'"
                                   required="matrix_type != 'fixed' and matrix_type and x_axis_field_type == 'question'"/>
                        </group>
                        <group string="Y-Axis (Height) Field">
                            <field name="y_axis_field_type" string="Y-Axis Field Type"/>
                            <field name="y_axis_calculated_field_id" string="Y-Axis Calculated Field"
                                   invisible="y_axis_field_type != 'calculated'"
                                   required="matrix_type != 'fixed' and matrix_type and y_axis_field_type == 'calculated'"/>
                            <field name="y_axis_question_field_id" string="Y-Axis Question Field"
                                   invisible="y_axis_field_type != 'question'"
                                   required="matrix_type != 'fixed' and matrix_type and y_axis_field_type == 'question'"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Duration &amp; Cost" name="duration_cost">
                            <group>
                                <group string="Duration Configuration (for BOM Operations)">
                                    <field name="duration_minutes" string="Duration (minutes)" readonly="1"
                                           decoration-info="duration_formula"
                                           decoration-muted="not duration_formula"/>
                                    <field name="default_duration" string="Default Duration (minutes)"
                                           invisible="duration_formula"/>

                                    <field name="test_duration_result" string="Duration Test Result" readonly="1"
                                           decoration-success="test_duration_result > 0"/>
                                    <field name="duration_formula" string="Duration Formula"
                                           placeholder="e.g., get_fixed_price('RecMatlT')"
                                           help="Formula to calculate operation duration in minutes for BOM"/>
                                </group>
                                <group string="Cost Configuration (for Pricing)">
                                    <field name="cost_amount" string="Cost" readonly="1"
                                           decoration-info="cost_formula"
                                           decoration-muted="not cost_formula"/>
                                    <field name="test_cost_result" string="Cost Test Result" readonly="1"
                                           decoration-success="test_cost_result > 0"/>
                                    <field name="cost_formula" string="Cost Formula"
                                           placeholder="e.g., get_fixed_price('RecMatlT')*2*get_fixed_price('WageP')"
                                           help="Formula to calculate operation cost for pricing"/>
                                </group>
                                <group string="Test Values">
                                    <field name="test_height" string="Test Height (mm)"/>
                                    <field name="test_width" string="Test Width (mm)"/>
                                    <field name="test_labor_time" string="Matrix Value" readonly="1"
                                           decoration-info="test_labor_time > 0"
                                           widget="float" options="{'digits': [16, 3]}"/>
                                </group>
                            </group>
                        </page>





                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                                <field name="worksheet_content" widget="text"/>
                            </group>
                        </page>

                        <page string="Details" name="details">
                            <group>
                                <field name="description" widget="text"/>
                                <field name="notes" widget="text"/>
                            </group>
                        </page>

                        <page string="Formula Help" name="formula_help">
                            <div class="alert alert-info" role="alert">
                                <h4><i class="fa fa-info-circle me-2"></i>Cost Formula Help</h4>
                                <p class="mb-3"><strong>Leave the Cost Formula empty</strong> to use the default cost.
                                   <strong>Use field names</strong> from your configuration (e.g., door_width, door_height, area).</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">Fixed Price Table Functions</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><code>get_fixed_price('WageInP')</code> - Install Wage</li>
                                                    <li><code>get_fixed_price('RecMatlT')</code> - Receive Material Time</li>
                                                    <li><code>get_fixed_price('SetupT')</code> - Setup Time</li>
                                                    <li><code>get_fixed_price('FT')</code> - Fixed Time</li>
                                                    <li><code>get_fixed_price('MaterialH')</code> - Material Handling</li>
                                                </ul>
                                            </div>
                                        </div>



                                        <div class="card mt-3">
                                            <div class="card-header bg-danger text-white">
                                                <h6 class="mb-0">Price Matrix Functions</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><code>get_price()</code> - Use linked matrix with mapped X/Y fields</li>
                                                    <li><code>get_price('Matrix Name')</code> - Specific matrix</li>
                                                    <li><code>get_price('Matrix', height, width)</code> - Custom dimensions</li>
                                                    <li><code>get_price(None, test_height, test_width)</code> - Use test values</li>
                                                </ul>
                                                <small class="text-muted">
                                                    <i class="fa fa-lightbulb-o me-1"></i>
                                                    Price matrices work the same as labor matrices but return price values instead of time.
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0">Worker Calculation Functions</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><code>requires_multiple_workers(height, width)</code> - Boolean check</li>
                                                    <li><code>get_worker_multiplier(height, width)</code> - Returns 1 or 2</li>
                                                </ul>
                                                <small class="text-muted">Default threshold: 2500mm for multiple workers</small>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0">Math Functions</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><code>round(), ceil(), floor(), abs()</code></li>
                                                    <li><code>max(), min(), sum()</code></li>
                                                    <li><code>height, width, door_height, door_width, area</code></li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header bg-secondary text-white">
                                                <h6 class="mb-0">Configuration Access Functions</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><code>get_question(25)</code> - Get answer by question number</li>
                                                    <li><code>get_answer(field_id)</code> - Get value by field ID or name</li>
                                                    <li><code>get_option_quantity(25)</code> - Get quantity from selected option</li>
                                                </ul>
                                                <small class="text-muted">
                                                    <i class="fa fa-lightbulb-o me-1"></i>
                                                    Access configuration answers from questions to use in your cost calculations. Use get_option_quantity() for selection fields where options have different quantities.
                                                </small>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">Option Quantity Function</h6>
                                            </div>
                                            <div class="card-body">
                                                <p class="mb-2"><strong>get_option_quantity(question_number)</strong></p>
                                                <p class="mb-2">Gets the quantity value from the selected option in a selection field.</p>
                                                <div class="alert alert-info mb-2" role="alert">
                                                    <small>
                                                        <strong>Example:</strong> If question 25 is "Flush Bolts" and user selects "Top &amp; Bottom Flush Bolts" (quantity: 2),
                                                        then <code>get_option_quantity(25)</code> returns <code>2.0</code>
                                                    </small>
                                                </div>
                                                <ul class="list-unstyled mb-0">
                                                    <li><code>get_option_quantity(25)</code> - Returns 2.0 for "Top &amp; Bottom"</li>
                                                    <li><code>get_option_quantity(25)</code> - Returns 1.0 for "Top Only"</li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header bg-light text-dark">
                                                <h6 class="mb-0">Test Formula Functions</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><code>test_height, test_width</code> - Use test input values</li>
                                                    <li><code>get_price(None, test_height, test_width)</code> - Price using test values</li>
                                                    <li><code>get_labor_time('Matrix', test_height, test_width)</code> - Labor using test values</li>
                                                </ul>
                                                <small class="text-muted">
                                                    <i class="fa fa-lightbulb-o me-1"></i>
                                                    Use test_height and test_width in formulas to test with sample dimensions.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header bg-dark text-white">
                                        <h6 class="mb-0">Formula Examples</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Simple Examples:</h6>
                                                <ul class="list-unstyled">
                                                    <li><code>get_labor_time()</code></li>
                                                    <li><code>get_price()</code></li>
                                                    <li><code>get_labor_time() + 15</code></li>
                                                    <li><code>max(30, get_labor_time())</code></li>
                                                    <li><code>get_fixed_price('SetupT') + get_labor_time()</code></li>
                                                    <li><code>get_question(25) * get_fixed_price('CncHngT')</code></li>
                                                    <li><code>get_option_quantity(25) * 5.0</code></li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Advanced Examples:</h6>
                                                <ul class="list-unstyled">
                                                    <li><code>get_question(25) * get_fixed_price('CncHngT') * get_fixed_price('WageP')</code></li>
                                                    <li><code>max(get_question(36), 1) * get_price()</code></li>
                                                    <li><code>get_labor_time() * get_worker_multiplier(door_height, door_width)</code></li>
                                                    <li><code>get_fixed_price('RecMatlT') * 60 + get_labor_time()</code></li>
                                                    <li><code>(get_question(25) + get_question(36)) * get_fixed_price('SetupT')</code></li>
                                                    <li><code>(get_question(10) * get_question(11)) * get_option_quantity(25) * 0.5</code></li>
                                                    <li><code>get_option_quantity(25) * (10 if get_question(30) > 1000 else 5)</code></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Operation Template Tree View -->
    <record id="view_config_matrix_operation_template_tree" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.tree</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <list string="Operation Templates" default_order="sequence, name">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="config_template_id" string="Config Template"/>
                <field name="workcenter_id"/>
                <field name="category"/>
                <field name="matrix_type" string="Matrix"/>
                <field name="duration_minutes" string="Duration (min)"/>
                <field name="cost_amount" string="Cost"/>
                <field name="usage_count" string="Used"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Operation Template Search View -->
    <record id="view_config_matrix_operation_template_search" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.search</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <search string="Search Operation Templates">
                <field name="name" string="Name"/>
                <field name="workcenter_id" string="Work Center"/>
                <field name="category" string="Category"/>
                <field name="matrix_type" string="Matrix Type"/>

                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>

                <separator/>
                <filter string="Labor Matrix" name="labor_matrix" domain="[('matrix_type', '=', 'labor')]"/>
                <filter string="Price Matrix" name="price_matrix" domain="[('matrix_type', '=', 'price')]"/>

                <separator/>
                <filter string="Cutting" name="cutting" domain="[('category', '=', 'cutting')]"/>
                <filter string="Assembly" name="assembly" domain="[('category', '=', 'assembly')]"/>
                <filter string="Finishing" name="finishing" domain="[('category', '=', 'finishing')]"/>
                <filter string="Quality Control" name="quality" domain="[('category', '=', 'quality')]"/>
                <filter string="Packaging" name="packaging" domain="[('category', '=', 'packaging')]"/>

                <group expand="0" string="Group By">
                    <filter string="Template" name="matrix_template" context="{'group_by': 'config_template_id'}"/>
                    <filter string="Work Center" name="group_workcenter" context="{'group_by': 'workcenter_id'}"/>
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                    <filter string="Matrix Type" name="group_matrix_type" context="{'group_by': 'matrix_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Operation Template Kanban View -->
    <record id="view_config_matrix_operation_template_kanban" model="ir.ui.view">
        <field name="name">config.matrix.operation.template.kanban</field>
        <field name="model">config.matrix.operation.template</field>
        <field name="arch" type="xml">
            <kanban default_group_by="category" class="o_kanban_small_column">
                <field name="name"/>
                <field name="workcenter_id"/>
                <field name="category"/>
                <field name="matrix_type"/>
                <field name="duration_minutes"/>
                <field name="cost_amount"/>
                <field name="usage_count"/>
                <field name="active"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <div class="o_kanban_record_subtitle">
                                        <field name="workcenter_id"/>
                                    </div>
                                </div>
                                <div class="o_kanban_manage_button_section">
                                    <a class="o_kanban_manage_toggle_button" href="#">
                                        <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                    </a>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <div class="row">
                                    <div class="col-4">
                                        <span class="badge badge-pill badge-info">
                                            <t t-esc="record.duration_minutes.value"/> min
                                        </span>
                                    </div>
                                    <div class="col-4">
                                        <span class="badge badge-pill badge-success">
                                            $<t t-esc="record.cost_amount.value"/>
                                        </span>
                                    </div>
                                    <div class="col-4 text-right">
                                        <span class="badge badge-pill badge-secondary" t-if="record.usage_count.value > 0">
                                            Used <t t-esc="record.usage_count.value"/> times
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_manage_button_section o_kanban_manage_view">
                                <a type="edit" class="btn btn-primary btn-sm">Edit</a>
                                <a name="action_view_usage" type="object" class="btn btn-secondary btn-sm" t-if="record.usage_count.value > 0">
                                    View Usage
                                </a>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Operation Template Action -->
    <record id="action_config_matrix_operation_template" model="ir.actions.act_window">
        <field name="name">Operation Templates</field>
        <field name="res_model">config.matrix.operation.template</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_matrix_template': 1}</field>
        <field name="search_view_id" ref="view_config_matrix_operation_template_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first operation template!
            </p>
            <p>
                Operation templates define reusable manufacturing operations that can be applied to different products.
                Each template includes a work center, duration settings, and optional worksheets.
            </p>
        </field>
    </record>
</odoo>
