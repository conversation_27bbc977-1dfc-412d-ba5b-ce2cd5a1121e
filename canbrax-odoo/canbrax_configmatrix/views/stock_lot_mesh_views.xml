<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Stock Lot Mesh Extensions -->
    
    <!-- Extend Stock Lot Form View -->
    <record id="view_stock_lot_form_mesh" model="ir.ui.view">
        <field name="name">stock.lot.form.mesh</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='main_group']" position="after">
                <group string="Mesh Properties" name="mesh_properties" 
                       invisible="not product_id.is_mesh_product">
                    <group>
                        <field name="mesh_width"/>
                        <field name="mesh_height"/>
                        <field name="display_name_with_size" readonly="1"/>
                    </group>
                    <!-- <group>
                        <field name="is_master_sheet"/>
                        <field name="is_planned_offcut"/>
                        <field name="is_unplanned_offcut"/>
                        <field name="source_master_lot_id" 
                               invisible="not is_unplanned_offcut and not is_planned_offcut"/>
                    </group> -->
                </group>
            </xpath>
            
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_source_master" type="object" 
                        class="oe_stat_button" icon="fa-arrow-up"
                        invisible="not source_master_lot_id">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Source</span>
                        <span class="o_stat_text">Master</span>
                    </div>
                </button>
                <!-- <button name="action_view_related_offcuts" type="object" 
                        class="oe_stat_button" icon="fa-scissors"
                        invisible="not is_master_sheet">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Related</span>
                        <span class="o_stat_text">Off-cuts</span>
                    </div>
                </button> -->
            </xpath>
        </field>
    </record>
    
    <!-- Extend Stock Lot Tree View -->
    <record id="view_stock_lot_tree_mesh" model="ir.ui.view">
        <field name="name">stock.lot.tree.mesh</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="mesh_width" optional="hide"/>
                <field name="mesh_height" optional="hide"/>
                <field name="display_name_with_size" optional="show"/>
                <field name="is_master_sheet" optional="hide"/>
                <field name="is_unplanned_offcut" optional="hide"/>
            </xpath>
        </field>
    </record>
    
    <!-- Mesh Lot Search View - Standalone to avoid inheritance issues -->
    <record id="view_stock_lot_search_mesh" model="ir.ui.view">
        <field name="name">stock.lot.search.mesh</field>
        <field name="model">stock.lot</field>
        <field name="arch" type="xml">
            <search string="Search Mesh Lots">
                <field name="name"/>
                <field name="product_id"/>
                <field name="mesh_width"/>
                <field name="mesh_height"/>
                <field name="mesh_series"/>
                <field name="mesh_type"/>
                <field name="source_master_lot_id"/>
                <separator/>
                <filter string="Mesh Products" name="mesh_products"
                        domain="[('product_id.is_mesh_product', '=', True)]"/>
                <filter string="Master Sheets" name="master_sheets"
                        domain="[('is_master_sheet', '=', True)]"/>
                <filter string="Planned Off-cuts" name="planned_offcuts"
                        domain="[('is_planned_offcut', '=', True)]"/>
                <filter string="Unplanned Off-cuts" name="unplanned_offcuts"
                        domain="[('is_unplanned_offcut', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Product" name="group_product"
                            context="{'group_by': 'product_id'}"/>
                    <filter string="Mesh Type" name="group_mesh_type"
                            context="{'group_by': 'mesh_type'}"/>
                    <filter string="Mesh Series" name="group_mesh_series"
                            context="{'group_by': 'mesh_series'}"/>
                    <filter string="Source Master" name="group_source_master"
                            context="{'group_by': 'source_master_lot_id'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Mesh Lots by Dimensions Action -->
    <record id="action_mesh_lots_by_dimensions" model="ir.actions.act_window">
        <field name="name">Mesh Lots by Dimensions</field>
        <field name="res_model">stock.lot</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_stock_lot_search_mesh"/>
        <field name="domain">[('product_id.is_mesh_product', '=', True), ('mesh_width', '>', 0), ('mesh_height', '>', 0)]</field>
        <field name="context">{'search_default_mesh_products': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No mesh lots with dimensions found!
            </p>
            <p>
                This view shows mesh lots with their specific dimensions for inventory tracking.
            </p>
        </field>
    </record>
    
    <!-- Unplanned Off-cuts Action -->
    <record id="action_unplanned_mesh_offcuts" model="ir.actions.act_window">
        <field name="name">Unplanned Mesh Off-cuts</field>
        <field name="res_model">stock.lot</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_stock_lot_search_mesh"/>
        <field name="domain">[('is_unplanned_offcut', '=', True)]</field>
        <field name="context">{'search_default_unplanned_offcuts': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No unplanned off-cuts yet!
            </p>
            <p>
                Unplanned off-cuts are created when cutting operations produce usable waste material.
            </p>
        </field>
    </record>
    
    <!-- Master Sheets Action -->
    <record id="action_mesh_master_sheets" model="ir.actions.act_window">
        <field name="name">Mesh Master Sheets</field>
        <field name="res_model">stock.lot</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_stock_lot_search_mesh"/>
        <field name="domain">[('is_master_sheet', '=', True)]</field>
        <field name="context">{'search_default_master_sheets': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No master sheets found!
            </p>
            <p>
                Master sheets are full-size mesh sheets purchased from manufacturers.
            </p>
        </field>
    </record>
    
</odoo>
