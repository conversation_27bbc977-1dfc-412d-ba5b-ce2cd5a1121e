<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Field Operation Mapping Form View -->
    <record id="view_config_matrix_field_operation_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.field.operation.mapping.form</field>
        <field name="model">config.matrix.field.operation.mapping</field>
        <field name="arch" type="xml">
            <form string="Field Operation Mapping">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="operation_name"/></h1>
                        <h2><field name="field_name" readonly="1"/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="field_id" readonly="1"/>
                            <field name="workcenter_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="name"/>
                            <field name="matrix_id" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Operation Details" name="operation_details">
                            <group>
                                <group>
                                    <field name="duration_formula" widget="code" options="{'mode': 'python'}"
                                           help="Python expression to calculate operation cost/value"/>
                                    <div>
                                        <button name="action_open_operation_builder" type="object"
                                                string="Add Cost" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"
                                           help="Additional condition for this operation"/>
                                </group>
                                <group>
                                    <field name="setup_time"/>
                                    <field name="cleanup_time"/>
                                </group>
                            </group>
                        </page>

                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                            </group>
                            <group string="Worksheet Content" invisible="worksheet_type == 'pdf'">
                                <field name="worksheet_content" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <field name="notes" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Field Operation Mapping List View -->
    <record id="view_config_matrix_field_operation_mapping_list" model="ir.ui.view">
        <field name="name">config.matrix.field.operation.mapping.list</field>
        <field name="model">config.matrix.field.operation.mapping</field>
        <field name="arch" type="xml">
            <list string="Field Operation Mappings" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="operation_name" string="Operation"/>
                <field name="workcenter_id" string="Work Center"/>
                <field name="duration_formula" string="Duration"/>
                <field name="condition" string="Condition"/>
                <!-- Hidden fields for functionality -->
                <field name="field_id" column_invisible="1"/>
                <!-- Action buttons -->
                <button name="action_open_operation_builder" type="object"
                        string="Add Duration" icon="fa-magic"
                        class="btn-link text-primary" title="Build duration formula"/>
            </list>
        </field>
    </record>

    <!-- Option Operation Mapping Form View -->
    <record id="view_config_matrix_option_operation_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.option.operation.mapping.form</field>
        <field name="model">config.matrix.option.operation.mapping</field>
        <field name="arch" type="xml">
            <form string="Option Operation Mapping">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="operation_name"/></h1>
                        <h2><field name="option_name" readonly="1"/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="option_id" readonly="1"/>
                            <field name="workcenter_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="name"/>
                            <field name="matrix_id" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Operation Details" name="operation_details">
                            <group>
                                <group>
                                    <field name="duration_formula" widget="code" options="{'mode': 'python'}"
                                           help="Python expression to calculate operation cost/value"/>
                                    <div>
                                        <button name="action_open_operation_builder" type="object"
                                                string="Add Cost" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"
                                           help="Additional condition for this operation"/>
                                </group>
                                <group>
                                    <field name="setup_time"/>
                                    <field name="cleanup_time"/>
                                </group>
                            </group>
                        </page>

                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                            </group>
                            <group string="Worksheet Content" invisible="worksheet_type == 'pdf'">
                                <field name="worksheet_content" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <field name="notes" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Option Operation Mapping List View (for use in one2many fields) -->
    <record id="view_config_matrix_option_operation_mapping_list" model="ir.ui.view">
        <field name="name">config.matrix.option.operation.mapping.list</field>
        <field name="model">config.matrix.option.operation.mapping</field>
        <field name="arch" type="xml">
            <list string="Operation Mappings" create="false" edit="false" delete="true">
                <field name="sequence" widget="handle"/>
                <field name="operation_name"/>
                <field name="workcenter_id"/>
                <field name="duration_formula"/>
                <field name="condition"/>
                <!-- Action buttons -->
                <button name="action_open_operation_builder" type="object"
                        string="Add Cost" icon="fa-magic"
                        class="btn-link text-primary" title="Build cost formula"/>
            </list>
        </field>
    </record>

    <!-- Option Operation Management Form View -->
    <record id="view_config_matrix_option_operation_management_form" model="ir.ui.view">
        <field name="name">config.matrix.option.operation.management.form</field>
        <field name="model">config.matrix.option</field>
        <field name="arch" type="xml">
            <form string="Manage Operation Mappings">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="name" readonly="1"/></h1>
                        <h2>Operation Mappings</h2>
                    </div>

                    <!-- Operation Mappings -->
                    <div class="alert alert-info mb-3" role="alert">
                        <h5><i class="fa fa-cogs me-2"></i>Operation Mappings</h5>
                        <p class="mb-0">Configure which manufacturing operations should be included in the routing when this option is selected. You can map multiple operations with different work centers and duration calculations.</p>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Current Operation Mappings</h6>
                        <button name="action_open_operation_builder" type="object"
                                string="Add Operation" class="btn btn-primary"
                                icon="fa-plus"/>
                    </div>

                    <field name="operation_mapping_ids" nolabel="1">
                        <list string="Operation Mappings" create="false" edit="false" delete="true">
                            <field name="sequence" widget="handle"/>
                            <field name="operation_name"/>
                            <field name="workcenter_id"/>
                            <field name="duration_formula"/>
                            <field name="condition"/>
                            <!-- Action buttons -->
                            <button name="action_open_operation_builder" type="object"
                                    string="Add Duration" icon="fa-magic"
                                    class="btn-link text-primary" title="Build duration formula"/>
                        </list>
                    </field>

                    <div class="mt-3" invisible="operation_mapping_ids">
                        <div class="text-center text-muted py-4">
                            <i class="fa fa-info-circle fa-2x mb-2"></i>
                            <p>No operation mappings configured yet.</p>
                            <p class="small">Click "Add Operation" above to add operations.</p>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Custom Action for Option Operation Mappings with Wizard Create -->
    <record id="action_config_matrix_option_operation_mapping_with_wizard" model="ir.actions.act_window">
        <field name="name">Manage Operation Mappings</field>
        <field name="res_model">config.matrix.option.operation.mapping</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_config_matrix_option_operation_mapping_list"/>
        <field name="context">{
            'create': True,
            'search_default_option_id': active_id,
        }</field>
        <field name="domain">[('option_id', '=', active_id)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add an operation mapping
            </p>
            <p>
                Click "New" to add an operation from a template.
            </p>
        </field>
    </record>

    <!-- Override the default create action to open wizard -->
    <record id="action_create_operation_mapping_wizard" model="ir.actions.act_window">
        <field name="name">Build Operation Mapping</field>
        <field name="res_model">config.matrix.operation.builder</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_option_id': active_id,
        }</field>
    </record>

    <!-- Option Operation Mapping Action with Custom Create Button -->
    <record id="action_config_matrix_option_operation_mapping_with_add" model="ir.actions.act_window">
        <field name="name">Manage Operation Mappings</field>
        <field name="res_model">config.matrix.option.operation.mapping</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_config_matrix_option_operation_mapping_list"/>
        <field name="context">{
            'create': False,
            'search_default_option_id': active_id,
        }</field>
        <field name="domain">[('option_id', '=', active_id)]</field>
    </record>

    <!-- Unified Operation Mapping Tree View -->
    <record id="view_config_matrix_operation_mapping_tree" model="ir.ui.view">
        <field name="name">config.matrix.operation.mapping.tree</field>
        <field name="model">config.matrix.operation.mapping</field>
        <field name="arch" type="xml">
            <list string="Operation Mappings">
                <field name="sequence" widget="handle"/>
                <field name="target_model"/>
                <field name="target_name"/>
                <field name="operation_name"/>
                <field name="workcenter_id"/>
                <field name="duration_formula"/>
                <field name="condition"/>
            </list>
        </field>
    </record>

    <!-- Unified Operation Mapping Form View -->
    <record id="view_config_matrix_operation_mapping_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.mapping.form</field>
        <field name="model">config.matrix.operation.mapping</field>
        <field name="arch" type="xml">
            <form string="Operation Mapping">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="operation_name"/></h1>
                        <h2><field name="target_name" readonly="1"/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="target_model"/>
                            <field name="target_id"/>
                            <field name="workcenter_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="name"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Operation Details" name="operation_details">
                            <group>
                                <group>
                                    <field name="duration_formula" widget="code" options="{'mode': 'python'}"/>
                                    <div>
                                        <button name="action_open_operation_builder" type="object"
                                                string="Add Duration" class="btn-primary"
                                                icon="fa-magic"/>
                                    </div>
                                    <field name="condition" widget="code" options="{'mode': 'python'}"/>
                                </group>
                                <group>
                                    <field name="setup_time"/>
                                    <field name="cleanup_time"/>
                                </group>
                            </group>
                        </page>

                        <page string="Worksheet" name="worksheet">
                            <group>
                                <field name="worksheet_type"/>
                            </group>
                            <group string="Worksheet Content" invisible="worksheet_type == 'pdf'">
                                <field name="worksheet_content" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <field name="notes" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Unified Operation Mapping Action -->
    <record id="action_config_matrix_operation_mapping" model="ir.actions.act_window">
        <field name="name">All Operation Mappings</field>
        <field name="res_model">config.matrix.operation.mapping</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': ['target_model']}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No operation mappings found
            </p>
            <p>
                This view shows all operation mappings across all configuration templates.
                Operation mappings define which manufacturing operations are triggered when specific fields or options are selected.
                For easier management, edit operations directly from the field/option forms or use Operation Templates.
            </p>
        </field>
    </record>

    <!-- Menu Item for Operation Mappings - REMOVED as requested -->
    <!-- <menuitem id="menu_config_matrix_operation_mapping"
              name="All Operation Mappings"
              parent="menu_config_matrix_configurations_root"
              action="action_config_matrix_operation_mapping"
              sequence="15"/> -->

</odoo>
