<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Cut Matrix Views -->
    
    <!-- Tree View -->
    <record id="view_mesh_cut_matrix_tree" model="ir.ui.view">
        <field name="name">mesh.cut.matrix.tree</field>
        <field name="model">mesh.cut.matrix</field>
        <field name="arch" type="xml">
            <list string="Mesh Cut Matrices">
                <field name="name"/>
                <field name="mesh_series"/>
                <field name="plan_count"/>
                <field name="active"/>
            </list>
        </field>
    </record>
    
    <!-- Form View -->
    <record id="view_mesh_cut_matrix_form" model="ir.ui.view">
        <field name="name">mesh.cut.matrix.form</field>
        <field name="model">mesh.cut.matrix</field>
        <field name="arch" type="xml">
            <form string="Mesh Cut Matrix">

                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_cut_plans" type="object"
                                class="oe_stat_button" icon="fa-scissors">
                            <field name="plan_count" widget="statinfo" string="Cut Plans"/>
                        </button>
                        <div class="oe_stat_button" style="border: 1px solid #ddd; padding: 8px; border-radius: 4px; margin-right: 4px;margin-left: 4px;">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="assigned_cells_count"/></span>
                                <span class="o_stat_text">Assigned Cells</span>
                            </div>
                        </div>
                        <div class="oe_stat_button" style="border: 1px solid #ddd; padding: 8px; border-radius: 4px;">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="total_cells_count"/></span>
                                <span class="o_stat_text">Total Cells</span>
                            </div>
                        </div>
                    </div>

                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger"
                            invisible="active"/>

                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Matrix Name"/>
                        </h1>
                    </div>

                    <group>
                        <group>
                            <field name="mesh_series"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="plan_count" readonly="1"/>
                            <field name="assigned_cells_count" readonly="1"/>
                            <field name="total_cells_count" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Visual Matrix" name="visual_matrix">
                            <field name="matrix_data" nolabel="1" widget="cut_matrix"/>
                        </page>

                        <page string="Configuration" name="configuration">
                            <group>
                                <group string="Orientation &amp; Dimensions">
                                    <field name="orientation_methodology"/>

                                    <!-- Dynamic labels for height/width values -->
                                    <label for="height_values" string="Height Values (mm)"
                                           invisible="orientation_methodology == 'largest_smallest'"/>
                                    <label for="height_values" string="Largest Values (mm)"
                                           invisible="orientation_methodology != 'largest_smallest'"/>
                                    <field name="height_values" widget="text" nolabel="1"
                                           placeholder="600,700,800,900,1000,1100,1200"
                                           help="Comma-separated values - these become the ROWS in the matrix"/>

                                    <label for="width_values" string="Width Values (mm)"
                                           invisible="orientation_methodology == 'largest_smallest'"/>
                                    <label for="width_values" string="Smallest Values (mm)"
                                           invisible="orientation_methodology != 'largest_smallest'"/>
                                    <field name="width_values" widget="text" nolabel="1"
                                           placeholder="325,375,425,475,525,575,625"
                                           help="Comma-separated values - these become the COLUMNS in the matrix"/>
                                </group>
                                <group string="Matrix Information">
                                    <!-- Orientation Methodology Display -->
                                    <div class="alert alert-success">
                                        <strong>Matrix Orientation:</strong><br/>
                                        <span invisible="orientation_methodology != 'height_width'">
                                            <i class="fa fa-arrows-v"></i> Height × Width (H×W)<br/>
                                            <small>Matrix Header: H\W | Traditional height/width format</small>
                                        </span>
                                        <span invisible="orientation_methodology != 'largest_smallest'">
                                            <i class="fa fa-expand"></i> Largest × Smallest (L×S)<br/>
                                            <small>Matrix Header: L\S | Portrait-first format (largest dimension first)</small>
                                        </span>
                                    </div>

                                    <field name="matrix_data" widget="text"
                                           placeholder="JSON data for matrix cells"/>
                                </group>
                            </group>
                        </page>



                        <page string="Cell Assignments" name="cell_assignments">
                            <div class="alert alert-success mb-3">
                                <strong>✅ Primary Data Storage:</strong> Cell assignments are now the primary way to store matrix data.
                                Changes made in the Visual Matrix tab automatically sync here, and vice versa.
                                Double-click any row to edit, or click "Add a line" to create new assignments.
                            </div>

                            <field name="cell_assignment_ids" nolabel="1">
                                <list string="Cell Assignments" decoration-success="has_assignment" decoration-muted="not has_assignment">
                                    <field name="cell_reference"/>
                                    <field name="height"/>
                                    <field name="width"/>
                                    <field name="primary_cut_plan_id" domain="[('matrix_id', '=', parent.id)]"/>
                                    <field name="secondary_cut_plan_id" domain="[('matrix_id', '=', parent.id), ('id', '!=', primary_cut_plan_id)]"/>
                                    <field name="arrow_direction"/>
                                    <field name="has_assignment" invisible="1"/>
                                </list>
                            </field>
                        </page>

                        <page string="Cut Plans" name="cut_plans">
                            <field name="cut_plan_ids" mode="list">
                                <list string="Cut Plans">
                                    <field name="name"/>
                                    <field name="cut_width"/>
                                    <field name="cut_height"/>
                                    <field name="byproduct_count"/>
                                </list>
                            </field>
                        </page>


                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Search View -->
    <record id="view_mesh_cut_matrix_search" model="ir.ui.view">
        <field name="name">mesh.cut.matrix.search</field>
        <field name="model">mesh.cut.matrix</field>
        <field name="arch" type="xml">
            <search string="Search Mesh Cut Matrices">
                <field name="name"/>
                <field name="mesh_series"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Saltwater Series" name="saltwater" domain="[('mesh_series', '=', 'saltwaterseries')]"/>
                <filter string="Diamond Grill" name="diamond" domain="[('mesh_series', '=', 'diamond')]"/>
                <filter string="Fly Screen" name="flyscreen" domain="[('mesh_series', '=', 'flyscreen')]"/>
                <group expand="0" string="Group By">
                    <filter string="Mesh Series" name="group_mesh_series" context="{'group_by': 'mesh_series'}"/>
                    <filter string="Active" name="group_active" context="{'group_by': 'active'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Action -->
    <record id="action_mesh_cut_matrix" model="ir.actions.act_window">
        <field name="name">Mesh Cut Matrices</field>
        <field name="res_model">mesh.cut.matrix</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_mesh_cut_matrix_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first mesh cutting matrix!
            </p>
            <p>
                Mesh cutting matrices define how master sheets can be cut to create
                specific sizes and what byproducts are generated.
            </p>
        </field>
    </record>

    <!-- Mesh Cut Matrix Visual Editor Form View -->
    <record id="view_mesh_cut_matrix_visual_editor_form" model="ir.ui.view">
        <field name="name">mesh.cut.matrix.visual.editor.form</field>
        <field name="model">mesh.cut.matrix.visual.editor</field>
        <field name="arch" type="xml">
            <form string="Mesh Cut Matrix Visual Editor">
                <header>
                    <button name="action_save_matrix" string="Save Matrix"
                            type="object" class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Editor Name"/></h1>
                    </div>

                    <group>
                        <group>
                            <field name="matrix_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="height_values" widget="text"
                                   placeholder="600,700,800,900,1000"/>
                            <field name="width_values" widget="text"
                                   placeholder="325,375,425,475,525"/>
                        </group>
                    </group>

                    <div class="alert alert-success" role="alert">
                        <h5><i class="fa fa-lightbulb-o"></i> How to Use the Visual Editor</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul>
                                    <li><strong>Click any cell</strong> to edit its properties</li>
                                    <li><strong>Right Arrow (→):</strong> Cut horizontally first, then follow arrow</li>
                                    <li><strong>Down Arrow (↓):</strong> Cut vertically first, then follow arrow</li>
                                    <li><strong>Master Sheet:</strong> Mark cells as master sheet sizes</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul>
                                    <li><strong>Cut Template:</strong> Reference to cutting instructions</li>
                                    <li><strong>Target Size:</strong> Where the arrow points to</li>
                                    <li><strong>Toolbar buttons:</strong> Quick actions for selected cell</li>
                                    <li><strong>Save Matrix:</strong> Apply changes to the main matrix</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Interactive Matrix Editor -->
                    <field name="matrix_html" nolabel="1" widget="html"/>

                    <!-- Raw JSON Data (for advanced users) -->
                    <group string="Raw Matrix Data (Advanced)">
                        <field name="matrix_data" widget="text"
                               placeholder="JSON data will be updated automatically"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Mesh Cut Plan Views -->
    
    <!-- Tree View -->
    <record id="view_mesh_cut_plan_tree" model="ir.ui.view">
        <field name="name">mesh.cut.plan.tree</field>
        <field name="model">mesh.cut.plan</field>
        <field name="arch" type="xml">
            <list string="Mesh Cut Plans">
                <field name="name"/>
                <field name="mesh_series"/>
                <field name="master_product_id"/>
                <field name="matrix_id"/>
                <field name="byproduct_count"/>
                <field name="assigned_cells_count"/>
            </list>
        </field>
    </record>
    
    <!-- Form View -->
    <record id="view_mesh_cut_plan_form" model="ir.ui.view">
        <field name="name">mesh.cut.plan.form</field>
        <field name="model">mesh.cut.plan</field>
        <field name="arch" type="xml">
            <form string="Mesh Cut Plan">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_byproducts" type="object"
                                class="oe_stat_button" icon="fa-cubes">
                            <field name="byproduct_count" widget="statinfo" string="Byproducts"/>
                        </button>

                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" string="Cut Matrix"/>
                        </h1>
                    </div>

                    <group>
                        <group>
                            <field name="matrix_id"/>
                            <field name="mesh_series" string="Mesh Series"/>
                            <field name="master_product_id"
                                   domain="[('is_mesh_product', '=', True), ('mesh_series_computed', '=', mesh_series)]"
                                   options="{'no_create': True, 'no_create_edit': True}"/>
                        </group>
                        <group>
                            <field name="cut_width" string="Cut Width (mm)"/>
                            <field name="cut_height" string="Cut Height (mm)"/>
                            <field name="byproduct_count" readonly="1"/>
                        </group>
                    </group>

                    <group string="Matrix Cell Assignments">
                        <field name="assigned_cells_count" readonly="1"/>
                        <field name="assigned_cells_display" readonly="1" widget="html" nolabel="1"/>
                    </group>
                    
                    <notebook>
                        <page string="Byproducts" name="byproducts">
                            <field name="byproduct_ids" mode="list">
                                <list string="Planned Byproducts" editable="bottom">
                                    <field name="product_id" domain="[('is_mesh_product', '=', True)]" width="400px"/>
                                    <field name="cut_seq" string="Cut Seq" width="80px"/>
                                    <field name="cut_value" string="Cut Value (mm)" width="100px"/>
                                    <field name="cut_orientation" string="Cut Orientation" width="120px"/>
                                    <field name="quantity" string="Qty" width="60px"/>
                                </list>
                            </field>
                        </page>
                        
                        <page string="Cut Instructions" name="instructions">
                            <group>
                                <field name="cut_instructions" widget="text" 
                                       placeholder="Enter cutting instructions for operators..."/>
                            </group>
                        </page>
                        
                        <page string="Diagram" name="diagram">
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Cutting Layout Diagram</h5>
                                        </div>
                                        <div class="card-body" style="padding: 20px;">
                                            <group>
                                                <group>
                                                    <field name="diagram_pdf_filename" invisible="1"/>
                                                    <field name="diagram_pdf" filename="diagram_pdf_filename"
                                                           widget="pdf_viewer"
                                                           options="{'preview_image': '/web/static/src/img/placeholder.png'}"
                                                           help="Upload a PDF diagram showing the cutting layout for this plan"/>
                                                </group>
                                            </group>

                                            <div class="alert alert-info mt-3" role="alert">
                                                <strong>PDF Diagram Instructions:</strong><br/>
                                                • Upload a PDF file showing the cutting layout for this master sheet size<br/>
                                                • Include dimensions, cut lines, and byproduct areas<br/>
                                                • The diagram will be displayed to operators during cutting operations
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </page>


                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Action -->
    <record id="action_mesh_cut_plan" model="ir.actions.act_window">
        <field name="name">Mesh Cut Plans</field>
        <field name="res_model">mesh.cut.plan</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No cut plans defined yet!
            </p>
            <p>
                Cut plans define how master sheets are cut and what byproducts are created.
            </p>
        </field>
    </record>

    <!-- Large Dialog Action for Cut Plans -->
    <record id="action_mesh_cut_plan_large" model="ir.actions.act_window">
        <field name="name">Cut Plans</field>
        <field name="res_model">mesh.cut.plan</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'dialog_size': 'large'}</field>
    </record>
    
    <!-- Mesh Cut Byproduct Views -->
    
    <!-- Tree View -->
    <record id="view_mesh_cut_byproduct_tree" model="ir.ui.view">
        <field name="name">mesh.cut.byproduct.tree</field>
        <field name="model">mesh.cut.byproduct</field>
        <field name="arch" type="xml">
            <list string="Mesh Cut Byproducts">
                <field name="cut_plan_id"/>
                <field name="mesh_type_filter"/>
                <field name="product_id"/>
                <field name="quantity"/>
                <field name="cut_seq"/>
                <field name="cut_orientation"/>
            </list>
        </field>
    </record>
    
    <!-- Form View -->
    <record id="view_mesh_cut_byproduct_form" model="ir.ui.view">
        <field name="name">mesh.cut.byproduct.form</field>
        <field name="model">mesh.cut.byproduct</field>
        <field name="arch" type="xml">
            <form string="Mesh Cut Byproduct">
                <sheet>
                    <group>
                        <group>
                            <field name="cut_plan_id"/>
                            <field name="mesh_type_filter"/>
                            <field name="product_id"/>
                        </group>
                        <group>
                            <field name="quantity"/>
                            <field name="cut_seq"/>
                            <field name="cut_orientation"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Action -->
    <record id="action_mesh_cut_byproduct" model="ir.actions.act_window">
        <field name="name">Mesh Cut Byproducts</field>
        <field name="res_model">mesh.cut.byproduct</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No byproducts defined yet!
            </p>
            <p>
                Byproducts are the planned off-cuts created when cutting master sheets.
            </p>
        </field>
    </record>
    
</odoo>
