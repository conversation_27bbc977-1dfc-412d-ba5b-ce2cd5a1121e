<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Operation Price Tree View -->
    <record id="view_config_matrix_operation_price_tree" model="ir.ui.view">
        <field name="name">config.matrix.operation.price.tree</field>
        <field name="model">config.matrix.operation.price</field>
        <field name="arch" type="xml">
            <list string="Fixed Price Table" default_order="sequence,description" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="type" string="Type"/>
                <field name="code" string="Code"/>
                <field name="description" string="Description"/>
                <field name="sub_description" string="Sub-Description"/>
                <field name="value_cost" string="Value / Cost" widget="float" options="{'digits': [16, 3]}"/>
                <field name="work_centre_id" string="Work Centre"/>
                <!-- Legacy fields (hidden by default) -->
                <field name="work_centre" string="Work Centre Code" column_invisible="1"/>
                <field name="unit_type" string="Unit" column_invisible="1"/>
                <field name="cost_price" string="Cost" widget="monetary" options="{'currency_field': 'currency_id'}" column_invisible="1"/>
                <field name="sale_price" string="Sale Price" widget="monetary" options="{'currency_field': 'currency_id'}" column_invisible="1"/>
                <field name="markup_percentage" string="Markup %" widget="percentage" column_invisible="1"/>
                <field name="category" string="Category" column_invisible="1"/>
                <field name="usage_count" string="Usage" column_invisible="1"/>
                <field name="active" invisible="1"/>
                <field name="currency_id" invisible="1"/>
                <!-- Action buttons -->
                <button name="open_record" type="object"
                        icon="fa-edit" title="Edit this record"
                        class="btn-link text-primary"/>
                <button name="unlink" type="object" icon="fa-trash"
                        title="Delete this record"
                        class="btn-link text-danger"
                        confirm="Are you sure you want to delete this fixed price entry?"/>
            </list>
        </field>
    </record>

    <!-- Operation Price Form View -->
    <record id="view_config_matrix_operation_price_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.price.form</field>
        <field name="model">config.matrix.operation.price</field>
        <field name="arch" type="xml">
            <form string="Operation Price">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="description" placeholder="Operation Description"/></h1>
                        <h2><field name="sub_description" placeholder="Additional details..."/></h2>
                    </div>

                    <group>
                        <group>
                            <field name="sequence"/>
                            <field name="type"/>
                            <field name="code"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="value_cost" widget="float" options="{'digits': [16, 3]}"/>
                            <field name="work_centre_id"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="usage_count" readonly="1"/>
                        </group>
                    </group>

                    <group string="Legacy Pricing Information" invisible="1">
                        <group>
                            <field name="category"/>
                            <field name="unit_type"/>
                            <field name="work_centre"/>
                            <field name="cost_price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="sale_price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        </group>
                        <group>
                            <field name="markup_percentage" readonly="1" widget="percentage"/>
                            <field name="profit_margin" readonly="1" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        </group>
                    </group>

                    <group string="Work Centers">
                        <field name="workcenter_ids" widget="many2many_tags" nolabel="1"/>
                    </group>

                    <group string="Notes">
                        <field name="notes" nolabel="1" widget="text"/>
                    </group>

                    <!-- Hidden fields -->
                    <field name="currency_id" invisible="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Operation Price Search View -->
    <record id="view_config_matrix_operation_price_search" model="ir.ui.view">
        <field name="name">config.matrix.operation.price.search</field>
        <field name="model">config.matrix.operation.price</field>
        <field name="arch" type="xml">
            <search string="Search Operation Prices">
                <field name="code"/>
                <field name="description"/>
                <field name="sub_description"/>
                <field name="type"/>
                <field name="work_centre_id"/>
                <field name="work_centre"/>
                <field name="category"/>
                <field name="workcenter_ids"/>
                <separator/>
                <filter string="Fixed Time" name="fixed_time" domain="[('type', '=', 'Fixed Time')]"/>
                <filter string="Labour" name="labour" domain="[('type', '=', 'Labour')]"/>
                <filter string="Material" name="material" domain="[('type', '=', 'Material')]"/>
                <filter string="Setup" name="setup" domain="[('type', '=', 'Setup')]"/>
                <filter string="Finishing" name="finishing" domain="[('type', '=', 'Finishing')]"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="High Usage" name="high_usage" domain="[('usage_count', '>', 5)]"/>
                <filter string="Unused" name="unused" domain="[('usage_count', '=', 0)]"/>
                <group expand="0" string="Group By">
                    <filter string="Type" name="group_type" context="{'group_by': 'type'}"/>
                    <filter string="Work Centre" name="group_work_centre_id" context="{'group_by': 'work_centre_id'}"/>
                    <filter string="Work Centre Code" name="group_work_centre" context="{'group_by': 'work_centre'}"/>
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                    <filter string="Unit Type" name="group_unit_type" context="{'group_by': 'unit_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action moved to menu_views.xml to ensure proper loading order -->

</odoo>
