<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- CSV Import Wizard Form View -->
    <record id="view_config_matrix_field_csv_import_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.field.csv.import.wizard.form</field>
        <field name="model">config.matrix.field.csv.import.wizard</field>
        <field name="arch" type="xml">
            <form string="Import CSV Options">
                <sheet>
                    <div class="oe_title">
                        <h1>Import Options from CSV</h1>
                        <p class="text-muted">
                            Import options and component mappings for field: <strong><field name="field_id" readonly="1" force_save="1"/></strong>
                        </p>
                    </div>

                    <group>
                        <group>
                            <field name="csv_file" widget="binary" filename="csv_filename"/>
                            <field name="csv_filename" invisible="1"/>
                        </group>
                        <group>
                            <field name="clear_existing"/>
                        </group>
                    </group>

                    <group string="CSV Preview" invisible="not preview_data">
                        <field name="preview_data" widget="text" readonly="1" nolabel="1"/>
                    </group>

                    <div class="alert alert-info mt-3" role="alert">
                        <h5><i class="fa fa-info-circle me-2"></i>CSV Format Requirements</h5>
                        <ul class="mb-0">
                            <li><strong>Field Label:</strong> The label for the field (used only for reference)</li>
                            <li><strong>Options/Option Text:</strong> Display text for the option</li>
                            <li><strong>Options/Option Value:</strong> Internal value for the option</li>
                            <li><strong>Options/Component Mappings/Mapping Type:</strong> "Static Product" or "Dynamic Field Match"</li>
                            <li><strong>Options/Component Mappings/Component Product:</strong> Name of the product to map</li>
                            <li><strong>Options/Component Mappings/Quantity Formula:</strong> Formula to calculate quantity</li>
                        </ul>
                    </div>
                </sheet>
                <footer>
                    <button name="action_import" string="Import" type="object" class="btn-primary"/>
                    <button name="action_download_template" string="Download Template" type="object" class="btn-secondary"/>
                    <button special="cancel" string="Cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
