# -*- coding: utf-8 -*-

from odoo import models, api
import logging
import re
import json

_logger = logging.getLogger(__name__)


class ConfigMatrixFieldNormalizer(models.AbstractModel):
    """
    Field Value Normalization Service
    
    Provides centralized field value normalization to ensure consistent data types
    between UI and backend template calculations.
    """
    _name = 'config.matrix.field.normalizer'
    _description = 'Field Value Normalization Service'

    @api.model
    def normalize_field_values(self, field_values, template_id, context_info=None):
        """
        Normalize field values for template calculations
        
        Args:
            field_values (dict): Raw field values from UI or backend
            template_id (int): Template ID for field type lookup
            context_info (dict): Context information (UI vs backend, source, etc.)
            
        Returns:
            dict: Normalized field values with consistent data types
        """
        if not field_values:
            return {}
            
        if not template_id:
            _logger.warning("[NORMALIZATION] No template_id provided, returning original values")
            return field_values.copy()
            
        context_info = context_info or {}
        source = context_info.get('source', 'unknown')
        
        _logger.info(f"[NORMALIZATION] Starting normalization for template {template_id}")
        _logger.info(f"[NORMALIZATION] Input: {len(field_values)} fields, source: {source}")
        
        try:
            # Get field type mapping from template
            field_type_mapping = self._get_field_type_mapping(template_id)
            _logger.info(f"[NORMALIZATION] Found {len(field_type_mapping)} field type mappings")
            
            # Normalize field values
            normalized_values = {}
            conversion_summary = {}
            
            for field_key, raw_value in field_values.items():
                try:
                    # Skip calculated fields - they're already processed
                    if field_key.startswith('_CALCULATED_'):
                        normalized_values[field_key] = raw_value
                        continue
                    
                    # Get field type
                    field_type = field_type_mapping.get(field_key, 'text')  # Default to text
                    
                    # Normalize value based on type
                    normalized_value = self._normalize_single_value(raw_value, field_type, field_key)
                    normalized_values[field_key] = normalized_value
                    
                    # Track conversions for debugging
                    if raw_value != normalized_value:
                        conversion_summary[field_key] = {
                            'from': raw_value,
                            'to': normalized_value,
                            'type': field_type
                        }
                        
                except Exception as e:
                    _logger.warning(f"[NORMALIZATION] Error normalizing field {field_key}: {e}")
                    # Fallback: keep original value
                    normalized_values[field_key] = raw_value
            
            _logger.info(f"[NORMALIZATION] Output: {len(normalized_values)} normalized fields")
            if conversion_summary:
                _logger.info(f"[NORMALIZATION] Conversions: {len(conversion_summary)} fields converted")
                # Log first few conversions for debugging
                for field_key, conversion in list(conversion_summary.items())[:5]:
                    _logger.info(f"[NORMALIZATION] {field_key}: {conversion['from']} -> {conversion['to']} ({conversion['type']})")
            
            return normalized_values
            
        except Exception as e:
            _logger.error(f"[NORMALIZATION] Error during normalization: {e}")
            _logger.error(f"[NORMALIZATION] Returning original field values as fallback")
            return field_values.copy()

    def _get_field_type_mapping(self, template_id):
        """
        Get field type mapping for normalization
        
        Args:
            template_id (int): Template ID
            
        Returns:
            dict: Mapping of field keys to field types
        """
        try:
            template = self.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                _logger.warning(f"[NORMALIZATION] Template {template_id} not found")
                return {}
            
            field_mapping = {}
            
            for section in template.section_ids:
                for field in section.field_ids:
                    # Map both field ID and technical name to field type
                    field_mapping[str(field.id)] = field.field_type
                    if field.technical_name:
                        field_mapping[field.technical_name] = field.field_type
            
            return field_mapping
            
        except Exception as e:
            _logger.error(f"[NORMALIZATION] Error getting field type mapping: {e}")
            return {}

    def _normalize_single_value(self, value, field_type, field_key):
        """
        Normalize a single field value based on its type
        
        Args:
            value: Raw value to normalize
            field_type (str): Expected field type
            field_key (str): Field key for debugging
            
        Returns:
            Normalized value
        """
        # Handle None and empty values
        if value is None:
            return self._get_default_value(field_type)
        
        # Handle empty strings
        if isinstance(value, str) and value.strip() == '':
            return self._get_default_value(field_type)
        
        try:
            if field_type == 'integer':
                return self._normalize_integer(value)
            elif field_type == 'float':
                return self._normalize_float(value)
            elif field_type == 'boolean':
                return self._normalize_boolean(value)
            elif field_type == 'selection':
                return self._normalize_selection(value)
            elif field_type in ['text', 'char']:
                return self._normalize_text(value)
            else:
                # Unknown type, try to preserve as-is
                return value
                
        except Exception as e:
            _logger.warning(f"[NORMALIZATION] Error normalizing {field_key} ({field_type}): {e}")
            return self._get_default_value(field_type)

    def _normalize_integer(self, value):
        """Normalize value to integer"""
        if isinstance(value, int):
            return value
        if isinstance(value, float):
            return int(value)
        if isinstance(value, str):
            # Try direct conversion
            if value.isdigit():
                return int(value)
            # Try to extract number from string like "3 per door"
            match = re.search(r'^(\d+)', value.strip())
            if match:
                return int(match.group(1))
        return 0

    def _normalize_float(self, value):
        """Normalize value to float"""
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            # Try direct conversion
            try:
                return float(value)
            except ValueError:
                # Try to extract number from string
                match = re.search(r'^(\d+\.?\d*)', value.strip())
                if match:
                    return float(match.group(1))
        return 0.0

    def _normalize_boolean(self, value):
        """Normalize value to boolean"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ['true', '1', 'yes', 'on', 'checked']
        if isinstance(value, (int, float)):
            return bool(value)
        return False

    def _normalize_selection(self, value):
        """Normalize selection field value"""
        # For selection fields, we typically want to preserve the string value
        # but also handle cases where numeric extraction is needed
        if isinstance(value, str):
            return value.strip()
        return str(value) if value is not None else ''

    def _normalize_text(self, value):
        """Normalize text field value"""
        if isinstance(value, str):
            return value.strip()
        return str(value) if value is not None else ''

    def _get_default_value(self, field_type):
        """Get default value for field type"""
        defaults = {
            'integer': 0,
            'float': 0.0,
            'boolean': False,
            'selection': '',
            'text': '',
            'char': ''
        }
        return defaults.get(field_type, '')

    @api.model
    def normalize_for_template_calculation(self, field_values, template_id, context_type='unknown'):
        """
        Convenience method for template calculations
        
        Args:
            field_values (dict): Raw field values
            template_id (int): Template ID
            context_type (str): Context type (UI_FIELD_OPTION_MAPPING, BACKEND_SAVE_CONFIG, etc.)
            
        Returns:
            dict: Normalized field values ready for template calculation
        """
        context_info = {
            'source': 'template_calculation',
            'context_type': context_type
        }
        
        return self.normalize_field_values(field_values, template_id, context_info)
