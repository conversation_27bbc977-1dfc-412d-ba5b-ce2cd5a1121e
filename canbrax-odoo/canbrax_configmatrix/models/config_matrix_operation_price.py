# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixOperationPrice(models.Model):
    _name = 'config.matrix.operation.price'
    _description = 'Fixed Price Table for Operations'
    _order = 'sequence, description'

    # Basic Information
    sequence = fields.Integer('Sequence', default=10, help='Order of display')
    active = fields.Boolean('Active', default=True)

    # Code and Type (for Excel import and reference)
    code = fields.Char('Code', required=True, help='Unique code for referencing in calculations (e.g., WageInP, RecMatlT)')
    type = fields.Selection([
        ('Fixed Time', 'Fixed Time'),
        ('Labour', 'Labour'),
        ('Material', 'Material'),
        ('Setup', 'Setup'),
        ('Finishing', 'Finishing'),
        ('Quality', 'Quality'),
        ('Other', 'Other')
    ], string='Type', required=True, default='Labour', help='Type of operation for categorization')

    # Description and Classification
    description = fields.Char('Description', required=True,
                             help='Main description of the operation (e.g., "Install Wage & Vehicle Cost")')
    sub_description = fields.Char('Sub-Description',
                                 help='Additional details about the operation type, units, etc. (e.g., "$/hr", "hr / Item Manufactured")')
    
    # Pricing Information
    value_cost = fields.Float('Value / Cost', digits=(16, 3), required=True,
                             help='The value or cost for this operation (e.g., $97.70, 0.035)')

    # Work Centre (dropdown selection)
    work_centre_id = fields.Many2one('mrp.workcenter', 'Work Centre', help='Select work centre for this operation')
    work_centre = fields.Char('Work Centre Code', help='Legacy work centre code field')

    # Legacy fields (kept for backward compatibility)
    unit_type = fields.Selection([
        ('hr', 'Hours'),
        ('min', 'Minutes'),
        ('each', 'Each'),
        ('length', 'Per Length'),
        ('area', 'Per Area'),
        ('volume', 'Per Volume'),
        ('kg', 'Per Kilogram'),
        ('item', 'Per Item'),
        ('pair', 'Per Pair'),
        ('hinge', 'Per Hinge'),
        ('fixed', 'Fixed Price')
    ], string='Unit Type', default='hr',
       help='The unit of measurement for pricing')

    cost_price = fields.Float('Cost Price', digits='Product Price',
                             help='Internal cost for this operation')
    sale_price = fields.Float('Sale Price', digits='Product Price',
                             help='Sale price for this operation')
    
    # Markup and Profit
    markup_percentage = fields.Float('Markup %', compute='_compute_markup', store=True,
                                   help='Markup percentage from cost to sale price')
    profit_margin = fields.Float('Profit Margin', compute='_compute_profit_margin', store=True,
                                help='Profit amount (Sale Price - Cost Price)')
    
    # Categories and Tags
    category = fields.Selection([
        ('labour', 'Labour'),
        ('fixed_time', 'Fixed Time'),
        ('material', 'Material Handling'),
        ('setup', 'Setup/Preparation'),
        ('finishing', 'Finishing'),
        ('quality', 'Quality Control'),
        ('packaging', 'Packaging'),
        ('other', 'Other')
    ], string='Category', default='labour', help='Category of operation')
    
    # Work Center Integration
    workcenter_ids = fields.Many2many('mrp.workcenter', 
                                     'operation_price_workcenter_rel',
                                     'price_id', 'workcenter_id',
                                     string='Applicable Work Centers',
                                     help='Work centers where this pricing applies')
    
    # Notes and Additional Info
    notes = fields.Text('Notes', help='Additional notes about this pricing entry')
    
    # Usage Tracking
    usage_count = fields.Integer('Usage Count', compute='_compute_usage_count', store=True,
                                help='Number of operation templates using this pricing')
    
    # Company and Currency
    company_id = fields.Many2one('res.company', 'Company',
                                default=lambda self: self.env.company,
                                help='Company this pricing applies to')
    currency_id = fields.Many2one('res.currency', 'Currency',
                                 related='company_id.currency_id', readonly=True,
                                 help='Currency for pricing')

    @api.depends('cost_price', 'sale_price')
    def _compute_markup(self):
        """Calculate markup percentage"""
        for record in self:
            if record.cost_price and record.cost_price > 0:
                record.markup_percentage = ((record.sale_price - record.cost_price) / record.cost_price) * 100
            else:
                record.markup_percentage = 0.0

    @api.depends('cost_price', 'sale_price')
    def _compute_profit_margin(self):
        """Calculate profit margin"""
        for record in self:
            record.profit_margin = record.sale_price - record.cost_price

    @api.depends()
    def _compute_usage_count(self):
        """Compute how many operation templates use this pricing"""
        for record in self:
            # Count operation templates that reference this pricing
            count = self.env['config.matrix.operation.template'].search_count([
                ('operation_price_id', '=', record.id)
            ])
            record.usage_count = count

    @api.constrains('value_cost', 'sale_price', 'cost_price')
    def _check_prices(self):
        """Validate price values"""
        for record in self:
            if record.value_cost < 0:
                raise ValidationError(_("Value/Cost cannot be negative"))
            if record.sale_price < 0:
                raise ValidationError(_("Sale price cannot be negative"))
            if record.cost_price < 0:
                raise ValidationError(_("Cost price cannot be negative"))

    @api.constrains('code')
    def _check_code_unique(self):
        """Ensure code is unique"""
        for record in self:
            if record.code:
                existing = self.search([('code', '=', record.code), ('id', '!=', record.id)])
                if existing:
                    raise ValidationError(_("Code '%s' already exists. Codes must be unique.") % record.code)

    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"[{record.code}] {record.description}"
            if record.sub_description:
                name += f" ({record.sub_description})"
            if record.value_cost:
                name += f" - {record.value_cost}"
            result.append((record.id, name))
        return result

    @api.model
    def import_from_excel(self, file_data, filename):
        """Import fixed price data from Excel file

        Expected Excel format:
        Column A: Type (Fixed Time, Labour, etc.)
        Column B: Code (WageInP, RecMatlT, etc.)
        Column C: Description
        Column D: Sub-Description
        Column E: Value / Cost
        Column F: Work Centre
        """
        try:
            import openpyxl
            import io

            # Load workbook
            workbook = openpyxl.load_workbook(io.BytesIO(file_data), data_only=True)
            worksheet = workbook.active

            imported_count = 0
            updated_count = 0
            errors = []

            # Skip header row, start from row 2
            for row_num, row in enumerate(worksheet.iter_rows(min_row=2, values_only=True), start=2):
                try:
                    # Skip empty rows
                    if not any(row):
                        continue

                    # Extract data from columns
                    type_val = str(row[0]).strip() if row[0] else ''
                    code = str(row[1]).strip() if row[1] else ''
                    description = str(row[2]).strip() if row[2] else ''
                    sub_description = str(row[3]).strip() if row[3] else ''
                    value_cost = float(row[4]) if row[4] and str(row[4]).replace('.', '').replace('-', '').isdigit() else 0.0
                    work_centre = str(row[5]).strip() if row[5] else ''

                    # Validate required fields
                    if not code or not description:
                        errors.append(f"Row {row_num}: Code and Description are required")
                        continue

                    # Check if record exists
                    existing = self.search([('code', '=', code)])

                    vals = {
                        'type': type_val,
                        'code': code,
                        'description': description,
                        'sub_description': sub_description,
                        'value_cost': value_cost,
                        'work_centre': work_centre,
                        'active': True,
                    }

                    if existing:
                        existing.write(vals)
                        updated_count += 1
                    else:
                        self.create(vals)
                        imported_count += 1

                except Exception as e:
                    errors.append(f"Row {row_num}: {str(e)}")
                    continue

            return {
                'imported': imported_count,
                'updated': updated_count,
                'errors': errors,
                'total_processed': imported_count + updated_count
            }

        except ImportError:
            raise ValidationError(_("openpyxl library is required to read Excel files"))
        except Exception as e:
            raise ValidationError(_("Error reading Excel file: %s") % str(e))

    def open_record(self):
        """Open the record in form view"""
        self.ensure_one()
        return {
            'name': _('Fixed Price Entry'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.operation.price',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
        }

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search functionality"""
        if args is None:
            args = []
        
        domain = args[:]
        if name:
            domain += ['|', '|',
                      ('description', operator, name),
                      ('sub_description', operator, name),
                      ('category', operator, name)]
        
        return self.search(domain, limit=limit).name_get()

    def copy_data(self, default=None):
        """Override copy to add 'Copy' to the description"""
        default = dict(default or {})
        if 'description' not in default:
            default['description'] = _("%s (Copy)") % self.description
        return super().copy_data(default)

    def get_price_for_duration(self, duration_minutes):
        """
        Calculate the price for a given duration
        
        Args:
            duration_minutes (float): Duration in minutes
            
        Returns:
            float: Calculated price
        """
        self.ensure_one()
        
        if self.unit_type == 'fixed':
            return self.sale_price
        elif self.unit_type == 'min':
            return self.sale_price * duration_minutes
        elif self.unit_type == 'hr':
            return self.sale_price * (duration_minutes / 60.0)
        else:
            # For other unit types, return the base price
            # This can be extended based on specific requirements
            return self.sale_price

    def get_cost_for_duration(self, duration_minutes):
        """
        Calculate the cost for a given duration
        
        Args:
            duration_minutes (float): Duration in minutes
            
        Returns:
            float: Calculated cost
        """
        self.ensure_one()
        
        if self.unit_type == 'fixed':
            return self.cost_price
        elif self.unit_type == 'min':
            return self.cost_price * duration_minutes
        elif self.unit_type == 'hr':
            return self.cost_price * (duration_minutes / 60.0)
        else:
            # For other unit types, return the base cost
            return self.cost_price
