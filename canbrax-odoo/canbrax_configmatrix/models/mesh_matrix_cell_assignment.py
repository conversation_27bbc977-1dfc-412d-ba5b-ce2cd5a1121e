# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class MeshMatrixCellAssignment(models.Model):
    _name = 'mesh.matrix.cell.assignment'
    _description = 'Mesh Matrix Cell Assignment'
    _order = 'matrix_id, height, width'
    _rec_name = 'display_name'

    # Core fields
    matrix_id = fields.Many2one('mesh.cut.matrix', 'Matrix', required=True, ondelete='cascade')
    height = fields.Float('Height (mm)', required=True)
    width = fields.Float('Width (mm)', required=True)

    # Add computed fields for dynamic labeling
    orientation_methodology = fields.Selection(
        related='matrix_id.orientation_methodology',
        string='Orientation Methodology',
        store=True
    )

    # Assignment fields
    primary_cut_plan_id = fields.Many2one('mesh.cut.plan', 'Primary Cut Plan',
                                         domain="[('matrix_id', '=', matrix_id)]")
    secondary_cut_plan_id = fields.Many2one('mesh.cut.plan', 'Secondary Cut Plan',
                                           domain="[('matrix_id', '=', matrix_id), ('id', '!=', primary_cut_plan_id)]")
    arrow_direction = fields.Selection([
        ('→', 'Right Arrow (→)'),
        ('↓', 'Down Arrow (↓)'),
    ], string='Navigation Direction', help='Navigation direction for matrix lookup')
    cut_to_size = fields.Boolean('Cut to Size', help="Indicates this cell should be cut to the exact dimensions")
    
    # Additional info
    notes = fields.Text('Notes')
    
    # Computed fields
    display_name = fields.Char('Display Name', compute='_compute_display_name', store=True)
    cell_reference = fields.Char('Cell Reference', compute='_compute_cell_reference', store=True)
    matrix_series = fields.Selection(related='matrix_id.mesh_series', string='Matrix Series', store=True)
    has_assignment = fields.Boolean('Has Assignment', compute='_compute_has_assignment', store=True)
    primary_master_product = fields.Char('Primary Master Product', related='primary_cut_plan_id.master_product_id.name')
    primary_master_dimensions = fields.Char('Primary Master Dimensions', compute='_compute_primary_master_dimensions', store=True)
    
    @api.depends('matrix_id', 'height', 'width')
    def _compute_display_name(self):
        for record in self:
            if record.matrix_id and record.height and record.width:
                record.display_name = f"{record.matrix_id.name} - {record.height}×{record.width}mm"
            else:
                record.display_name = "New Assignment"
    
    @api.depends('matrix_id', 'height', 'width')
    def _compute_cell_reference(self):
        for record in self:
            if record.matrix_id and record.height and record.width:
                # Get Excel-style reference (A1, B2, etc.)
                record.cell_reference = record._get_excel_cell_reference()
            else:
                record.cell_reference = ""
    
    @api.depends('primary_cut_plan_id', 'secondary_cut_plan_id', 'arrow_direction', 'cut_to_size')
    def _compute_has_assignment(self):
        for record in self:
            record.has_assignment = bool(record.primary_cut_plan_id or record.secondary_cut_plan_id or record.arrow_direction or record.cut_to_size)
    
    def _get_excel_cell_reference(self):
        """Generate Excel-style cell reference (A1, B2, etc.)"""
        if not self.matrix_id or not self.height or not self.width:
            return ""
        
        try:
            # Get width and height lists from matrix
            widths = [int(w.strip()) for w in self.matrix_id.width_values.split(',') if w.strip()]
            heights = [int(h.strip()) for h in self.matrix_id.height_values.split(',') if h.strip()]
            
            # Find column index (width)
            if self.width in widths:
                col_index = widths.index(self.width)
                col_letter = self._get_excel_column_letter(col_index)
            else:
                col_letter = "?"
            
            # Find row index (height)
            if self.height in heights:
                row_number = heights.index(self.height) + 1
            else:
                row_number = "?"
            
            return f"{col_letter}{row_number}"
        except:
            return "?"
    
    def _get_excel_column_letter(self, col_index):
        """Convert column index to Excel letter (0=A, 1=B, ..., 25=Z, 26=AA, etc.)"""
        result = ""
        while col_index >= 0:
            result = chr(col_index % 26 + ord('A')) + result
            col_index = col_index // 26 - 1
        return result

    @api.depends('primary_cut_plan_id', 'primary_cut_plan_id.master_product_id', 'primary_cut_plan_id.master_product_id.mesh_width', 'primary_cut_plan_id.master_product_id.mesh_height')
    def _compute_primary_master_dimensions(self):
        """Compute shortened dimensions from master product's mesh properties"""
        for record in self:
            if (record.primary_cut_plan_id and
                record.primary_cut_plan_id.master_product_id and
                record.primary_cut_plan_id.master_product_id.mesh_width and
                record.primary_cut_plan_id.master_product_id.mesh_height):

                # Get dimensions from master product's mesh properties
                width_mm = record.primary_cut_plan_id.master_product_id.mesh_width
                height_mm = record.primary_cut_plan_id.master_product_id.mesh_height

                # Convert to shortened format (remove trailing zeros)
                width_short = self._shorten_dimension(width_mm)
                height_short = self._shorten_dimension(height_mm)

                record.primary_master_dimensions = f"{width_short} x {height_short}"
            else:
                record.primary_master_dimensions = ""

    def _shorten_dimension(self, dimension_mm):
        """Convert dimension from mm to shortened format (e.g., 1200 -> 12, 2000 -> 2)"""
        if not dimension_mm:
            return ""

        # Convert to string and remove trailing zeros
        dim_str = str(int(dimension_mm))

        # Remove trailing zeros
        while dim_str.endswith('0') and len(dim_str) > 1:
            dim_str = dim_str[:-1]

        return dim_str

    @api.constrains('height', 'width', 'matrix_id')
    def _check_unique_cell(self):
        for record in self:
            if record.matrix_id and record.height and record.width:
                existing = self.search([
                    ('matrix_id', '=', record.matrix_id.id),
                    ('height', '=', record.height),
                    ('width', '=', record.width),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(
                        f"A cell assignment already exists for {record.height}×{record.width}mm "
                        f"in matrix '{record.matrix_id.name}'"
                    )
    
    @api.constrains('primary_cut_plan_id', 'secondary_cut_plan_id')
    def _check_different_cut_plans(self):
        for record in self:
            if (record.primary_cut_plan_id and record.secondary_cut_plan_id and 
                record.primary_cut_plan_id == record.secondary_cut_plan_id):
                raise ValidationError("Primary and Secondary cut plans must be different")
    
    def action_view_cut_plan(self):
        """Open the primary cut plan"""
        self.ensure_one()
        if not self.primary_cut_plan_id:
            return
        
        return {
            'type': 'ir.actions.act_window',
            'name': 'Cut Plan',
            'res_model': 'mesh.cut.plan',
            'res_id': self.primary_cut_plan_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.cell_reference} ({record.height}×{record.width}mm)"
            if record.matrix_id:
                name = f"{record.matrix_id.name} - {name}"
            result.append((record.id, name))
        return result

    @api.model_create_multi
    def create(self, vals_list):
        """Auto-sync to matrix JSON when creating assignments"""
        records = super().create(vals_list)

        # Sync each affected matrix
        matrices = records.mapped('matrix_id')
        for matrix in matrices:
            matrix.sync_cell_assignments_to_matrix()

        return records

    def write(self, vals):
        """Auto-sync to matrix JSON when updating assignments"""
        result = super().write(vals)

        # Sync affected matrices
        matrices = self.mapped('matrix_id')
        for matrix in matrices:
            matrix.sync_cell_assignments_to_matrix()

        return result

    def unlink(self):
        """Auto-sync to matrix JSON when deleting assignments"""
        matrices = self.mapped('matrix_id')
        result = super().unlink()

        # Sync affected matrices after deletion
        for matrix in matrices:
            matrix.sync_cell_assignments_to_matrix()

        return result

    # Helper methods for mesh orientation methodology
    def get_dimension_display(self):
        """Return formatted dimension display"""
        self.ensure_one()
        if self.matrix_id and self.matrix_id.orientation_methodology == 'largest_smallest':
            return f"{int(self.height)}×{int(self.width)}mm (LS)"
        else:
            return f"{int(self.height)}×{int(self.width)}mm (HW)"

    def get_dimension_labels(self):
        """Get appropriate labels for this cell's dimensions"""
        self.ensure_one()
        if self.matrix_id and self.matrix_id.orientation_methodology == 'largest_smallest':
            return {
                'first_label': 'Largest (mm)',
                'second_label': 'Smallest (mm)'
            }
        else:
            return {
                'first_label': 'Height (mm)',
                'second_label': 'Width (mm)'
            }
