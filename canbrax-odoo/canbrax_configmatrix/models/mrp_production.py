# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)

class MrpBom(models.Model):
    _inherit = 'mrp.bom'

    config_id = fields.Many2one('config.matrix.configuration', "Configuration",
                              ondelete="set null", help="Configuration that generated this BOM")
    is_configured = fields.Bo<PERSON>an("Configured", compute='_compute_is_configured', store=True)
    configuration_summary = fields.Text("Configuration Summary", compute='_compute_configuration_summary')

    @api.depends('config_id')
    def _compute_is_configured(self):
        for bom in self:
            bom.is_configured = bool(bom.config_id)

    @api.depends('config_id')
    def _compute_configuration_summary(self):
        for bom in self:
            if bom.config_id:
                bom.configuration_summary = bom.config_id.get_configuration_summary()
            else:
                bom.configuration_summary = ""

    def action_view_configuration(self):
        """View configuration details"""
        self.ensure_one()

        if not self.config_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Not Configured'),
                    'message': _('This bill of materials is not based on a configured product.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Configuration'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'form',
            'res_id': self.config_id.id,
        }

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    config_id = fields.Many2one('config.matrix.configuration', "Configuration",
                              related='bom_id.config_id', store=True)
    is_configured = fields.Boolean("Configured", related='bom_id.is_configured', store=True)
    configuration_summary = fields.Text("Configuration Summary", related='bom_id.configuration_summary')

    # Mesh cut plan integration
    mesh_cut_operation_ids = fields.One2many('mesh.cut.operation', 'production_id', 'Mesh Cut Operations')
    has_mesh_operations = fields.Boolean('Has Mesh Operations', compute='_compute_has_mesh_operations', store=True)

    @api.depends('mesh_cut_operation_ids')
    def _compute_has_mesh_operations(self):
        for production in self:
            production.has_mesh_operations = bool(production.mesh_cut_operation_ids)

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to link mesh cut operations and add byproducts"""
        _logger.info(f"[BYPRODUCT_DEBUG] Creating {len(vals_list)} Manufacturing Orders with mesh integration")
        productions = super().create(vals_list)

        for production in productions:
            _logger.info(f"[BYPRODUCT_DEBUG] Processing MO {production.name}")
            _logger.info(f"[BYPRODUCT_DEBUG] - Config ID: {production.config_id.id if production.config_id else 'None'}")
            _logger.info(f"[BYPRODUCT_DEBUG] - BOM ID: {production.bom_id.id if production.bom_id else 'None'}")
            _logger.info(f"[BYPRODUCT_DEBUG] - BOM Config ID: {production.bom_id.config_id.id if production.bom_id and production.bom_id.config_id else 'None'}")

            # Try to get config from BOM if not directly available
            config_id = production.config_id
            if not config_id and production.bom_id and production.bom_id.config_id:
                config_id = production.bom_id.config_id
                _logger.info(f"[BYPRODUCT_DEBUG] Using config from BOM: {config_id.id}")

            if config_id:
                _logger.info(f"[BYPRODUCT_DEBUG] Integrating mesh operations for config {config_id.id}")
                production._link_mesh_operations_for_config(config_id)
                production._add_mesh_byproducts_for_config(config_id)
            else:
                _logger.warning(f"[BYPRODUCT_DEBUG] No config found for MO {production.name} - skipping mesh integration")

        return productions

    def _link_mesh_operations(self):
        """Link existing mesh cut operations to this production"""
        self.ensure_one()
        config_id = self.config_id or (self.bom_id.config_id if self.bom_id else None)
        if config_id:
            self._link_mesh_operations_for_config(config_id)

    def _link_mesh_operations_for_config(self, config):
        """Link existing mesh cut operations to this production for a specific config"""
        self.ensure_one()

        # Find mesh operations for this configuration
        mesh_operations = self.env['mesh.cut.operation'].search([
            ('config_id', '=', config.id),
            ('production_id', '=', False)  # Not yet linked to a production
        ])

        _logger.info(f"[BYPRODUCT_DEBUG] Found {len(mesh_operations)} unlinked mesh operations for config {config.id}")

        if mesh_operations:
            mesh_operations.write({'production_id': self.id})
            _logger.info(f"[BYPRODUCT_DEBUG] ✓ Linked {len(mesh_operations)} mesh operations to production {self.name}")

    def _add_mesh_byproducts(self):
        """Add byproducts from mesh cut plans to the BOM"""
        self.ensure_one()
        config_id = self.config_id or (self.bom_id.config_id if self.bom_id else None)
        if config_id:
            self._add_mesh_byproducts_for_config(config_id)

    def _add_mesh_byproducts_for_config(self, config):
        """Add byproducts from mesh cut plans to the BOM for a specific config"""
        self.ensure_one()

        if not self.bom_id:
            _logger.warning(f"[BYPRODUCT_DEBUG] No BOM found for MO {self.name}")
            return

        # Find mesh operations for this config that have cut plans
        mesh_operations = self.env['mesh.cut.operation'].search([
            ('config_id', '=', config.id),
            ('cut_plan_id', '!=', False)
        ])

        _logger.info(f"[BYPRODUCT_DEBUG] Found {len(mesh_operations)} mesh operations with cut plans for config {config.id}")

        byproducts_added = 0
        for mesh_operation in mesh_operations:
            if mesh_operation.cut_plan_id and mesh_operation.cut_plan_id.byproduct_ids:
                cut_plan = mesh_operation.cut_plan_id
                _logger.info(f"[BYPRODUCT_DEBUG] Processing cut plan {cut_plan.name} with {len(cut_plan.byproduct_ids)} byproducts")

                for byproduct in cut_plan.byproduct_ids:
                    if not byproduct.product_id:
                        _logger.warning(f"[BYPRODUCT_DEBUG] Skipping byproduct with no product")
                        continue

                    # Check if byproduct already exists in BOM
                    existing_byproduct = self.bom_id.byproduct_ids.filtered(
                        lambda b: b.product_id.id == byproduct.product_id.id
                    )

                    if not existing_byproduct:
                        # Create BOM byproduct record
                        try:
                            new_byproduct = self.env['mrp.bom.byproduct'].create({
                                'bom_id': self.bom_id.id,
                                'product_id': byproduct.product_id.id,
                                'product_qty': byproduct.quantity,
                                'product_uom_id': byproduct.product_id.uom_id.id,
                            })
                            byproducts_added += 1
                            _logger.info(f"[BYPRODUCT_DEBUG] ✓ Created BOM byproduct {new_byproduct.id}: {byproduct.product_id.name} (qty: {byproduct.quantity})")
                        except Exception as e:
                            _logger.error(f"[BYPRODUCT_DEBUG] ✗ Failed to create BOM byproduct: {e}")
                    else:
                        # Update quantity if byproduct already exists
                        old_qty = existing_byproduct.product_qty
                        existing_byproduct.product_qty += byproduct.quantity
                        _logger.info(f"[BYPRODUCT_DEBUG] ✓ Updated existing BOM byproduct {byproduct.product_id.name}: {old_qty} → {existing_byproduct.product_qty}")

        _logger.info(f"[BYPRODUCT_DEBUG] Added {byproducts_added} new byproducts to BOM {self.bom_id.code}")
        _logger.info(f"[BYPRODUCT_DEBUG] ✓ BOM byproducts will be inherited by MO automatically")



    def action_view_mesh_operations(self):
        """View mesh cut operations for this manufacturing order"""
        self.ensure_one()

        if not self.mesh_cut_operation_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Mesh Operations'),
                    'message': _('This manufacturing order has no mesh cutting operations.'),
                    'type': 'info',
                }
            }

        return {
            'name': _('Mesh Cut Operations'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'tree,form',
            'domain': [('production_id', '=', self.id)],
            'context': {'default_production_id': self.id},
        }

    def action_view_configuration(self):
        """View configuration details"""
        self.ensure_one()

        if not self.config_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Not Configured'),
                    'message': _('This manufacturing order is not based on a configured product.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Configuration'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'form',
            'res_id': self.config_id.id,
        }

    def action_confirm(self):
        """Override confirm to integrate mesh operations after MO is fully set up"""
        result = super().action_confirm()

        # Integrate mesh operations after confirmation
        for production in self:
            config_id = production.config_id or (production.bom_id.config_id if production.bom_id else None)
            if config_id:
                _logger.info(f"[BYPRODUCT_DEBUG] Integrating mesh operations for confirmed MO {production.name}, config {config_id.id}")
                production._link_mesh_operations_for_config(config_id)
                production._add_mesh_byproducts_for_config(config_id)

        return result
