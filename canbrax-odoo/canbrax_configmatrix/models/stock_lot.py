# -*- coding: utf-8 -*-
import logging
import re
from odoo import models, fields, api

_logger = logging.getLogger(__name__)

class StockLot(models.Model):
    _inherit = 'stock.lot'
    
    # Dimensions for unplanned off-cuts
    mesh_width = fields.Float('Mesh Width (mm)', 
                            help="Width of the mesh piece in millimeters")
    mesh_height = fields.Float('Mesh Height (mm)', 
                             help="Height of the mesh piece in millimeters")
    
    # Mesh type flags for easy filtering
    is_master_sheet = fields.<PERSON><PERSON><PERSON>('Is Master Sheet', 
                                   help="This lot represents a master sheet")
    is_planned_offcut = fields.<PERSON><PERSON><PERSON>('Is Planned Off-cut', 
                                     help="This lot represents a planned off-cut")
    is_unplanned_offcut = fields.<PERSON><PERSON><PERSON>('Is Unplanned Off-cut', 
                                       help="This lot represents an unplanned off-cut")
    mesh_series = fields.Selection(
        related='product_id.mesh_series'
    )
    mesh_type = fields.Selection(
        related='product_id.mesh_type'
    )
    # Reference to original master if this is an off-cut
    source_master_lot_id = fields.Many2one('stock.lot', 'Source Master Lot',
                                         help="The master sheet lot this off-cut came from")
    
    # Computed field for display
    display_name_with_size = fields.Char('Display Name with Size', 
                                       compute='_compute_display_name_with_size',
                                       store=True)
    
    @api.depends('name', 'mesh_width', 'mesh_height')
    def _compute_display_name_with_size(self):
        """Compute display name including dimensions"""
        for lot in self:
            if lot.mesh_width and lot.mesh_height:
                lot.display_name_with_size = f"{lot.name} ({lot.mesh_width}x{lot.mesh_height}mm)"
            else:
                lot.display_name_with_size = lot.name

    @api.model
    def generate_lot_names(self, first_lot, count):
        """Generate `lot_names` from a string.
        
        Enhanced to support offcut naming convention when called from create_unplanned_offcut context.
        """
        if self.env.context.get('create_unplanned_offcut'):
            width = self.env.context.get('offcut_width')
            height = self.env.context.get('offcut_height')
            product_id = self.env.context.get('offcut_product_id')
            
            if width and height and product_id:
                # Use offcut naming convention: <product_code>_<width>x<height>_<sequence>
                product = self.env['product.product'].browse(product_id)
                default_code = product.default_code or product.name
                
                # Clean default_code to remove special characters
                default_code = re.sub(r'[^a-zA-Z0-9_-]', '_', default_code)
                
                width_int = int(width)
                height_int = int(height)
                
                base_name = f"{default_code}_{width_int}x{height_int}"
                
                # Find existing lots with similar base name to determine next sequence
                existing_lots = self.search([
                    ('name', 'like', f"{base_name}_%"),
                    ('product_id', '=', product_id)
                ])
                
                if not existing_lots:
                    sequence = 1
                else:
                    sequence_numbers = []
                    for lot in existing_lots:
                        # Extract sequence number from lot name (e.g., "ABC_100x200_5" -> 5)
                        match = re.search(rf"{re.escape(base_name)}_(\d+)$", lot.name)
                        if match:
                            sequence_numbers.append(int(match.group(1)))
                    
                    if sequence_numbers:
                        # Find the next available sequence number
                        sequence = max(sequence_numbers) + 1
                    else:
                        sequence = 1
                
                lot_names = []
                for i in range(count):
                    current_sequence = sequence + i
                    lot_name = f"{base_name}_{current_sequence:02d}"
                    lot_names.append({'lot_name': lot_name})
                
                return lot_names
        
        return super(StockLot, self).generate_lot_names(first_lot, count)

    @api.model
    def create_unplanned_offcut(self, product_id, width, height, source_lot_id=None, quantity=1.0):
        """Create a new unplanned off-cut lot
        
        Args:
            product_id (int): Product ID for the off-cut
            width (float): Width in mm
            height (float): Height in mm
            source_lot_id (int, optional): Source master lot ID
            quantity (float): Quantity of the off-cut
            
        Returns:
            stock.lot: Created lot record
        """
        # Set context for the enhanced generate_lot_names method
        context = {
            'create_unplanned_offcut': True,
            'offcut_width': width,
            'offcut_height': height,
            'offcut_product_id': product_id,
        }
        
        # Generate lot name using the enhanced generate_lot_names method
        lot_names = self.with_context(**context).generate_lot_names(None, 1)
        lot_name = lot_names[0]['lot_name']
        
        # Create the lot with the generated name
        lot = self.create({
            'name': lot_name,
            'product_id': product_id,
            'mesh_width': width,
            'mesh_height': height,
            'is_unplanned_offcut': True,
            'source_master_lot_id': source_lot_id,
        })
        
        return lot
    
    def action_view_source_master(self):
        """View the source master sheet"""
        self.ensure_one()
        if not self.source_master_lot_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Source Master',
                    'message': 'This lot does not have a source master sheet.',
                    'type': 'warning',
                }
            }
        
        return {
            'name': 'Source Master Sheet',
            'type': 'ir.actions.act_window',
            'res_model': 'stock.lot',
            'view_mode': 'form',
            'res_id': self.source_master_lot_id.id,
        }
    
    def action_view_related_offcuts(self):
        """View all off-cuts created from this master sheet"""
        self.ensure_one()
        
        offcuts = self.search([('source_master_lot_id', '=', self.id)])
        
        return {
            'name': f'Off-cuts from {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'stock.lot',
            'view_mode': 'list,form',
            'domain': [('id', 'in', offcuts.ids)],
            'context': {'default_source_master_lot_id': self.id},
        }
