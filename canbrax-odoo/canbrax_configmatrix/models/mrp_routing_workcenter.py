# -*- coding: utf-8 -*-

from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)


class MrpRoutingWorkcenter(models.Model):
    _inherit = 'mrp.routing.workcenter'

    # Config Matrix Cost Fields
    config_cost_value = fields.Float(
        'Config Cost Value',
        digits='Product Price',
        help='Calculated cost value from Config Matrix operation template formula'
    )
    config_operation_template_id = fields.Many2one(
        'config.matrix.operation.template',
        'Config Operation Template',
        help='Reference to the Config Matrix operation template used to create this operation'
    )
    is_config_generated = fields.Bo<PERSON>an(
        'Config Generated',
        default=False,
        help='True if this operation was generated from Config Matrix'
    )
    config_formula_result = fields.Char(
        'Formula Result',
        help='Details about the formula calculation result'
    )

    @api.depends('config_cost_value', 'time_cycle', 'workcenter_id')
    def _compute_display_cost_info(self):
        """Compute display information for cost"""
        for operation in self:
            if operation.is_config_generated and operation.config_cost_value:
                operation.display_cost_info = f"Cost: ${operation.config_cost_value:.2f}"
            else:
                operation.display_cost_info = ""

    display_cost_info = fields.Char(
        'Cost Info',
        compute='_compute_display_cost_info',
        help='Display cost information'
    )
