# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ConfigMatrixSettings(models.Model):
    _name = 'config.matrix.settings'
    _description = 'Configuration Matrix Global Settings'
    _rec_name = 'name'

    name = fields.Char('Settings Name', default='Matrix Configuration', readonly=True)
    
    # Work Center Settings
    default_mesh_workcenter_id = fields.Many2one(
        'mrp.workcenter', 
        'Default Mesh Work Center',
        help='Default work center for mesh cutting operations'
    )
    default_cutting_workcenter_id = fields.Many2one(
        'mrp.workcenter', 
        'Default Cutting Work Center',
        help='Default work center for general cutting operations'
    )
    default_assembly_workcenter_id = fields.Many2one(
        'mrp.workcenter', 
        'Default Assembly Work Center',
        help='Default work center for assembly operations'
    )
    
    # Mesh Operation Settings
    mesh_default_duration = fields.Float(
        'Mesh Default Duration (minutes)', 
        default=30.0,
        help='Default duration for mesh cutting operations'
    )
    mesh_cost_per_sqm = fields.Float(
        'Mesh Cost per m²', 
        default=2.5,
        help='Default cost per square meter for mesh operations'
    )
    
    # General Operation Settings
    default_operation_duration = fields.Float(
        'Default Operation Duration (minutes)', 
        default=60.0,
        help='Default duration for operations when no specific duration is set'
    )
    
    # System Settings
    auto_assign_workcenters = fields.Boolean(
        'Auto-assign Work Centers',
        default=True,
        help='Automatically assign work centers to operation templates on creation'
    )
    
    @api.model
    def get_settings(self):
        """Get the singleton settings record"""
        settings = self.search([], limit=1)
        if not settings:
            settings = self.create({})
            # Auto-assign work centers on first creation
            settings._auto_assign_default_workcenters()
        return settings

    def _auto_assign_default_workcenters(self):
        """Auto-assign default work centers if not set"""
        # Try to find UP1 (UPCUT SAW) for mesh operations
        if not self.default_mesh_workcenter_id:
            mesh_wc = self.env['mrp.workcenter'].search([('code', '=', 'UP1')], limit=1)
            if not mesh_wc:
                # Fallback to any cutting work center
                mesh_wc = self.env['mrp.workcenter'].search([
                    '|', ('name', 'ilike', 'cut'), ('name', 'ilike', 'saw')
                ], limit=1)
            if not mesh_wc:
                # Final fallback to first available work center
                mesh_wc = self.env['mrp.workcenter'].search([], limit=1)
            if mesh_wc:
                self.default_mesh_workcenter_id = mesh_wc.id

        # Auto-assign cutting work center if not set
        if not self.default_cutting_workcenter_id:
            cutting_wc = self.env['mrp.workcenter'].search([
                '|', ('name', 'ilike', 'cut'), ('name', 'ilike', 'saw')
            ], limit=1)
            if not cutting_wc:
                cutting_wc = self.env['mrp.workcenter'].search([], limit=1)
            if cutting_wc:
                self.default_cutting_workcenter_id = cutting_wc.id

        # Auto-assign assembly work center if not set
        if not self.default_assembly_workcenter_id:
            assembly_wc = self.env['mrp.workcenter'].search([
                '|', ('name', 'ilike', 'assembly'), ('name', 'ilike', 'assemble')
            ], limit=1)
            if not assembly_wc:
                assembly_wc = self.env['mrp.workcenter'].search([], limit=1)
            if assembly_wc:
                self.default_assembly_workcenter_id = assembly_wc.id
    
    @api.model
    def get_default_mesh_workcenter(self):
        """Get the default mesh work center"""
        settings = self.get_settings()
        return settings.default_mesh_workcenter_id
    
    @api.model
    def get_default_cutting_workcenter(self):
        """Get the default cutting work center"""
        settings = self.get_settings()
        return settings.default_cutting_workcenter_id
    
    @api.model
    def get_default_assembly_workcenter(self):
        """Get the default assembly work center"""
        settings = self.get_settings()
        return settings.default_assembly_workcenter_id
    
    @api.model
    def auto_assign_workcenter_for_operation(self, operation_template):
        """Auto-assign work center based on operation category"""
        settings = self.get_settings()
        
        if not settings.auto_assign_workcenters:
            return False
            
        if not operation_template.workcenter_id:
            workcenter = False
            
            # Check if it's a mesh operation
            if hasattr(operation_template, 'is_mesh_operation') and operation_template.is_mesh_operation:
                workcenter = settings.default_mesh_workcenter_id
            # Check category
            elif operation_template.category == 'cutting':
                workcenter = settings.default_cutting_workcenter_id
            elif operation_template.category == 'assembly':
                workcenter = settings.default_assembly_workcenter_id
            
            # Fallback to any available work center
            if not workcenter:
                workcenter = self.env['mrp.workcenter'].search([], limit=1)
            
            if workcenter:
                operation_template.workcenter_id = workcenter.id
                return workcenter
        
        return False
