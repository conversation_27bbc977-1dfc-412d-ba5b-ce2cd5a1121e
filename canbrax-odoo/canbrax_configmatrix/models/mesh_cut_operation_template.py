# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class MeshCutOperationTemplate(models.Model):
    """
    Specialized operation template for creating mesh cut operations.
    This extends the base operation template to handle mesh-specific logic.
    """
    _inherit = 'config.matrix.operation.template'
    
    # Mesh-specific fields
    is_mesh_operation = fields.<PERSON><PERSON>an('Is Mesh Operation', default=False,
                                      help='This operation creates mesh cut operation records')
    
    mesh_operation_type = fields.Selection([
        ('standard', 'Standard Mesh Cut'),
        ('precision', 'Precision Mesh Cut'),
        ('auto', 'Auto-Select Based on Complexity')
    ], string='Mesh Operation Type', default='auto',
       help='Type of mesh cut operation to create')
    
    auto_find_mesh = fields.Boolean('Auto Find Best Mesh', default=True,
                                   help='Automatically find the best mesh option when creating the operation')
    
    create_bom_component = fields.<PERSON><PERSON><PERSON>('Create BOM Component', default=True,
                                         help='Automatically add the selected mesh product as a BOM component')

    def execute(self, config_values, bom=None):
        """
        Standard execute method called by operation mapping system.
        Delegates to execute_mesh_operation for mesh-specific logic.
        """
        if self.is_mesh_operation:
            return self.execute_mesh_operation(config_values, bom)
        else:
            # Fall back to parent execute method if it exists
            return super().execute(config_values, bom) if hasattr(super(), 'execute') else {}

    def execute_mesh_operation(self, config_values, bom=None):
        """
        Execute the mesh cut operation creation logic.
        
        Args:
            config_values (dict): Configuration values from the configurator
            bom (mrp.bom, optional): BOM record to add components to
            
        Returns:
            dict: Operation result with created records
        """
        self.ensure_one()
        
        if not self.is_mesh_operation:
            return {}
        
        # Extract mesh requirements from calculated fields
        mesh_required = config_values.get('_CALCULATED_mesh_operation_required', False)
        if not mesh_required:
            _logger.debug("Mesh operation not required, skipping")
            return {}
        
        mesh_width = config_values.get('_CALCULATED_mesh_width', 0)
        mesh_height = config_values.get('_CALCULATED_mesh_height', 0)
        mesh_series = config_values.get('_CALCULATED_mesh_series', 'saltwater')
        
        if not mesh_width or not mesh_height:
            _logger.warning(f"Invalid mesh dimensions: {mesh_width}x{mesh_height}")
            return {}
        
        # Determine operation name
        operation_name = f"Mesh Cut - {mesh_width}x{mesh_height}mm ({mesh_series})"
        
        # Create mesh cut operation
        try:
            mesh_operation = self.env['mesh.cut.operation'].create({
                'name': operation_name,
                'required_width': mesh_width,
                'required_height': mesh_height,
                'required_qty': 1.0,
                'mesh_series': mesh_series,
                'state': 'draft',
                # Link to configuration if available
                'config_id': config_values.get('config_id'),
                'sale_order_id': config_values.get('sale_order_id'),
                'operation_template_id': self.id,
            })
            
            _logger.info(f"Created mesh cut operation: {mesh_operation.name}")
            
            # Auto-find best mesh if enabled
            if self.auto_find_mesh:
                try:
                    mesh_operation.action_find_mesh()
                    _logger.info(f"Auto-found mesh for operation {mesh_operation.id}")
                except Exception as e:
                    _logger.warning(f"Could not auto-find mesh: {str(e)}")
            
            # Add mesh product as BOM component if enabled and BOM provided
            mesh_product = None
            if self.create_bom_component and bom and mesh_operation.selected_option_id:
                # Get the mesh product from the selected option
                if hasattr(mesh_operation.selected_option_id, 'product_id'):
                    mesh_product = mesh_operation.selected_option_id.product_id
                elif hasattr(mesh_operation.selected_option_id, 'source_product_id'):
                    mesh_product = mesh_operation.selected_option_id.source_product_id
                
                if mesh_product:
                    # Calculate quantity (usually 1 for mesh)
                    mesh_qty = 1.0
                    
                    # Check if component already exists
                    existing_line = bom.bom_line_ids.filtered(
                        lambda l: l.product_id.id == mesh_product.id
                    )
                    
                    if existing_line:
                        # Update existing quantity
                        existing_line[0].product_qty += mesh_qty
                        _logger.info(f"Updated existing BOM line for {mesh_product.name}")
                    else:
                        # Create new BOM line
                        self.env['mrp.bom.line'].create({
                            'bom_id': bom.id,
                            'product_id': mesh_product.id,
                            'product_qty': mesh_qty,
                        })
                        _logger.info(f"Added mesh product {mesh_product.name} to BOM")
            
            return {
                'mesh_operation_id': mesh_operation.id,
                'mesh_operation_name': mesh_operation.name,
                'mesh_product_id': mesh_product.id if mesh_product else None,
                'mesh_product_name': mesh_product.name if mesh_product else None,
                'success': True,
            }
            
        except Exception as e:
            _logger.error(f"Error creating mesh cut operation: {str(e)}")
            return {
                'error': str(e),
                'success': False,
            }
    
    def get_calculated_duration(self, config_values):
        """Override to handle mesh-specific duration calculation"""
        if self.is_mesh_operation:
            # Simple duration based on area
            mesh_area = config_values.get('_CALCULATED_mesh_area', 0)

            # Base duration + area factor
            if mesh_area > 1000000:  # Large mesh (>1m²)
                return 60.0  # 1 hour for large mesh
            else:
                return 30.0  # 30 minutes for standard mesh

        # Fall back to parent method
        return super().get_calculated_duration(config_values)

    def get_calculated_cost(self, config_values):
        """Override to handle mesh-specific cost calculation"""
        if self.is_mesh_operation:
            # Simple cost calculation based on area
            mesh_area = config_values.get('_CALCULATED_mesh_area', 0)
            area_m2 = mesh_area / 1000000

            # Base cost per square meter
            return area_m2 * 2.5  # $2.50 per m²

        # Fall back to parent method
        return super().get_calculated_cost(config_values)
