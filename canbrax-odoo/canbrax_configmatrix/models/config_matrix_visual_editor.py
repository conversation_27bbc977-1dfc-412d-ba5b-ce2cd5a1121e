# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json

class ConfigMatrixVisualEditor(models.TransientModel):
    _name = 'config.matrix.visual.editor'
    _description = 'Visual Matrix Editor'
    
    name = fields.Char("Editor Name", default="Matrix Visual Editor")
    matrix_id = fields.Many2one('config.matrix.price.matrix', "Price Matrix")
    matrix_type = fields.Selection([
        ('price', 'Price Matrix')
    ], string="Matrix Type", default='price')
    
    # Matrix configuration
    matrix_name = fields.Char("Matrix Name")
    heights_text = fields.Text("Heights (comma-separated)", default="1000,1200,1500,1800,2000")
    widths_text = fields.Text("Widths (comma-separated)", default="600,800,1000,1200,1500")
    currency_name = fields.Char("Currency", default="AUD")
    
    # Matrix data as JSON
    matrix_data_json = fields.Text("Matrix Data (JSON)")
    
    # Statistics
    total_cells = fields.Integer("Total Cells", compute='_compute_stats')
    filled_cells = fields.Integer("Filled Cells", compute='_compute_stats')
    completion_rate = fields.Integer("Completion Rate %", compute='_compute_stats')
    
    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        
        # Load matrix data if matrix_id is provided
        if 'matrix_id' in self.env.context:
            matrix_id = self.env.context['matrix_id']
            matrix_type = self.env.context.get('matrix_type', 'price')
            
            if matrix_type == 'price' and matrix_id:
                matrix = self.env['config.matrix.price.matrix'].browse(matrix_id)
                if matrix.exists():
                    res.update({
                        'matrix_id': matrix.id,
                        'matrix_type': 'price',
                        'matrix_name': matrix.name,
                        'matrix_data_json': matrix.matrix_data or '{}',
                        'currency_name': matrix.currency_id.name if matrix.currency_id else 'AUD'
                    })
                    
                    # Parse height and width ranges
                    try:
                        height_ranges = json.loads(matrix.height_ranges or '[]')
                        width_ranges = json.loads(matrix.width_ranges or '[]')
                        
                        heights = [str(h.get('label', h.get('max', ''))) for h in height_ranges]
                        widths = [str(w.get('label', w.get('max', ''))) for w in width_ranges]
                        
                        res['heights_text'] = ','.join(heights)
                        res['widths_text'] = ','.join(widths)
                    except (json.JSONDecodeError, TypeError):
                        pass
                        

        
        return res
    
    @api.depends('heights_text', 'widths_text', 'matrix_data_json')
    def _compute_stats(self):
        for editor in self:
            try:
                heights = [h.strip() for h in (editor.heights_text or '').split(',') if h.strip()]
                widths = [w.strip() for w in (editor.widths_text or '').split(',') if w.strip()]
                matrix_data = json.loads(editor.matrix_data_json or '{}')
                
                total = len(heights) * len(widths)
                filled = len([k for k, v in matrix_data.items() if v])
                
                editor.total_cells = total
                editor.filled_cells = filled
                editor.completion_rate = int((filled / total * 100) if total > 0 else 0)
            except (json.JSONDecodeError, ValueError, TypeError):
                editor.total_cells = 0
                editor.filled_cells = 0
                editor.completion_rate = 0
    
    def action_save_matrix(self):
        """Save the matrix data back to the original matrix"""
        self.ensure_one()
        
        if not self.matrix_name:
            raise UserError(_("Matrix name is required"))
        
        # Parse heights and widths
        try:
            heights = [h.strip() for h in self.heights_text.split(',') if h.strip()]
            widths = [w.strip() for w in self.widths_text.split(',') if w.strip()]
            matrix_data = json.loads(self.matrix_data_json or '{}')
        except (ValueError, json.JSONDecodeError) as e:
            raise UserError(_("Invalid data format: %s") % str(e))
        
        if not heights or not widths:
            raise UserError(_("Heights and widths are required"))
        
        # Create height and width ranges
        height_ranges = []
        for i, height in enumerate(heights):
            try:
                height_val = int(height)
                min_val = int(heights[i-1]) + 1 if i > 0 else height_val
                height_ranges.append({
                    'min': min_val,
                    'max': height_val,
                    'label': height
                })
            except ValueError:
                raise UserError(_("Invalid height value: %s") % height)
        
        width_ranges = []
        for i, width in enumerate(widths):
            try:
                width_val = int(width)
                min_val = int(widths[i-1]) + 1 if i > 0 else width_val
                width_ranges.append({
                    'min': min_val,
                    'max': width_val,
                    'label': width
                })
            except ValueError:
                raise UserError(_("Invalid width value: %s") % width)
        
        # Save to appropriate matrix
        if self.matrix_type == 'price' and self.matrix_id:
            self.matrix_id.write({
                'name': self.matrix_name,
                'height_ranges': json.dumps(height_ranges),
                'width_ranges': json.dumps(width_ranges),
                'matrix_data': self.matrix_data_json,
            })
            

        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Matrix Saved'),
                'message': _('Matrix "%s" has been saved successfully.') % self.matrix_name,
                'type': 'success',
            }
        }
    
    def action_fill_sample_data(self):
        """Fill the matrix with sample data for testing"""
        self.ensure_one()
        
        try:
            heights = [h.strip() for h in self.heights_text.split(',') if h.strip()]
            widths = [w.strip() for w in self.widths_text.split(',') if w.strip()]
        except:
            raise UserError(_("Please enter valid heights and widths first"))
        
        if not heights or not widths:
            raise UserError(_("Heights and widths are required"))
        
        # Generate sample data
        sample_data = {}
        base_value = 100.0 if self.matrix_type == 'price' else 1.0
        
        for i, height in enumerate(heights):
            for j, width in enumerate(widths):
                # Create a key like "1200_800"
                key = f"{height}_{width}"
                
                # Generate sample value based on position
                multiplier = 1 + (i * 0.2) + (j * 0.15)
                value = round(base_value * multiplier, 2)
                
                sample_data[key] = value
        
        self.matrix_data_json = json.dumps(sample_data)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sample Data Generated'),
                'message': _('Sample data has been generated for %d cells.') % len(sample_data),
                'type': 'success',
            }
        }
    
    def action_clear_matrix(self):
        """Clear all matrix data"""
        self.ensure_one()
        self.matrix_data_json = '{}'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Matrix Cleared'),
                'message': _('All matrix data has been cleared.'),
                'type': 'info',
            }
        }
    
    def action_test_lookup(self):
        """Test price lookup with sample dimensions"""
        self.ensure_one()
        
        try:
            heights = [h.strip() for h in self.heights_text.split(',') if h.strip()]
            widths = [w.strip() for w in self.widths_text.split(',') if w.strip()]
            matrix_data = json.loads(self.matrix_data_json or '{}')
        except:
            raise UserError(_("Invalid matrix data"))
        
        if not heights or not widths:
            raise UserError(_("No dimensions available for testing"))
        
        # Test with middle values
        test_height = heights[len(heights) // 2]
        test_width = widths[len(widths) // 2]
        test_key = f"{test_height}_{test_width}"
        
        result = matrix_data.get(test_key)
        
        if result:
            message = _('Test successful: %s x %s = %s %s') % (
                test_height, test_width, result, 
                self.currency_name or ('AUD' if self.matrix_type == 'price' else 'hours')
            )
            msg_type = 'success'
        else:
            message = _('No value found for dimensions %s x %s') % (test_height, test_width)
            msg_type = 'warning'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Test Lookup'),
                'message': message,
                'type': msg_type,
            }
        }
    
    def get_matrix_for_display(self):
        """Get matrix data formatted for display"""
        self.ensure_one()
        
        try:
            heights = [h.strip() for h in self.heights_text.split(',') if h.strip()]
            widths = [w.strip() for w in self.widths_text.split(',') if w.strip()]
            matrix_data = json.loads(self.matrix_data_json or '{}')
        except:
            return {'heights': [], 'widths': [], 'data': {}}
        
        return {
            'heights': heights,
            'widths': widths,
            'data': matrix_data,
            'currency': self.currency_name or 'AUD',
            'total_cells': len(heights) * len(widths),
            'filled_cells': len([k for k, v in matrix_data.items() if v]),
            'completion_rate': int((len([k for k, v in matrix_data.items() if v]) / max(1, len(heights) * len(widths))) * 100)
        }
