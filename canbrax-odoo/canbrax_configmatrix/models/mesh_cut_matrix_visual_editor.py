# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json
import logging

_logger = logging.getLogger(__name__)

class MeshCutMatrixVisualEditor(models.TransientModel):
    _name = 'mesh.cut.matrix.visual.editor'
    _description = 'Visual Editor for Mesh Cut Matrix'
    
    name = fields.Char('Editor Name', required=True)
    matrix_id = fields.Many2one('mesh.cut.matrix', 'Cut Matrix', required=True)
    
    # Matrix configuration
    height_values = fields.Text('Height Values', 
                               help="Comma-separated list of height values")
    width_values = fields.Text('Width Values',
                              help="Comma-separated list of width values")
    
    # Matrix data
    matrix_data = fields.Text('Matrix Data JSON',
                             help="JSON object containing matrix cells with arrows and cut templates")
    
    # Visual matrix HTML for display and editing
    matrix_html = fields.Html('Matrix Visual Editor', compute='_compute_matrix_html')
    
    @api.depends('matrix_data', 'height_values', 'width_values')
    def _compute_matrix_html(self):
        """Generate interactive HTML for the cutting matrix editor"""
        for editor in self:
            editor.matrix_html = editor._generate_interactive_matrix_html()
    
    def _generate_interactive_matrix_html(self):
        """Generate interactive HTML table for editing the cutting matrix - Excel-style interface"""
        if not self.height_values or not self.width_values:
            return "<p>Please configure height and width values first.</p>"

        try:
            heights = [int(h.strip()) for h in self.height_values.split(',') if h.strip()]
            widths = [int(w.strip()) for w in self.width_values.split(',') if w.strip()]
            matrix_data = json.loads(self.matrix_data or '{}')
        except (ValueError, json.JSONDecodeError):
            return "<p>Invalid height/width values or matrix data format.</p>"

        if not heights or not widths:
            return "<p>No valid height or width values found.</p>"

        # Generate Excel-style HTML interface
        html = ['<div class="mesh-cut-matrix-excel-editor">']

        # Add instructions and toolbar
        html.append('''
        <div class="editor-header">
            <h4>Mesh Cut Matrix Editor</h4>
            <p class="text-muted">Click on any cell to edit. Use the toolbar below to add arrows and configure cuts.</p>

            <div class="editor-toolbar">
                <div class="btn-group me-2" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setArrowMode('right')" id="btnArrowRight">
                        → Right Arrow
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setArrowMode('down')" id="btnArrowDown">
                        ↓ Down Arrow
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setArrowMode('none')" id="btnNoArrow">
                        ✕ No Arrow
                    </button>
                </div>

                <div class="btn-group me-2" role="group">
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="toggleMasterMode()" id="btnMaster">
                        ⬛ Master Sheet
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="toggleOffcutMode()" id="btnOffcut">
                        📦 Offcut
                    </button>
                </div>

                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearSelectedCells()">
                        🗑️ Clear
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="saveMatrix()">
                        💾 Save Matrix
                    </button>
                </div>
            </div>

            <div class="current-mode-indicator">
                <span id="currentMode">Mode: Select cells to edit</span>
            </div>
        </div>
        ''')

        # Generate the matrix table
        html.append('<div class="matrix-container">')
        html.append('<table class="excel-matrix-table">')

        # Header row
        html.append('<thead><tr>')
        html.append('<th class="corner-cell">H\\W</th>')
        for width in widths:
            html.append(f'<th class="width-header">{width}</th>')
        html.append('</tr></thead>')

        # Data rows
        html.append('<tbody>')
        for height in heights:
            html.append('<tr>')
            html.append(f'<th class="height-header">{height}</th>')

            for width in widths:
                cell_key = f"{height}_{width}"
                cell_data = matrix_data.get(cell_key, {})

                # Generate Excel-style cell
                cell_content = self._generate_excel_cell_content(cell_data, height, width)
                cell_class = self._get_excel_cell_class(cell_data)

                html.append(f'''
                <td class="excel-cell {cell_class}"
                    data-height="{height}"
                    data-width="{width}"
                    data-key="{cell_key}"
                    onclick="selectCell(this, {height}, {width})"
                    title="Click to select {height}x{width}mm">
                    {cell_content}
                </td>
                ''')

            html.append('</tr>')

        html.append('</tbody>')
        html.append('</table>')
        html.append('</div>')

        # Add quick edit panel
        html.append('''
        <div class="quick-edit-panel" id="quickEditPanel" style="display: none;">
            <div class="panel-header">
                <h6>Quick Edit: <span id="selectedCellInfo"></span></h6>
                <button type="button" class="btn-close" onclick="closeQuickEdit()"></button>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-4">
                        <label>Cut Template:</label>
                        <input type="text" class="form-control form-control-sm" id="cutTemplate"
                               placeholder="e.g., 1100x620">
                    </div>
                    <div class="col-md-4">
                        <label>Target Size:</label>
                        <input type="text" class="form-control form-control-sm" id="targetSize"
                               placeholder="e.g., 1100x620">
                    </div>
                    <div class="col-md-4">
                        <label>Notes:</label>
                        <input type="text" class="form-control form-control-sm" id="cellNotes"
                               placeholder="Optional notes">
                    </div>
                </div>
                <div class="mt-2">
                    <button type="button" class="btn btn-primary btn-sm" onclick="applyQuickEdit()">Apply</button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="closeQuickEdit()">Cancel</button>
                </div>
            </div>
        </div>
        ''')

        html.append('</div>')

        # Add CSS and JavaScript
        html.append(self._get_excel_editor_css())
        html.append(self._get_excel_editor_js())
        
        return ''.join(html)
    
    def _generate_excel_cell_content(self, cell_data, height, width):
        """Generate Excel-style content for a matrix cell"""
        if not cell_data:
            return f'<div class="cell-dimensions">{height}×{width}</div>'

        content = []

        # Add arrow with proper styling
        arrow = cell_data.get('arrow', '')
        if arrow == 'right':
            content.append('<div class="cell-arrow arrow-right">→</div>')
        elif arrow == 'down':
            content.append('<div class="cell-arrow arrow-down">↓</div>')

        # Add cut template or size info
        if cell_data.get('cut_template'):
            template = cell_data['cut_template']
            content.append(f'<div class="cell-template">{template}</div>')
        elif cell_data.get('target_size'):
            size = cell_data['target_size']
            content.append(f'<div class="cell-size">{size}</div>')

        # Add master/offcut indicators
        if cell_data.get('is_master'):
            content.append('<div class="cell-type master">MASTER</div>')
        elif cell_data.get('is_offcut'):
            content.append('<div class="cell-type offcut">OFFCUT</div>')

        # Add notes if present
        if cell_data.get('notes'):
            notes = cell_data['notes'][:15] + ('...' if len(cell_data['notes']) > 15 else '')
            content.append(f'<div class="cell-notes">{notes}</div>')

        # If no specific content, show dimensions
        if not content:
            content.append(f'<div class="cell-dimensions">{height}×{width}</div>')

        return ''.join(content)
    
    def _get_excel_cell_class(self, cell_data):
        """Get CSS class for Excel-style matrix cell"""
        classes = []

        if not cell_data:
            classes.append('cell-empty')
        else:
            if cell_data.get('is_master'):
                classes.append('cell-master')
            elif cell_data.get('is_offcut'):
                classes.append('cell-offcut')
            elif cell_data.get('cut_template'):
                classes.append('cell-template')
            elif cell_data.get('arrow'):
                classes.append('cell-arrow')
            else:
                classes.append('cell-configured')

        return ' '.join(classes)
    
    def _get_excel_editor_css(self):
        """Return Excel-style CSS for the matrix editor"""
        return '''
        <style>
        .mesh-cut-matrix-excel-editor {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px 0;
        }

        .editor-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }

        .editor-header h4 {
            margin: 0 0 10px 0;
            font-weight: 600;
        }

        .editor-toolbar {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }

        .current-mode-indicator {
            margin-top: 10px;
            padding: 8px 12px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            font-weight: 500;
        }

        .matrix-container {
            background: white;
            border-radius: 0 0 8px 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .excel-matrix-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            background: white;
        }

        .corner-cell {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            padding: 8px;
            font-weight: bold;
            text-align: center;
            min-width: 60px;
        }

        .width-header, .height-header {
            background: #f8f9fa;
            border: 1px solid #dadce0;
            padding: 8px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
            color: #5f6368;
        }

        .excel-cell {
            border: 1px solid #dadce0;
            padding: 4px;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            position: relative;
            min-width: 80px;
            height: 50px;
            transition: all 0.2s ease;
            background: white;
        }

        .excel-cell:hover {
            background: #e8f0fe;
            border-color: #4285f4;
            box-shadow: 0 1px 3px rgba(66,133,244,0.3);
        }

        .excel-cell.selected {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
            box-shadow: 0 2px 8px rgba(26,115,232,0.4);
        }

        /* Cell type styling - like Excel colors */
        .excel-cell.cell-empty {
            background: #ffffff;
        }

        .excel-cell.cell-master {
            background: #d4edda;
            border-color: #28a745;
        }

        .excel-cell.cell-offcut {
            background: #fff3cd;
            border-color: #ffc107;
        }

        .excel-cell.cell-template {
            background: #cce5ff;
            border-color: #0066cc;
        }

        .excel-cell.cell-arrow {
            background: #e1f5fe;
            border-color: #00bcd4;
        }

        /* Cell content styling */
        .cell-arrow {
            font-size: 16px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 2px;
        }

        .cell-arrow.arrow-right {
            color: #2e7d32;
        }

        .cell-arrow.arrow-down {
            color: #d32f2f;
        }

        .cell-template {
            font-size: 9px;
            font-weight: 600;
            color: #0d47a1;
            margin-bottom: 2px;
        }

        .cell-size {
            font-size: 9px;
            color: #424242;
            font-weight: 500;
        }

        .cell-type {
            font-size: 8px;
            font-weight: bold;
            padding: 1px 3px;
            border-radius: 2px;
            margin-bottom: 2px;
        }

        .cell-type.master {
            background: #4caf50;
            color: white;
        }

        .cell-type.offcut {
            background: #ff9800;
            color: white;
        }

        .cell-dimensions {
            font-size: 9px;
            color: #757575;
            font-style: italic;
        }

        .cell-notes {
            font-size: 8px;
            color: #9e9e9e;
            margin-top: 2px;
        }

        /* Quick Edit Panel */
        .quick-edit-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 1px solid #dadce0;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 500px;
        }

        .panel-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dadce0;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-header h6 {
            margin: 0;
            font-weight: 600;
            color: #202124;
        }

        .panel-body {
            padding: 20px;
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #5f6368;
        }

        .btn-close:hover {
            color: #d93025;
        }
        </style>
        '''
    
    def _get_excel_editor_js(self):
        """Return Excel-style JavaScript for matrix editor functionality"""
        return '''
        <script>
        // Global state
        let selectedCells = [];
        let currentMode = 'select';
        let currentArrow = '';
        let isMasterMode = false;
        let isOffcutMode = false;
        let matrixData = {};

        // Initialize editor
        document.addEventListener('DOMContentLoaded', function() {
            updateModeIndicator();
        });

        // Mode management
        function setArrowMode(direction) {
            currentMode = 'arrow';
            currentArrow = direction;
            isMasterMode = false;
            isOffcutMode = false;
            updateToolbarButtons();
            updateModeIndicator();
        }

        function toggleMasterMode() {
            currentMode = 'master';
            isMasterMode = !isMasterMode;
            isOffcutMode = false;
            currentArrow = '';
            updateToolbarButtons();
            updateModeIndicator();
        }

        function toggleOffcutMode() {
            currentMode = 'offcut';
            isOffcutMode = !isOffcutMode;
            isMasterMode = false;
            currentArrow = '';
            updateToolbarButtons();
            updateModeIndicator();
        }

        function updateToolbarButtons() {
            // Reset all buttons
            document.querySelectorAll('.editor-toolbar .btn').forEach(btn => {
                btn.classList.remove('active');
                btn.classList.add('btn-outline-primary', 'btn-outline-success', 'btn-outline-info', 'btn-outline-secondary');
                btn.classList.remove('btn-primary', 'btn-success', 'btn-info', 'btn-secondary');
            });

            // Highlight active button
            if (currentArrow === 'right') {
                const btn = document.getElementById('btnArrowRight');
                btn.classList.add('btn-primary');
                btn.classList.remove('btn-outline-primary');
            } else if (currentArrow === 'down') {
                const btn = document.getElementById('btnArrowDown');
                btn.classList.add('btn-primary');
                btn.classList.remove('btn-outline-primary');
            } else if (currentArrow === 'none') {
                const btn = document.getElementById('btnNoArrow');
                btn.classList.add('btn-secondary');
                btn.classList.remove('btn-outline-secondary');
            }

            if (isMasterMode) {
                const btn = document.getElementById('btnMaster');
                btn.classList.add('btn-success');
                btn.classList.remove('btn-outline-success');
            }

            if (isOffcutMode) {
                const btn = document.getElementById('btnOffcut');
                btn.classList.add('btn-info');
                btn.classList.remove('btn-outline-info');
            }
        }

        function updateModeIndicator() {
            const indicator = document.getElementById('currentMode');
            let modeText = 'Mode: ';

            if (currentArrow === 'right') {
                modeText += 'Adding Right Arrows →';
            } else if (currentArrow === 'down') {
                modeText += 'Adding Down Arrows ↓';
            } else if (currentArrow === 'none') {
                modeText += 'Removing Arrows';
            } else if (isMasterMode) {
                modeText += 'Marking Master Sheets ⬛';
            } else if (isOffcutMode) {
                modeText += 'Marking Offcuts 📦';
            } else {
                modeText += 'Select cells to edit';
            }

            indicator.textContent = modeText;
        }

        // Cell selection and editing
        function selectCell(cellElement, height, width) {
            const cellKey = height + '_' + width;

            // Apply current mode to cell
            if (currentMode === 'arrow') {
                applyCellArrow(cellElement, height, width, currentArrow);
            } else if (currentMode === 'master') {
                applyCellMaster(cellElement, height, width, isMasterMode);
            } else if (currentMode === 'offcut') {
                applyCellOffcut(cellElement, height, width, isOffcutMode);
            } else {
                // Select mode - show quick edit
                showQuickEdit(cellElement, height, width);
            }
        }

        function applyCellArrow(cellElement, height, width, arrow) {
            const cellKey = height + '_' + width;

            if (!matrixData[cellKey]) matrixData[cellKey] = {};

            if (arrow === 'none') {
                delete matrixData[cellKey].arrow;
            } else {
                matrixData[cellKey].arrow = arrow;
            }

            updateCellDisplay(cellElement, matrixData[cellKey], height, width);
        }

        function applyCellMaster(cellElement, height, width, isMaster) {
            const cellKey = height + '_' + width;

            if (!matrixData[cellKey]) matrixData[cellKey] = {};

            if (isMaster) {
                matrixData[cellKey].is_master = true;
                delete matrixData[cellKey].is_offcut;
            } else {
                delete matrixData[cellKey].is_master;
            }

            updateCellDisplay(cellElement, matrixData[cellKey], height, width);
        }

        function applyCellOffcut(cellElement, height, width, isOffcut) {
            const cellKey = height + '_' + width;

            if (!matrixData[cellKey]) matrixData[cellKey] = {};

            if (isOffcut) {
                matrixData[cellKey].is_offcut = true;
                delete matrixData[cellKey].is_master;
            } else {
                delete matrixData[cellKey].is_offcut;
            }

            updateCellDisplay(cellElement, matrixData[cellKey], height, width);
        }

        // Quick edit panel
        function showQuickEdit(cellElement, height, width) {
            const panel = document.getElementById('quickEditPanel');
            const info = document.getElementById('selectedCellInfo');

            info.textContent = height + '×' + width + 'mm';
            panel.style.display = 'block';

            // Load current cell data
            const cellKey = height + '_' + width;
            const cellData = matrixData[cellKey] || {};

            document.getElementById('cutTemplate').value = cellData.cut_template || '';
            document.getElementById('targetSize').value = cellData.target_size || '';
            document.getElementById('cellNotes').value = cellData.notes || '';

            // Store current cell reference
            panel.dataset.height = height;
            panel.dataset.width = width;
            panel.dataset.cellElement = cellElement;
        }

        function applyQuickEdit() {
            const panel = document.getElementById('quickEditPanel');
            const height = parseInt(panel.dataset.height);
            const width = parseInt(panel.dataset.width);
            const cellKey = height + '_' + width;

            if (!matrixData[cellKey]) matrixData[cellKey] = {};

            const cutTemplate = document.getElementById('cutTemplate').value;
            const targetSize = document.getElementById('targetSize').value;
            const notes = document.getElementById('cellNotes').value;

            if (cutTemplate) matrixData[cellKey].cut_template = cutTemplate;
            else delete matrixData[cellKey].cut_template;

            if (targetSize) matrixData[cellKey].target_size = targetSize;
            else delete matrixData[cellKey].target_size;

            if (notes) matrixData[cellKey].notes = notes;
            else delete matrixData[cellKey].notes;

            // Update cell display
            const cellElement = document.querySelector(`[data-height="${height}"][data-width="${width}"]`);
            updateCellDisplay(cellElement, matrixData[cellKey], height, width);

            closeQuickEdit();
        }

        function closeQuickEdit() {
            document.getElementById('quickEditPanel').style.display = 'none';
        }
        
        function updateCellDisplay(cellElement, cellData, height, width) {
            let content = '';
            let cellClasses = ['excel-cell'];

            // Generate content based on cell data
            if (!cellData || Object.keys(cellData).length === 0) {
                content = '<div class="cell-dimensions">' + height + '×' + width + '</div>';
                cellClasses.push('cell-empty');
            } else {
                const parts = [];

                // Add arrow
                if (cellData.arrow === 'right') {
                    parts.push('<div class="cell-arrow arrow-right">→</div>');
                    cellClasses.push('cell-arrow');
                } else if (cellData.arrow === 'down') {
                    parts.push('<div class="cell-arrow arrow-down">↓</div>');
                    cellClasses.push('cell-arrow');
                }

                // Add template or size
                if (cellData.cut_template) {
                    parts.push('<div class="cell-template">' + cellData.cut_template + '</div>');
                    cellClasses.push('cell-template');
                } else if (cellData.target_size) {
                    parts.push('<div class="cell-size">' + cellData.target_size + '</div>');
                }

                // Add type indicators
                if (cellData.is_master) {
                    parts.push('<div class="cell-type master">MASTER</div>');
                    cellClasses.push('cell-master');
                } else if (cellData.is_offcut) {
                    parts.push('<div class="cell-type offcut">OFFCUT</div>');
                    cellClasses.push('cell-offcut');
                }

                // Add notes
                if (cellData.notes) {
                    const shortNotes = cellData.notes.length > 15 ?
                        cellData.notes.substring(0, 15) + '...' : cellData.notes;
                    parts.push('<div class="cell-notes">' + shortNotes + '</div>');
                }

                content = parts.length > 0 ? parts.join('') :
                    '<div class="cell-dimensions">' + height + '×' + width + '</div>';
            }
            
            // Update cell appearance
            cellElement.innerHTML = content;
            cellElement.className = cellClasses.join(' ');
        }

        // Utility functions
        function clearSelectedCells() {
            if (selectedCells.length === 0) {
                alert('Please select cells first by clicking on them');
                return;
            }

            selectedCells.forEach(cellElement => {
                const height = parseInt(cellElement.dataset.height);
                const width = parseInt(cellElement.dataset.width);
                const cellKey = height + '_' + width;

                delete matrixData[cellKey];
                updateCellDisplay(cellElement, {}, height, width);
            });

            selectedCells = [];
        }

        function saveMatrix() {
            // TODO: Implement server save
            console.log('Saving matrix data:', matrixData);

            // For now, just show a success message
            alert('Matrix saved successfully! (Note: This is a demo - actual saving would be implemented)');
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeQuickEdit();
            } else if (e.key === 'Delete' && selectedCells.length > 0) {
                clearSelectedCells();
            }
        });
        </script>
        '''
    
    def action_save_matrix(self):
        """Save the matrix data back to the original matrix"""
        if self.matrix_id:
            self.matrix_id.update_matrix_data(self.matrix_data)
        return {'type': 'ir.actions.act_window_close'}
    
    def action_fill_sample_data(self):
        """Fill with sample cutting matrix data"""
        self.matrix_id.action_fill_sample_data()
        self.matrix_data = self.matrix_id.matrix_data
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }
