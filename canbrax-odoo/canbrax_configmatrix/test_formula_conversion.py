#!/usr/bin/env python3
"""
Test script for formula conversion functionality
"""

def convert_js_to_python(js_formula):
    """Convert JavaScript formula syntax to Python syntax"""
    if not js_formula:
        return js_formula

    python_formula = js_formula
    print(f"DEBUG: Starting conversion of: {js_formula}")

    # Convert JavaScript operators to Python
    python_formula = python_formula.replace('===', '==')
    python_formula = python_formula.replace('!==', '!=')
    python_formula = python_formula.replace('&&', ' and ')
    
    # Convert JavaScript Math.min/max to Python min/max
    python_formula = python_formula.replace('Math.min(', 'min(')
    python_formula = python_formula.replace('Math.max(', 'max(')
    python_formula = python_formula.replace('Math.', '')

    # Convert JavaScript Number() function to parseFloat
    python_formula = python_formula.replace('Number(', 'parseFloat(')

    print(f"DEBUG: Before ternary conversion: {python_formula}")

    # Convert ternary operator first (before handling ||)
    python_formula = convert_ternary_operators(python_formula)

    print(f"DEBUG: After ternary conversion: {python_formula}")

    # Convert JavaScript logical OR with default value (|| 0) to Python equivalent
    # This needs to be after ternary conversion to avoid breaking nested ternary logic
    python_formula = python_formula.replace('||', ' or ')

    # Clean up any remaining JavaScript-specific syntax
    python_formula = python_formula.replace('===', '==')
    python_formula = python_formula.replace('!==', '!=')

    print(f"DEBUG: Final result: {python_formula}")
    return python_formula

def convert_ternary_operators(formula):
    """Convert JavaScript ternary operators to Python if-else expressions"""
    if ' ? ' not in formula or ' : ' not in formula:
        return formula
    
    def convert_nested_ternary(expr):
        """Convert nested ternary operators recursively"""
        print(f"DEBUG: convert_nested_ternary called with: {expr}")
        
        # Find the first question mark
        first_question = expr.find(' ? ')
        if first_question == -1:
            print(f"DEBUG: No question mark found")
            return expr
        
        print(f"DEBUG: First question mark at position {first_question}")
        
        # Find the first colon after the question mark
        # This is a simplified approach - just find the first colon
        colon_pos = expr.find(' : ', first_question + 3)
        
        if colon_pos == -1:
            print(f"DEBUG: No colon found after question mark")
            return expr
        
        print(f"DEBUG: Colon position found at {colon_pos}")
        
        # Extract the three parts
        condition = expr[:first_question].strip()
        true_part = expr[first_question + 3:colon_pos].strip()
        false_part = expr[colon_pos + 3:].strip()
        
        print(f"DEBUG: Extracted - condition: '{condition}', true_part: '{true_part}', false_part: '{false_part}'")
        
        # Remove outer parentheses if they're balanced
        if true_part.startswith('(') and true_part.endswith(')'):
            inner = true_part[1:-1]
            if inner.count('(') == inner.count(')'):
                true_part = inner
                print(f"DEBUG: Removed outer parentheses from true_part: '{true_part}'")
        
        if false_part.startswith('(') and false_part.endswith(')'):
            inner = false_part[1:-1]
            if inner.count('(') == inner.count(')'):
                false_part = inner
                print(f"DEBUG: Removed outer parentheses from false_part: '{false_part}'")
        
        # Recursively convert nested ternary in false_part
        if ' ? ' in false_part and ' : ' in false_part:
            print(f"DEBUG: Recursively converting nested ternary in false_part")
            false_part = convert_nested_ternary(false_part)
        
        # Generate Python expression
        python_expr = f"({true_part} if ({condition}) else {false_part})"
        print(f"DEBUG: Generated Python expression: {python_expr}")
        
        return python_expr
    
    try:
        result = convert_nested_ternary(formula)
        print(f"DEBUG: convert_ternary_operators returning: {result}")
        return result
    except Exception as e:
        print(f"DEBUG: Ternary conversion failed for formula: {formula[:100]}... Error: {e}")
        return formula.replace(' ? ', ' if ').replace(' : ', ' else ')

def test_formula_conversion():
    """Test the formula conversion with sample formulas"""
    print("Testing JavaScript to Python Formula Conversion")
    print("=" * 50)
    
    # Test formulas
    test_formulas = [
        # Simple ternary
        "a > b ? 1 : 0",
        
        # Nested ternary
        "x === 'yes' ? (y > 10 ? 'high' : 'low') : 'unknown'",
        
        # Complex nested ternary from the real data
        "sws_wscrn_hand_built_window_screen === 'no' ? (parseFloat(sws_wscrn_make_window_screen_height_mm) || 0) : (sws_wscrn_hand_built_type === 'angled_out_of_square_bow_different_heights_widths' ? (Math.max((Number(sws_wscrn_square_make_height_mm) + Number(sws_wscrn_top_left_top_out_of_square_dimension_mm) + Number(sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm)), (Number(sws_wscrn_square_make_height_mm) + Number(sws_wscrn_top_right_top_out_of_square_dimension_mm) + Number(sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm))) || 0) : '')",
        
        # Simple dependency
        "_CALCULATED_Middle_width > 1200 ? 2 : 1",
        
        # Math functions
        "Math.max(a, b, c) + Math.min(x, y)",
        
        # Number function
        "Number(value) || 0"
    ]
    
    for i, formula in enumerate(test_formulas, 1):
        print(f"\nTest {i}:")
        print(f"Original: {formula}")
        
        try:
            converted = convert_js_to_python(formula)
            print(f"Converted: {converted}")
            
            # Test if the converted formula is valid Python syntax
            try:
                # Create a simple test context
                test_context = {
                    'a': 5, 'b': 3, 'c': 7, 'x': 10, 'y': 2,
                    'sws_wscrn_hand_built_window_screen': 'no',
                    'sws_wscrn_make_window_screen_height_mm': '1425',
                    'sws_wscrn_hand_built_type': 'angled_out_of_square_bow_different_heights_widths',
                    'sws_wscrn_square_make_height_mm': '1400',
                    'sws_wscrn_top_left_top_out_of_square_dimension_mm': '25',
                    'sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm': '0',
                    'sws_wscrn_top_right_top_out_of_square_dimension_mm': '25',
                    'sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm': '0',
                    'sws_wscrn_square_make_width_mm': '900',
                    'sws_wscrn_top_left_side_out_of_square_dimension_mm': '25',
                    'sws_wscrn_top_right_side_out_of_square_dimension_mm': '25',
                    'sws_wscrn_middle_left_side_out_of_square_dimension_mm': '20',
                    'sws_wscrn_middle_right_side_out_of_square_dimension_mm': '20',
                    'sws_wscrn_bottom_left_side_out_of_square_dimension_mm': '0',
                    'sws_wscrn_bottom_right_side_out_of_square_dimension_mm': '0',
                    '_CALCULATED_Middle_width': 945,
                    'value': '42',
                    'parseFloat': lambda x: float(x) if x not in [None, ''] else 0,
                    'min': min,
                    'max': max
                }
                
                result = eval(converted, test_context)
                print(f"✅ Evaluation successful: {result}")
                
            except Exception as eval_error:
                print(f"❌ Evaluation failed: {eval_error}")
                
        except Exception as e:
            print(f"❌ Conversion failed: {e}")
    
    print("\n" + "=" * 50)
    print("Formula conversion test completed!")

# Test the ternary conversion function directly
print("\n" + "="*50)
print("Testing Ternary Conversion Directly")
print("="*50)

test_cases = [
    "a > b ? 1 : 0",
    "x === 'yes' ? (y > 10 ? 'high' : 'low') : 'unknown'",
    "_CALCULATED_Middle_width > 1200 ? 2 : 1"
]

for i, test_case in enumerate(test_cases, 1):
    print(f"\nDirect Test {i}:")
    print(f"Original: {test_case}")
    converted = convert_ternary_operators(test_case)
    print(f"Converted: {converted}")
    
    # Try to evaluate the converted formula
    try:
        # Create a simple context for testing
        context = {'a': 5, 'b': 3, 'x': 'yes', 'y': 15, '_CALCULATED_Middle_width': 1500}
        result = eval(converted, context)
        print(f"✅ Evaluation successful: {result}")
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")

print("\n" + "="*50)
print("Direct ternary conversion test completed!")
print("="*50)

if __name__ == "__main__":
    test_formula_conversion()
