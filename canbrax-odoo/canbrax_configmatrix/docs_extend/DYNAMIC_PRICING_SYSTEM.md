# Dynamic Pricing System - Complete Implementation Guide

## Overview

The **Dynamic Pricing System** replaces the old static price grid fields (`mesh_price_grid_id`, `frame_price_grid_id`, `mulion_mohair_price_grid_id`, `plugh_price_grid_id`) with a flexible, condition-based pricing system that automatically selects and applies all applicable price matrices based on configuration values.

## Key Changes

### 1. Removed Static Price Grid Fields

**Before (Old System):**
```python
# Static price grid assignments
mesh_price_grid_id = fields.Many2one('config.matrix.price.matrix', "Mesh Grid")
frame_price_grid_id = fields.Many2one('config.matrix.price.matrix', "Frame Grid")
mulion_mohair_price_grid_id = fields.Many2one('config.matrix.price.matrix', "Mulion Mohair Grid")
plugh_price_grid_id = fields.Many2one('config.matrix.price.matrix', "Plugh Grid")
```

**After (New System):**
```python
# Dynamic Price Matrix System
# All price matrices with is_sale_price_matrix=True and belonging to this template
# will be automatically used for pricing based on their conditions
```

### 2. New Dynamic Pricing Logic

The system now automatically:
- **Finds all price matrices** with `is_sale_price_matrix = True` belonging to the template
- **Evaluates conditions** for each matrix against configuration values
- **Applies all matching matrices** (not just the first one)
- **Processes matrices without conditions** as always applicable

## Implementation Details

### 1. Core Helper Method

```python
def _get_applicable_price_matrices(self, configuration_values):
    """Get all applicable price matrices for this template based on conditions"""
    self.ensure_one()
    
    # Get all price matrices with is_sale_price_matrix=True that belong to this template
    price_matrices = self.env['config.matrix.price.matrix'].search([
        ('is_sale_price_matrix', '=', True),
        ('active', '=', True),
        ('matrix_id', '=', self.id)  # Belongs to this template
    ], order='sequence ASC')
    
    applicable_matrices = []
    
    for matrix in price_matrices:
        try:
            # Check if matrix has conditions
            has_conditions = bool(matrix.special_conditions and matrix.special_conditions.strip())
            
            if not has_conditions:
                # No conditions - always applicable
                applicable_matrices.append({
                    'matrix': matrix,
                    'height_field': matrix.height_calculated_field_id.name if matrix.height_calculated_field_id else None,
                    'width_field': matrix.width_calculated_field_id.name if matrix.width_calculated_field_id else None,
                    'type': 'general',
                    'has_conditions': False
                })
            else:
                # Has conditions - evaluate them
                if matrix.evaluate_special_conditions(configuration_values, ...):
                    applicable_matrices.append({
                        'matrix': matrix,
                        'height_field': matrix.height_calculated_field_id.name if matrix.height_calculated_field_id else None,
                        'width_field': matrix.width_calculated_field_id.name if matrix.width_calculated_field_id else None,
                        'type': 'general',
                        'has_conditions': True
                    })
        except Exception as e:
            _logger.warning(f"Error evaluating matrix '{matrix.name}': {str(e)}")
            continue
    
    return applicable_matrices
```

### 2. Updated Pricing Method

```python
def get_configuration_price(self, configuration_values):
    """Get total price for a configuration using dynamic price matrix selection"""
    self.ensure_one()
    
    # Get all applicable price matrices using the new dynamic system
    price_matrix_configs = self._get_applicable_price_matrices(configuration_values)
    
    if not price_matrix_configs:
        return {
            'price': 0.0,
            'currency_id': currency.id,
            'breakdown': []
        }

    # Process each applicable price matrix
    for config in price_matrix_configs:
        price_matrix = config['matrix']
        height_field = config['height_field']
        width_field = config['width_field']
        matrix_type = config['type']
        has_conditions = config['has_conditions']
        
        # Extract dimensions and calculate price
        height = self._extract_dimension_value(configuration_values, [height_field], 'height')
        width = self._extract_dimension_value(configuration_values, [width_field], 'width')
        
        if height and width:
            price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier * panel_quantity
            
            if price:
                total_price += price
                breakdown.append({
                    'matrix_name': price_matrix.name,
                    'price': price,
                    'dimensions': {'height': height, 'width': width},
                    'matrix_type': matrix_type,
                    'height_field': height_field,
                    'width_field': width_field,
                    'pricing_source': 'price_matrix',
                    'has_conditions': has_conditions
                })

    # Sales Fixed Pricing: Process as separate step (not as fallback)
    sales_fixed_pricing_result = self._get_sales_fixed_pricing(configuration_values)
    # ... process sales fixed pricing ...
    
    return {
        'price': total_price,
        'currency_id': currency.id,
        'breakdown': breakdown
    }
```

## Configuration Requirements

### 1. Price Matrix Setup

Each price matrix must be configured with:

```python
# Required fields
is_sale_price_matrix = True          # Must be True to be considered
active = True                        # Must be active
matrix_id = template_id              # Must belong to the template

# Optional fields
special_conditions = "door_height >= 2000"  # JavaScript condition (optional)
sequence = 10                        # Processing order
```

### 2. Template Configuration

Templates no longer need specific price grid assignments. Instead:

```python
# Template automatically uses all applicable matrices
# No specific price grid fields needed
# Computed fields show matrix statistics
has_price_matrices = fields.Boolean(compute='_compute_matrix_info')
price_matrix_count = fields.Integer(compute='_compute_matrix_info')
```

## Pricing Flow

### 1. Matrix Discovery
```
1. Find all price matrices with is_sale_price_matrix=True
2. Filter by template (matrix_id = template.id)
3. Order by sequence ASC
```

### 2. Condition Evaluation
```
For each matrix:
├── No special_conditions → Always applicable
└── Has special_conditions → Evaluate against configuration values
    ├── Condition passes → Matrix applicable
    └── Condition fails → Matrix not applicable
```

### 3. Price Calculation
```
For each applicable matrix:
├── Extract dimensions (height, width)
├── Look up price in matrix
├── Apply quantity multipliers
└── Add to total price and breakdown
```

### 4. Sales Fixed Pricing
```
Process as separate step:
├── Find all sales fixed pricing rules for template
├── Evaluate conditions for each rule
├── Sum all matching rules
└── Add to total price and breakdown
```

## Benefits

### 1. Flexibility
- **No hardcoded matrix assignments** - all matrices are considered dynamically
- **Condition-based selection** - matrices are selected based on configuration values
- **Multiple matrices** - all applicable matrices are used, not just one

### 2. Maintainability
- **Centralized logic** - all pricing logic in one place
- **Easy to add new matrices** - just create with `is_sale_price_matrix=True`
- **No template changes** - new matrices automatically considered

### 3. Performance
- **Efficient queries** - single query to find all applicable matrices
- **Early termination** - stops processing on errors
- **Caching support** - can be easily cached if needed

### 4. Debugging
- **Comprehensive logging** - detailed logs for each step
- **Test methods** - `test_dynamic_pricing_evaluation()` for debugging
- **Breakdown information** - detailed pricing breakdown

## Usage Examples

### 1. Basic Matrix (No Conditions)

```python
# Create a matrix that always applies
matrix = env['config.matrix.price.matrix'].create({
    'name': 'Standard Door Pricing',
    'is_sale_price_matrix': True,
    'matrix_id': template.id,
    'special_conditions': '',  # No conditions - always applies
    'height_ranges': '[...]',
    'width_ranges': '[...]',
    'matrix_data': '{"1200_800": 150.0, ...}'
})
```

### 2. Conditional Matrix

```python
# Create a matrix with conditions
matrix = env['config.matrix.price.matrix'].create({
    'name': 'Premium Door Pricing',
    'is_sale_price_matrix': True,
    'matrix_id': template.id,
    'special_conditions': 'door_height >= 2000 and door_type === "sliding"',
    'height_ranges': '[...]',
    'width_ranges': '[...]',
    'matrix_data': '{"1200_800": 200.0, ...}'
})
```

### 3. Testing Dynamic Pricing

```python
# Test the dynamic pricing system
template = env['config.matrix.template'].browse(template_id)
test_result = template.test_dynamic_pricing_evaluation({
    'door_height': 2500,
    'door_width': 1200,
    'door_type': 'sliding'
})

print(f"Available matrices: {len(test_result['available_price_matrices'])}")
print(f"Applicable matrices: {len(test_result['applicable_matrices'])}")
print(f"Total price: {test_result['total_price']}")
```

## Migration from Old System

### 1. Data Migration

If you have existing templates with the old price grid fields:

```python
# Migration script (if needed)
def migrate_to_dynamic_pricing(cr, version):
    """Migrate old price grid assignments to dynamic system"""
    
    # Find all templates with old price grid assignments
    cr.execute("""
        SELECT id, mesh_price_grid_id, frame_price_grid_id, 
               mulion_mohair_price_grid_id, plugh_price_grid_id
        FROM config_matrix_template
        WHERE mesh_price_grid_id IS NOT NULL 
           OR frame_price_grid_id IS NOT NULL
           OR mulion_mohair_price_grid_id IS NOT NULL
           OR plugh_price_grid_id IS NOT NULL
    """)
    
    for template_id, mesh_id, frame_id, mulion_id, plugh_id in cr.fetchall():
        # Update matrices to be sale price matrices
        matrix_ids = [id for id in [mesh_id, frame_id, mulion_id, plugh_id] if id]
        
        if matrix_ids:
            cr.execute("""
                UPDATE config_matrix_price_matrix 
                SET is_sale_price_matrix = true
                WHERE id IN %s
            """, (tuple(matrix_ids),))
```

### 2. Template Updates

Templates automatically work with the new system - no changes needed.

### 3. View Updates

The template form view has been updated to show information about the dynamic pricing system instead of the old price grid fields.

## Testing

### 1. Unit Testing

```python
def test_dynamic_pricing_system(self):
    """Test the dynamic pricing system"""
    
    # Create test template
    template = self.env['config.matrix.template'].create({
        'name': 'Test Template',
        'code': 'TEST001',
        'product_template_id': self.env['product.template'].create({'name': 'Test Product'}).id
    })
    
    # Create test matrices
    matrix1 = self.env['config.matrix.price.matrix'].create({
        'name': 'Always Apply Matrix',
        'is_sale_price_matrix': True,
        'matrix_id': template.id,
        'special_conditions': '',  # No conditions
        'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
        'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
        'matrix_data': '{"3000_3000": 100.0}'
    })
    
    matrix2 = self.env['config.matrix.price.matrix'].create({
        'name': 'Conditional Matrix',
        'is_sale_price_matrix': True,
        'matrix_id': template.id,
        'special_conditions': 'door_height >= 2000',
        'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
        'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
        'matrix_data': '{"3000_3000": 150.0}'
    })
    
    # Test pricing
    result = template.get_configuration_price({
        'door_height': 2500,
        'door_width': 1200
    })
    
    # Both matrices should apply
    self.assertEqual(len(result['breakdown']), 2)
    self.assertEqual(result['price'], 250.0)  # 100 + 150
```

### 2. Integration Testing

```python
def test_dynamic_pricing_integration(self):
    """Test dynamic pricing with sales fixed pricing"""
    
    # Test with sales fixed pricing
    result = template.get_configuration_price({
        'door_height': 2500,
        'door_width': 1200
    })
    
    # Should include both matrix pricing and fixed pricing
    matrix_prices = [item for item in result['breakdown'] if item['pricing_source'] == 'price_matrix']
    fixed_prices = [item for item in result['breakdown'] if item['pricing_source'] == 'sales_fixed_pricing']
    
    self.assertGreater(len(matrix_prices), 0)
    # Fixed pricing depends on configuration
```

## Troubleshooting

### 1. No Matrices Applied

**Symptoms**: No price matrices are being used for pricing
**Causes**:
- No matrices with `is_sale_price_matrix=True`
- Matrices don't belong to the template (`matrix_id` not set)
- All matrices have conditions that fail

**Solutions**:
```python
# Check matrix configuration
matrices = env['config.matrix.price.matrix'].search([
    ('is_sale_price_matrix', '=', True),
    ('active', '=', True),
    ('matrix_id', '=', template.id)
])
print(f"Found {len(matrices)} matrices for template {template.id}")

# Test each matrix
for matrix in matrices:
    result = matrix.test_special_conditions(configuration_values)
    print(f"Matrix {matrix.name}: {result['evaluation_result']}")
```

### 2. Wrong Prices

**Symptoms**: Prices don't match expected values
**Causes**:
- Wrong dimension fields configured
- Matrix data incorrect
- Quantity multipliers not applied

**Solutions**:
```python
# Test pricing calculation
result = template.test_dynamic_pricing_evaluation(configuration_values)
print(f"Pricing breakdown: {result['pricing_breakdown']}")

# Check dimension extraction
height = template._extract_dimension_value(configuration_values, [height_field], 'height')
width = template._extract_dimension_value(configuration_values, [width_field], 'width')
print(f"Dimensions: height={height}, width={width}")
```

### 3. Performance Issues

**Symptoms**: Slow pricing calculations
**Causes**:
- Too many matrices to evaluate
- Complex condition expressions
- Inefficient database queries

**Solutions**:
```python
# Limit matrices if needed
matrices = matrices[:10]  # Limit to 10 matrices

# Add caching
@tools.ormcache('self.id', 'config_hash')
def _get_applicable_price_matrices_cached(self, configuration_values):
    return self._get_applicable_price_matrices_uncached(configuration_values)
```

## Future Enhancements

### 1. Caching
- Add `@tools.ormcache` for expensive operations
- Cache matrix evaluation results
- Cache applicable matrices per template

### 2. Performance Optimization
- Batch matrix evaluation
- Parallel processing for multiple matrices
- Database query optimization

### 3. Advanced Features
- Matrix priority system
- Matrix dependencies
- Matrix versioning
- A/B testing for matrices

### 4. Analytics
- Track which matrices are used most
- Monitor pricing performance
- Analyze condition effectiveness

## Conclusion

The Dynamic Pricing System provides a flexible, maintainable, and powerful solution for handling complex pricing scenarios in the ConfigMatrix system. By automatically selecting and applying all applicable price matrices based on configuration values, it eliminates the need for hardcoded matrix assignments while providing comprehensive pricing capabilities.

The system is backward compatible, easy to use, and provides excellent debugging and testing capabilities. It represents a significant improvement over the old static price grid system while maintaining all existing functionality.

---

**Document Created**: 2024-12-19  
**Version**: 1.0  
**Status**: Implementation Complete  
**Next Update**: After Performance Optimization Implementation
