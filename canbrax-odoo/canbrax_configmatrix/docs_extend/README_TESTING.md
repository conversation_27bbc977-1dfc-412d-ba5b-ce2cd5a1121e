# ConfigMatrix Testing Guide - Quick Start

## Overview

This directory contains comprehensive testing documentation and tools for the ConfigMatrix system. The testing framework covers unit tests, integration tests, user acceptance tests, and performance testing.

## 📚 Documentation Files

### 1. **TESTING_GUIDE.md** - Complete Testing Guide
- **Purpose**: Comprehensive testing procedures and best practices
- **Audience**: <PERSON><PERSON><PERSON>, QA Engineers, System Administrators
- **Content**: 
  - Testing environment setup
  - Unit testing framework
  - Integration testing
  - User acceptance testing
  - Performance testing
  - Test data management
  - Testing best practices
  - Common test scenarios
  - Troubleshooting guide

### 2. **TESTING_CHECKLIST.md** - Quick Reference Checklist
- **Purpose**: Step-by-step testing checklist for quick reference
- **Audience**: Testers, Developers, Project Managers
- **Content**:
  - Pre-testing setup checklist
  - Unit testing checklist
  - Integration testing checklist
  - User acceptance testing checklist
  - Performance testing checklist
  - Security testing checklist
  - Error handling checklist
  - Mobile and responsive testing checklist
  - Browser compatibility checklist
  - Emergency testing procedures

### 3. **README_TESTING.md** - This File
- **Purpose**: Quick start guide and navigation
- **Audience**: New users, developers getting started
- **Content**: Overview, quick start, file descriptions

## 🚀 Quick Start

### 1. Set Up Testing Environment

```bash
# Navigate to the ConfigMatrix module
cd canbrax-odoo/canbrax_configmatrix

# Set up test environment
python run_tests.py --setup

# Verify environment is ready
python run_tests.py --help
```

### 2. Run Your First Test

```bash
# Run all tests
python run_tests.py

# Run only unit tests
python run_tests.py --unit

# Run with verbose output
python run_tests.py --verbose
```

### 3. Run Specific Test Types

```bash
# Integration tests
python run_tests.py --integration

# Performance tests
python run_tests.py --performance

# Tests from specific file
python run_tests.py --file test_manufacturing_order_creation
```

## 🛠️ Testing Tools

### Test Runner Script (`run_tests.py`)

The `run_tests.py` script provides an easy way to run tests without remembering complex Odoo commands.

**Features:**
- Automatic test database management
- Multiple test type selection
- Verbose output options
- Environment setup and cleanup
- Test result parsing and reporting

**Usage Examples:**
```bash
# Basic usage
python run_tests.py

# Run specific test types
python run_tests.py --unit --verbose
python run_tests.py --integration
python run_tests.py --performance

# Environment management
python run_tests.py --setup
python run_tests.py --cleanup

# Run tests from specific file
python run_tests.py --file test_calculated_fields
```

## 📋 Testing Workflow

### Phase 1: Environment Setup
1. **Read the Testing Guide**: Start with `TESTING_GUIDE.md` for comprehensive understanding
2. **Set up Environment**: Use `python run_tests.py --setup`
3. **Verify Setup**: Check that test database is accessible

### Phase 2: Test Execution
1. **Start with Unit Tests**: `python run_tests.py --unit`
2. **Run Integration Tests**: `python run_tests.py --integration`
3. **Execute Performance Tests**: `python run_tests.py --performance`
4. **Run All Tests**: `python run_tests.py`

### Phase 3: Analysis and Reporting
1. **Review Test Results**: Check output for failures and errors
2. **Use Checklist**: Reference `TESTING_CHECKLIST.md` for comprehensive coverage
3. **Document Issues**: Log any problems found during testing
4. **Clean Up**: Use `python run_tests.py --cleanup` when done

## 🎯 Testing Priorities

### High Priority (Run First)
- **Unit Tests**: Core functionality validation
- **Integration Tests**: Module interaction verification
- **Security Tests**: Access control and data protection

### Medium Priority (Run Regularly)
- **Performance Tests**: System performance validation
- **User Acceptance Tests**: User workflow verification
- **Error Handling Tests**: Exception and error scenario testing

### Low Priority (Run as Needed)
- **Mobile Testing**: Responsive design validation
- **Browser Compatibility**: Cross-browser functionality
- **Data Migration Tests**: Import/export functionality

## 🔧 Configuration

### Environment Variables

Set these environment variables for custom database configuration:

```bash
export DB_USER=your_postgres_user
export DB_PASSWORD=your_postgres_password
export DB_HOST=localhost
export DB_PORT=5432
```

### Test Configuration

The test runner automatically creates a `test.conf` file with these settings:

```ini
[options]
addons_path = ./canbrax_configmatrix
db_host = localhost
db_port = 5432
db_user = postgres
db_password = 
db_name = canbrax_test
test_enable = True
test_tags = canbrax
log_level = test
```

## 📊 Test Results Interpretation

### Success Indicators
- ✅ **All tests pass**: System is working correctly
- ✅ **Performance benchmarks met**: System meets performance requirements
- ✅ **No security vulnerabilities**: System is secure
- ✅ **User workflows functional**: System is user-friendly

### Warning Signs
- ⚠️ **Some tests fail**: Investigate failures immediately
- ⚠️ **Performance degradation**: Check for recent changes
- ⚠️ **Security warnings**: Review access controls
- ⚠️ **User experience issues**: Validate UI/UX changes

### Critical Issues
- ❌ **Multiple test failures**: System may be unstable
- ❌ **Security test failures**: Immediate security review required
- ❌ **Performance test failures**: System may not meet requirements
- ❌ **Integration test failures**: Module dependencies may be broken

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Problems
```bash
# Check PostgreSQL service
sudo systemctl status postgresql

# Verify database exists
psql -h localhost -U postgres -l | grep canbrax_test

# Create database manually if needed
createdb -h localhost -U postgres canbrax_test
```

#### 2. Test Execution Failures
```bash
# Check Odoo installation
python -c "import odoo; print('Odoo found')"

# Verify module path
ls -la canbrax_configmatrix/

# Check Python dependencies
pip list | grep odoo
```

#### 3. Performance Issues
```bash
# Monitor system resources
htop
iostat 1
free -h

# Check database performance
pg_stat_statements
```

### Getting Help

1. **Check the Testing Guide**: `TESTING_GUIDE.md` contains detailed troubleshooting
2. **Review Test Output**: Use `--verbose` flag for detailed information
3. **Check Logs**: Review Odoo logs for error details
4. **Verify Environment**: Ensure all prerequisites are met

## 📈 Continuous Testing

### Development Workflow
1. **Write Tests First**: Follow TDD principles
2. **Run Tests Frequently**: Test after each change
3. **Automate Testing**: Integrate with CI/CD pipelines
4. **Monitor Results**: Track test success rates over time

### CI/CD Integration
The testing framework is designed to integrate with CI/CD systems:

```yaml
# GitHub Actions example
- name: Run ConfigMatrix Tests
  run: |
    cd canbrax_configmatrix
    python run_tests.py --verbose
```

## 📚 Additional Resources

### Odoo Testing Documentation
- [Odoo Testing Guide](https://www.odoo.com/documentation/16.0/developer/reference/testing.html)
- [Odoo Test Framework](https://github.com/odoo/odoo/tree/master/odoo/tests)

### Python Testing Resources
- [Python Testing Best Practices](https://docs.python-guide.org/writing/tests/)
- [pytest Documentation](https://docs.pytest.org/)

### ConfigMatrix Documentation
- [Developer Guide](06_DEVELOPER_GUIDE.md)
- [Data Models](01_DATA_MODELS.md)
- [User Guide](07_USER_GUIDE.md)

## 🤝 Contributing to Testing

### Adding New Tests
1. **Follow Naming Convention**: Use `test_` prefix for test methods
2. **Use Test Classes**: Group related tests in test classes
3. **Follow AAA Pattern**: Arrange, Act, Assert
4. **Document Tests**: Add clear docstrings explaining test purpose

### Test File Structure
```
tests/
├── __init__.py
├── test_config_matrix_template.py
├── test_config_matrix_configuration.py
├── test_manufacturing_order_creation.py
├── test_mesh_operations.py
├── test_calculated_fields.py
└── test_formula_conversion.py
```

### Test Naming Convention
- **Test Classes**: `TestClassName` (e.g., `TestConfigMatrixTemplate`)
- **Test Methods**: `test_method_name` (e.g., `test_template_creation`)
- **Test Files**: `test_module_name.py` (e.g., `test_config_matrix_template.py`)

## 📞 Support

### Getting Help
1. **Check Documentation**: Start with the testing guide
2. **Review Examples**: Look at existing test files
3. **Use Checklist**: Follow the testing checklist
4. **Run with Verbose**: Use `--verbose` flag for detailed output

### Reporting Issues
When reporting testing issues, include:
- Test command used
- Error messages and stack traces
- Environment details (OS, Python version, Odoo version)
- Steps to reproduce the issue
- Expected vs. actual behavior

---

**Remember**: Regular testing ensures the ConfigMatrix system remains reliable, secure, and performant. Use this testing framework to maintain high quality standards throughout development and deployment.
