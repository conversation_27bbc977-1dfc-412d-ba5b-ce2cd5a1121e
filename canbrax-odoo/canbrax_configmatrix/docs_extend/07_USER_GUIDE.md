# ConfigMatrix User Guide

## Overview

This user guide provides comprehensive instructions for using the ConfigMatrix system across all user roles and use cases. It covers administrative setup, sales configuration, manufacturing integration, and customer portal usage.

## User Roles and Access

### 1. ConfigMatrix Administrator
**Access Level**: Full administrative access
**Responsibilities**:
- Create and manage configuration templates
- Set up sections, fields, and options
- Configure pricing matrices and labor calculations
- Manage component mappings and BOM generation
- Monitor system performance and usage

### 2. ConfigMatrix User
**Access Level**: Basic configuration access
**Responsibilities**:
- Use existing configuration templates
- Create and save configurations
- Generate BOMs and pricing
- View configuration history

### 3. Builder Portal User
**Access Level**: Builder-specific portal access
**Responsibilities**:
- Access builder-specific configuration interface
- View and manage builder configurations
- Access specialized tools and features

### 4. Portal User (Customer)
**Access Level**: Customer-facing portal access
**Responsibilities**:
- Configure products through customer portal
- View pricing and availability
- Save and retrieve configurations

## Administrative Setup

### 1. Creating Configuration Templates

#### Step 1: Create Template
1. Navigate to **ConfigMatrix > Configuration > Templates**
2. Click **Create** to add a new template
3. Fill in the basic information:
   - **Template Name**: Descriptive name for the template
   - **Template Code**: Unique identifier (e.g., DOOR001)
   - **Product Template**: Select the configurable product
   - **Use Case**: Primary use case (Check Measure, Sales, Online)
   - **Description**: Detailed description of the template

#### Step 2: Set Template State
- **Draft**: Template is being created and configured
- **Testing**: Template is being tested with sample data
- **Active**: Template is available for use
- **Archived**: Template is no longer in use

#### Step 3: Add Sections
1. In the template form, go to the **Sections** tab
2. Click **Add a line** to create a new section
3. Configure each section:
   - **Name**: Section name (e.g., "Door", "Hardware", "Extrusions")
   - **Sequence**: Display order
   - **Description**: Section description

### 2. Configuring Fields

#### Step 1: Add Fields to Sections
1. In a section, go to the **Fields** tab
2. Click **Add a line** to create a new field
3. Configure the field properties:
   - **Field Name**: User-friendly name
   - **Technical Name**: Internal identifier
   - **Field Type**: Text, Number, Selection, Boolean, Date
   - **Required**: Whether the field is mandatory
   - **Default Value**: Default value for the field
   - **Help Text**: Help text for users

#### Step 2: Configure Advanced Features
1. **Visibility Conditions**: Expression to show/hide field
   - Example: `door_type == "sliding"`
   - Example: `width >= 800 and width <= 1200`

2. **Dynamic Help Templates**: Context-sensitive help
   - Check Measure: `"Width should be {door_height} + 100mm"`
   - Sales: `"Standard width for {door_type} doors"`
   - Online: `"Enter width in millimeters"`

3. **Dynamic Error Messages**: Custom validation messages
   - Example: `"Width must be greater than {min_width}mm"`

4. **Dynamic Default Values**: Intelligent defaults
   - Example: `door_height * 0.8`

5. **Range Validation**: Min/max values with expressions
   - Min Value: `door_height * 0.5`
   - Max Value: `door_height * 1.2`

#### Step 3: Add Options for Selection Fields
1. For selection fields, go to the **Options** tab
2. Click **Add a line** to create options
3. Configure each option:
   - **Name**: Option display name
   - **Value**: Option value
   - **Sequence**: Display order

### 3. Setting Up Component Mappings

#### Step 1: Configure Component Mappings
1. In a field, go to the **Component Mappings** tab
2. Click **Add a line** to create mappings
3. Configure the mapping:
   - **Component Product**: Product to include in BOM
   - **Quantity**: Fixed quantity or formula
   - **Quantity Formula**: Expression for dynamic quantities
     - Example: `width / 1000` (quantity per meter)

#### Step 2: Configure Field Component Mappings
1. For 1:many relationships, use **Field Component Mappings**
2. Configure dynamic components:
   - **Is Dynamic**: Enable dynamic component selection
   - **Reference Value**: Value to match against
   - **Filter Domain**: Product filter expression
     - Example: `[('name', 'ilike', reference_value)]`

### 4. Setting Up Dynamic Pricing System

#### Step 1: Create Price Matrices
1. Navigate to **ConfigMatrix > Pricing > Price Matrices**
2. Click **Create** to add a new matrix
3. Configure the matrix:
   - **Name**: Matrix name (e.g., "Door Frame Pricing")
   - **Product Template**: Select the configurable product
   - **Is Sale Price Matrix**: Check this box for dynamic pricing
   - **Sales Price Application**: Select application type (mullion_mohair, plugh)
   - **Height Calculated Field**: Select height field reference
   - **Width Calculated Field**: Select width field reference
   - **Height Ranges**: JSON array of height ranges
   - **Width Ranges**: JSON array of width ranges
   - **Matrix Data**: JSON pricing matrix data
   - **Special Conditions**: Optional conditions for matrix selection

#### Step 2: Configure Matrix Data
1. Use the matrix editor to set prices
2. Configure ranges in JSON format:
```json
[
  {"min": 0, "max": 1000, "label": "small"},
  {"min": 1001, "max": 2000, "label": "medium"},
  {"min": 2001, "max": 3000, "label": "large"}
]
```

#### Step 3: Set Up Special Conditions (Optional)
1. Add special conditions to control when matrix is used
2. Use JavaScript-like expressions:
   - Example: `door_type === "sliding" && width >= 800`
   - Example: `has_midrail === true && height > 2000`
3. Matrices without conditions are always applicable

#### Step 4: Set Up Sales Fixed Pricing (Additional Layer)
1. Navigate to **ConfigMatrix > Pricing > Sales Fixed Pricing**
2. Click **Create** to add fixed pricing rules
3. Configure the rule:
   - **Template**: Select configuration template
   - **Description**: Rule description
   - **Fixed Price**: Price amount
   - **Condition**: JavaScript expression for when to apply
   - **Sequence**: Display order

#### Step 5: Set Up Labor Time Matrix
1. Create labor time matrices for manufacturing
2. Configure labor times and costs
3. Set cost per hour for calculations

### 5. Adding Visual Components

#### Step 1: Create SVG Components
1. In the template, go to the **Visual Components** tab
2. Click **Add a line** to create visual components
3. Configure the component:
   - **Name**: Component name
   - **SVG Content**: Vector graphics content
   - **Layer Name**: Layer identifier
   - **Visibility Condition**: When to show the layer

#### Step 2: Configure Conditional Layers
1. Set visibility conditions for layers
2. Use expressions to show/hide based on configuration
3. Test visual components with different configurations

## Sales Configuration Workflow

### 1. Starting a Configuration

#### Step 1: Select Configurable Product
1. In a sales order, add a configurable product
2. The "Configure" button appears next to the product line
3. Click **Configure** to open the ConfigMatrix form

#### Step 2: Configure Product
1. The configuration form opens with relevant questions
2. Questions appear based on the selected use case
3. Answer questions as they appear
4. Use dynamic help text for guidance

#### Step 3: Real-time Updates
1. As you answer questions, other questions appear/disappear
2. Visual preview updates in real-time
3. Pricing updates automatically
4. Validation errors appear immediately

### 2. Using Dynamic Features

#### Dynamic Help Text
- Help text changes based on your answers
- Example: "Width should be 2100mm" (based on door height)
- Use placeholders like `{door_height}` for dynamic values

#### Dynamic Error Messages
- Error messages are context-sensitive
- Example: "Width must be greater than 800mm for sliding doors"
- Messages include actual values from your configuration

#### Dynamic Default Values
- Default values are calculated automatically
- Example: Door width defaults to 80% of door height
- Defaults update when related fields change

### 3. Validation and Error Handling

#### Real-time Validation
- Fields validate as you type
- Error messages appear immediately
- Invalid values are highlighted

#### Range Validation
- Min/max values are enforced
- Ranges can be dynamic based on other fields
- Example: Width must be between 50% and 120% of height

#### Required Field Validation
- Required fields must be completed
- Validation occurs before saving
- Clear error messages guide completion

### 4. Saving and Applying Configuration

#### Step 1: Save Configuration
1. Click **Save Configuration** when complete
2. Configuration is saved with all field values
3. BOM is automatically generated
4. Pricing is calculated and applied

#### Step 2: Review Results
1. Review the generated BOM
2. Check calculated pricing
3. Verify labor time calculations
4. Review configuration summary

#### Step 3: Apply to Sales Order
1. Configuration is applied to the sales order line
2. Product price is updated with configuration pricing
3. BOM is attached for manufacturing
4. Configuration can be modified later

## Manufacturing Integration

### 1. BOM Generation

#### Automatic BOM Creation
1. When configuration is saved, BOM is generated automatically
2. Components are added based on field values and options
3. Quantities are calculated using formulas
4. BOM includes all necessary components

#### Component Quantities
- Fixed quantities: Set in component mapping
- Formula-based quantities: Calculated dynamically
- Example: `width / 1000` for quantity per meter
- Example: `height * width / 1000000` for area-based quantities

#### Dynamic Component Selection
- Components can be selected dynamically
- Based on field values and filter conditions
- Example: Select hardware based on door type
- Example: Select extrusions based on dimensions

### 2. Manufacturing Order Creation

#### Automatic MO Generation
1. When sales order is confirmed, manufacturing orders are created
2. Configured BOMs are used for component lists
3. Labor times are calculated from matrices
4. Manufacturing orders include configuration details

#### Labor Time Calculations
1. Labor times are calculated from matrices
2. Based on product dimensions and complexity
3. Costs are calculated using hourly rates
4. Labor is included in manufacturing planning

### 3. Quality Control Integration

#### Configuration Tracking
1. Manufacturing orders include configuration details
2. Quality control can reference original configuration
3. Configuration history is maintained
4. Changes are tracked and logged

## Customer Portal Usage

### 1. Portal Configuration Interface

#### Accessing Configuration
1. Log into customer portal
2. Navigate to product catalog
3. Select configurable products
4. Click "Configure" to start configuration

#### Simplified Interface
1. Interface is optimized for customer use
2. Simplified language and instructions
3. Real-time pricing updates
4. Mobile-responsive design

### 2. Customer Configuration Features

#### Real-time Pricing
1. Pricing updates as configuration changes
2. No hidden costs or surprises
3. Clear pricing breakdown
4. Currency and tax calculations

#### Configuration History
1. Save configurations for future reference
2. Retrieve and modify saved configurations
3. Share configurations with others
4. Export configuration details

#### Validation and Guidance
1. Real-time validation prevents errors
2. Help text guides customers
3. Error messages are customer-friendly
4. Default values simplify configuration

### 3. Order Placement

#### Configuration to Order
1. Complete configuration and add to cart
2. Review configuration summary
3. Proceed to checkout
4. Configuration details included in order

#### Order Tracking
1. Track order status in portal
2. View configuration details
3. Access manufacturing progress
4. Download configuration documents

## Builder Portal Features

### 1. Builder-Specific Interface

#### Specialized Tools
1. Advanced configuration options
2. Professional measurement tools
3. Technical specifications
4. Engineering calculations

#### Builder Workflows
1. Check measure configurations
2. Site survey integration
3. Installation planning
4. Project management

### 2. Professional Features

#### Advanced Calculations
1. Complex formula support
2. Engineering specifications
3. Material calculations
4. Cost analysis tools

#### Project Management
1. Multiple configuration management
2. Project timelines
3. Resource planning
4. Installation scheduling

## Advanced Features

### 1. Calculated Fields

#### Formula-based Fields
1. Create computed fields using formulas
2. Use field values in calculations
3. Example: `width * height / 1000000` for area
4. Example: `(width + height) * 2` for perimeter

#### Dependency Tracking
1. Calculated fields update automatically
2. Dependencies are tracked efficiently
3. Performance optimized with caching
4. Circular dependency detection

### 2. Import/Export Tools

#### Excel Import/Export
1. Import configuration templates from Excel
2. Export configurations to Excel
3. Bulk data management
4. Template sharing

#### JSON Import/Export
1. Export configurations as JSON
2. Import configurations from JSON
3. API integration support
4. Data migration tools

### 3. Performance Optimization

#### Caching
1. Template data is cached for performance
2. Visibility calculations are cached
3. Computed fields use efficient caching
4. Large configurations are optimized

#### Debouncing
1. UI updates are debounced for performance
2. Expensive operations are optimized
3. Real-time updates are smooth
4. Memory usage is managed efficiently

## Troubleshooting

### 1. Common Issues

#### Configuration Not Saving
- Check required fields are completed
- Verify validation rules are met
- Ensure user has proper permissions
- Check for system errors in logs

#### BOM Not Generating
- Verify component mappings are configured
- Check product availability
- Ensure formulas are valid
- Review error logs for details

#### Pricing Not Calculating
- Verify price matrices have `is_sale_price_matrix=True`
- Check matrices belong to the correct template
- Ensure special conditions are valid (if used)
- Verify sales fixed pricing rules are configured
- Check dimension ranges are correct
- Ensure labor time matrices are set up
- Review pricing calculations and logs

#### Visibility Issues
- Check visibility conditions are valid
- Verify field dependencies are correct
- Test conditions with sample data
- Review expression syntax

### 2. Performance Issues

#### Slow Configuration Loading
- Check template complexity
- Review field dependencies
- Optimize visibility conditions
- Consider caching strategies

#### Memory Usage
- Monitor large configurations
- Optimize calculated fields
- Review component mappings
- Implement cleanup procedures

### 3. User Experience Issues

#### Interface Problems
- Check browser compatibility
- Verify JavaScript is enabled
- Clear browser cache
- Test with different browsers

#### Mobile Responsiveness
- Test on mobile devices
- Check responsive design
- Verify touch interactions
- Optimize for small screens

## Best Practices

### 1. Template Design

#### Field Organization
- Group related fields in sections
- Use logical field ordering
- Provide clear field names
- Include helpful descriptions

#### Validation Rules
- Set appropriate required fields
- Use meaningful validation messages
- Test validation with edge cases
- Provide clear error guidance

#### Component Mapping
- Map all necessary components
- Use formulas for dynamic quantities
- Test component selection
- Verify BOM accuracy

### 2. User Experience

#### Interface Design
- Use clear, simple language
- Provide helpful guidance
- Include visual feedback
- Optimize for user workflow

#### Error Handling
- Provide clear error messages
- Guide users to solutions
- Prevent common errors
- Validate input early

#### Performance
- Optimize for speed
- Use efficient calculations
- Implement proper caching
- Monitor performance metrics

### 3. Data Management

#### Template Maintenance
- Keep templates up to date
- Archive unused templates
- Document template changes
- Test template modifications

#### Configuration Management
- Backup important configurations
- Archive old configurations
- Monitor configuration usage
- Clean up unused data

#### Security
- Control access appropriately
- Validate user inputs
- Log security events
- Monitor system access

## Support and Resources

### 1. Documentation
- Technical documentation
- User guides and tutorials
- Video demonstrations
- FAQ and troubleshooting

### 2. Training
- User training sessions
- Administrator training
- Custom training programs
- Online training resources

### 3. Support
- Technical support
- User support
- Bug reporting
- Feature requests

### 4. Community
- User forums
- Knowledge sharing
- Best practices
- User feedback

This user guide provides comprehensive coverage of all ConfigMatrix features and workflows. Users should refer to specific sections based on their role and requirements.