# Field Value Normalization System Design

## Overview

This document outlines the design for a centralized field value normalization system to ensure consistent data types between UI and backend template calculations, resolving the $4.48 cost difference issue.

## Problem Statement

**Current Issue**: UI ($260.75, 53 operations) vs Backend ($256.27, 55 operations)
- **Root Cause**: Data type inconsistencies between UI (strings) and backend (mixed types)
- **Impact**: Same templates produce different costs due to formula evaluation differences

## Design Principles

1. **Centralized**: Single normalization point for all template calculations
2. **Type-Safe**: Consistent data types based on field definitions
3. **Backward Compatible**: No breaking changes to existing functionality
4. **Context-Aware**: Handle UI vs backend differences transparently
5. **Extensible**: Easy to add new field types and conversion rules

## Architecture

### 1. Core Normalization Service

**Location**: `models/config_matrix_field_normalizer.py`

```python
class ConfigMatrixFieldNormalizer(models.AbstractModel):
    _name = 'config.matrix.field.normalizer'
    _description = 'Field Value Normalization Service'
    
    def normalize_field_values(self, field_values, template_id, context_info=None):
        """
        Normalize field values for template calculations
        
        Args:
            field_values (dict): Raw field values from UI or backend
            template_id (int): Template ID for field type lookup
            context_info (dict): Context information (UI vs backend)
            
        Returns:
            dict: Normalized field values with consistent data types
        """
```

### 2. Field Type Detection

**Strategy**: Use template field definitions to determine expected data types

```python
def _get_field_type_mapping(self, template_id):
    """Get field type mapping for normalization"""
    template = self.env['config.matrix.template'].browse(template_id)
    field_mapping = {}
    
    for section in template.section_ids:
        for field in section.field_ids:
            # Map both field ID and technical name
            field_mapping[str(field.id)] = field.field_type
            field_mapping[field.technical_name] = field.field_type
    
    return field_mapping
```

### 3. Data Type Conversion Rules

**Supported Types**:
- `integer`: Convert to int, default 0
- `float`: Convert to float, default 0.0
- `boolean`: Convert to bool, handle string representations
- `selection`: Extract numeric values, handle option text
- `text`: Keep as string, extract numeric if needed
- `calculated`: Preserve existing values (already processed)

### 4. Integration Points

**Template Calculation Context**:
```python
# In config_matrix_operation_template.py get_calculated_cost()
def get_calculated_cost(self, config_values=None):
    # BEFORE: ctx = dict(config_values)
    # AFTER: Normalize first
    normalizer = self.env['config.matrix.field.normalizer']
    normalized_values = normalizer.normalize_field_values(
        config_values, 
        self.template_id.id,
        {'context_type': self._get_context_type()}
    )
    ctx = dict(normalized_values)
```

**UI Controller Integration**:
```python
# In configuration_controller.py calculate_operation_costs()
# Before calling template methods
normalizer = request.env['config.matrix.field.normalizer']
normalized_field_values = normalizer.normalize_field_values(
    field_values, 
    template_id,
    {'source': 'ui_field_option_mapping'}
)
cost_value = mapping.operation_template_id.get_calculated_cost(normalized_field_values)
```

**Backend Integration**:
```python
# In config_matrix_configuration.py
# Before operation calculations
normalized_config_values = self.env['config.matrix.field.normalizer'].normalize_field_values(
    config_values,
    self.template_id.id,
    {'source': 'backend_save_config'}
)
```

## Implementation Plan

### Phase 1: Core Normalizer
1. Create `ConfigMatrixFieldNormalizer` model
2. Implement basic type conversion methods
3. Add field type detection from template
4. Create comprehensive test cases

### Phase 2: Template Integration
1. Modify `get_calculated_cost()` to use normalizer
2. Modify `get_calculated_duration()` to use normalizer
3. Update context creation in operation templates
4. Add normalization debugging logs

### Phase 3: Controller Integration
1. Update UI controller to normalize before template calls
2. Update backend configuration to normalize before calculations
3. Ensure consistent field access patterns
4. Validate operation count consistency

### Phase 4: Testing & Validation
1. Test with existing templates
2. Verify UI/backend cost consistency
3. Performance impact assessment
4. Edge case handling

## Implementation Status

✅ **COMPLETED** - Field Value Normalization System has been successfully implemented!

### What Was Implemented

1. **Core Normalizer Service** (`config_matrix_field_normalizer.py`)
   - Centralized field value normalization
   - Support for integer, float, boolean, selection, and text field types
   - Comprehensive error handling and fallback mechanisms
   - Detailed logging for debugging

2. **Template Integration**
   - Modified `get_calculated_cost()` and `get_calculated_duration()` methods
   - Automatic normalization before formula evaluation
   - Context-aware normalization (UI vs backend)

3. **Controller Integration**
   - UI controller automatically benefits from template normalization
   - Backend configuration calls use `save_config=True` context
   - Consistent field value processing across all calculation paths

4. **Testing Framework**
   - Comprehensive unit tests in `test_field_normalization.py`
   - Integration tests in `test_normalization_consistency.py`
   - Edge case handling and performance testing

## Expected Outcomes

1. **Cost Consistency**: ✅ UI and backend now produce identical costs
2. **Operation Count Alignment**: ✅ Resolved 53 vs 55 operation difference
3. **Type Safety**: ✅ Eliminated formula evaluation errors from type mismatches
4. **Maintainability**: ✅ Centralized normalization logic

## Debugging Enhancements

The system includes comprehensive logging:

```python
[NORMALIZATION] Starting normalization for template 24
[NORMALIZATION] Input: 300 fields, source: ui_field_option_mapping
[NORMALIZATION] Found 45 field type mappings
[NORMALIZATION] Conversions: 12 fields converted
[NORMALIZATION] bx_dbl_hinge_quantity: "1" -> 1 (integer)
[NORMALIZATION] bx_dbl_hinge_door_height_mm: "2100" -> 2100.0 (float)
[NORMALIZATION] Output: 300 normalized fields
```

## Backward Compatibility

✅ **FULLY MAINTAINED**
- Existing templates continue to work without changes
- Automatic fallback to original behavior if normalization fails
- No changes to template formula syntax required
- Gradual rollout achieved through context detection

## Performance Considerations

✅ **OPTIMIZED**
- Field type mappings cached per template
- Minimal database queries during normalization
- Only calculated fields are processed (skip `_CALCULATED_` fields)
- Efficient error handling with fallbacks

## Usage Examples

### Automatic Usage (Recommended)
The normalization system works automatically when operation templates are called:

```python
# UI Controller - automatically normalized
cost = mapping.operation_template_id.with_context(ui_field_option_mapping=True).get_calculated_cost(field_values)

# Backend Configuration - automatically normalized
cost = mapping.operation_template_id.with_context(save_config=True).get_calculated_cost(config_values)
```

### Manual Usage (Advanced)
For custom scenarios, use the normalizer directly:

```python
normalizer = self.env['config.matrix.field.normalizer']
normalized_values = normalizer.normalize_field_values(
    field_values,
    template_id,
    {'source': 'custom_calculation'}
)
```

## Troubleshooting

### Common Issues

1. **Template ID Missing**: Normalizer falls back to original values
2. **Unknown Field Types**: Default to text normalization
3. **Invalid Values**: Use field type defaults (0, 0.0, False, '')

### Debug Logging

Enable detailed logging by checking Odoo logs for:
- `[NORMALIZATION]` - Field value conversions
- `[TEMPLATE_COST_DEBUG]` - Template calculations
- `[TEMPLATE_DURATION_DEBUG]` - Duration calculations
