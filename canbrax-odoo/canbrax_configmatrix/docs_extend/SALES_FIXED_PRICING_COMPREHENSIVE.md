# Sales Fixed Pricing System - Comprehensive Guide

## Overview

The **Sales Fixed Pricing** system is a conditional pricing rule system that provides an additional pricing layer for all components in the ConfigMatrix system. It allows administrators to define fixed pricing rules that apply when specific conditions are met, running as a separate step alongside regular pricing calculations.

## Key Features

- **Conditional Pricing**: Apply fixed prices based on configuration values
- **All Matching Rules**: ALL rules that meet their conditions are applied (not just first match)
- **Flexible Conditions**: Use JavaScript expressions that convert to Python for complex pricing logic
- **Template Integration**: Link pricing rules to specific product templates
- **Separate Step Processing**: Runs independently alongside matrix pricing
- **Automatic Application**: Rules are automatically evaluated during configuration
- **Cumulative Pricing**: Multiple rules can apply to the same configuration

## Architecture

### Core Components

#### 1. Sales Fixed Pricing Model (`config.matrix.sales.fixed.price`)
- **Purpose**: Stores conditional fixed pricing rules for configurable products
- **Integration**: Links to configuration templates and evaluates conditions dynamically
- **Separate Step**: Runs independently alongside matrix pricing

#### 2. Template Integration
- **Location**: Integrated into `config.matrix.template.get_configuration_price()`
- **Trigger**: Always runs as separate step after matrix pricing processing
- **Flow**: Evaluates conditions and applies fixed pricing as additional cost layer

#### 3. Pricing Flow Integration
```
Regular Grid Pricing → Special Condition Matrix → Sales Fixed Pricing (Separate Step)
     ↓                        ↓                        ↓
Grid lookup success    ALL matching matrices    Fixed pricing always runs
     ↓                        ↓                        ↓
Use grid price         Use all matching matrices    Sum all matching rules
     ↓                        ↓                        ↓
     └────────────────────────┼────────────────────────┘
                              ↓
                    Combined Pricing Result
```

## Data Model

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `sequence` | Integer | Display order (default: 10) | No |
| `active` | Boolean | Active status (default: True) | No |
| `template_id` | Many2one | Configuration template reference | Yes |
| `description` | Text | Detailed description of the pricing rule | No |
| `value_cost` | Float | Fixed price amount (digits: Product Price) | No |
| `condition` | Char | Primary condition expression (JavaScript syntax) | No |
| `code` | Char | Legacy condition field (fallback) | No |
| `company_id` | Many2one | Company reference (default: current company) | No |
| `currency_id` | Many2one | Currency (related to company) | No |

### Key Features

#### 1. Dual Condition Fields
- **`condition`**: Primary field for JavaScript expressions
- **`code`**: Legacy field for backward compatibility
- **Priority**: `condition` field takes precedence over `code`

#### 2. Template Association
- **Many-to-One**: Each fixed pricing rule links to one template
- **Scope**: Rules only apply to their assigned template
- **Isolation**: No cross-template pricing interference

#### 3. Sequence-Based Priority
- **Ordering**: Rules processed by `sequence` field (ascending)
- **All Matches**: ALL rules with met conditions are applied
- **Cumulative**: Multiple matching rules contribute to total price

## How It Works

### 1. Rule Evaluation Process

When a configuration is processed, the system:

1. **Processes all matrix pricing** (mesh, frame, mullion_mohair, plugh)
2. **Processes special condition matrices** (ALL matching matrices)
3. **Fetches all active sales fixed pricing rules** for the template
4. **Evaluates ALL rules** in priority order (lowest number = highest priority)
5. **Applies ALL matching rules** (cumulative pricing)
6. **Adds all fixed prices** to the total configuration price

### 2. Priority System

Rules are evaluated in priority order:
- **Lower priority numbers = Higher priority**
- **Example**: Priority 10 is evaluated before Priority 50
- **ALL matching rules are applied** - multiple rules can contribute to total price

### 3. Condition Evaluation

The system automatically converts JavaScript expressions to Python syntax using the existing `ConfigMatrixCalculatedField` conversion logic:

#### Supported JavaScript Syntax
```javascript
// Comparison operators
door_height >= 2000 && door_type === 'sliding'
width > 1000 || height > 2000

// Math functions
Math.max(width, height) > 1500
Math.min(width, height) < 800

// Boolean values
true && (condition1 || condition2)
!false && active === true

// Number functions
Number(value) || 0
parseFloat(height) > 1000
```

#### Converted Python Syntax
```python
# Comparison operators
door_height >= 2000 and door_type == 'sliding'
width > 1000 or height > 2000

# Math functions
max(width, height) > 1500
min(width, height) < 800

# Boolean values
True and (condition1 or condition2)
not False and active == True

# Number functions
float(value) or 0
float(height) > 1000
```

### 4. Evaluation Context

The system provides a rich evaluation context with:

#### 1. Configuration Values
```python
# All configuration field values are available as variables
ctx = {
    'door_height': 2000,
    'door_width': 1000,
    'door_type': 'sliding',
    'active': True,
    # ... all other field values
}
```

#### 2. Math Functions
```python
math_context = {
    'round': round,
    'ceil': math.ceil,
    'floor': math.floor,
    'abs': abs,
    'max': max,
    'min': min,
    'sum': sum,
    'sqrt': math.sqrt
}
```

#### 3. JavaScript Compatibility
```python
js_compatibility = {
    'parseFloat': float,
    'parseInt': int,
    'Number': float,
    'Math': Math object with max, min, abs, ceil, floor, sqrt
}
```

#### 4. Boolean Values
```python
bool_context = {
    'true': True,
    'false': False,
    'True': True,
    'False': False
}
```

### 5. Cumulative Pricing

Unlike traditional pricing systems that stop at the first match, Sales Fixed Pricing applies **ALL rules that meet their conditions**:

- **Multiple rules can apply** to the same configuration
- **Prices are cumulative** - all matching rule prices are added together
- **Order matters** - rules are evaluated in priority order
- **Transparency** - each applied rule appears in the pricing breakdown

**Example**: If you have rules for "Large Door Discount" (-$50) and "Commercial Customer Discount" (-$100), and both conditions are met, both discounts will be applied for a total of -$150.

## Usage Examples

### Example 1: Large Door Discount

**Rule**: Apply $50 discount for doors with height >= 2000mm

```python
# Condition
door_height >= 2000

# Fixed Price
-50.00

# Priority
50
```

**Result**: When `door_height` is 2000mm or more, a $50 discount is applied.

### Example 2: Commercial Customer Pricing

**Rule**: Apply $100 discount for commercial customers

```python
# Condition
customer_type == 'commercial'

# Fixed Price
-100.00

# Priority
40
```

**Result**: When `customer_type` equals 'commercial', a $100 discount is applied.

### Example 3: Sliding Door Premium

**Rule**: Apply $75 surcharge for sliding doors with midrails

```python
# Condition
door_type == 'sliding' and has_midrail == True

# Fixed Price
75.00

# Priority
60
```

**Result**: When both conditions are true, a $75 surcharge is applied.

### Example 4: Cumulative Pricing Scenario

**Configuration**: Commercial customer ordering a large sliding door with express delivery

**Rule 1**: Large Door Discount
```python
# Condition
door_height >= 2000

# Fixed Price
-50.00

# Priority
50
```

**Rule 2**: Commercial Customer Discount
```python
# Condition
customer_type == 'commercial'

# Fixed Price
-100.00

# Priority
40
```

**Rule 3**: Sliding Door Premium
```python
# Condition
door_type == 'sliding'

# Fixed Price
75.00

# Priority
60
```

**Rule 4**: Express Delivery Surcharge
```python
# Condition
delivery_type == 'express'

# Fixed Price
150.00

# Priority
70
```

**Result**: All four rules apply because all conditions are met:
- Large Door Discount: -$50
- Commercial Customer Discount: -$100  
- Sliding Door Premium: +$75
- Express Delivery Surcharge: +$150
- **Total Fixed Pricing**: -$50 + (-$100) + $75 + $150 = +$75

## Technical Implementation

### Sales Fixed Pricing Method

#### `_get_sales_fixed_pricing()`

**Purpose**: Retrieves and evaluates sales fixed pricing rules for all components

**Process**:
1. **Search**: Find active sales fixed pricing entries for the template
2. **Order**: Sort by sequence (ascending)
3. **Evaluate**: Check conditions for each entry
4. **Accumulate**: Sum prices of ALL matching rules
5. **Return**: Structured result with breakdown

**Return Structure**:
```python
{
    'fixed_pricing_applied': bool,
    'total_price': float,
    'breakdown': [
        {
            'id': int,
            'description': str,
            'price': float,
            'condition': str
        }
    ],
    'currency': record or None
}
```

### Integration with Pricing System

#### Complete Pricing Flow

```python
# 1. Matrix Pricing Processing
for config in price_matrix_configs:
    price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier * panel_quantity
    if price:
        total_price += price
        # Add to breakdown

# 2. Special Condition Matrix Evaluation (ALL Matches)
matching_plugh_matrices = []
for matrix in plugh_sale_price_matrices:
    if matrix.evaluate_special_conditions(configuration_values):
        matching_plugh_matrices.append(matrix)
        # Each matching matrix gets its own breakdown entry

# 3. Sales Fixed Pricing (Separate Step)
sales_fixed_pricing_result = self._get_sales_fixed_pricing(configuration_values)
if sales_fixed_pricing_result['fixed_pricing_applied']:
    total_price += sales_fixed_pricing_result['total_price'] * final_quantity_multiplier * panel_quantity
    # Add individual fixed pricing entries to breakdown
```

## User Interface

### Views

#### 1. List View
- **Fields**: Sequence, Template, Description, Fixed Price, Condition
- **Features**: Drag-and-drop reordering, monetary formatting
- **Sorting**: Default order by sequence

#### 2. Form View
- **Layout**: Clean, organized structure
- **Fields**: Template selection, sequence, pricing, conditions
- **Validation**: Real-time condition validation
- **Help**: Contextual help text for condition fields

#### 3. Search View
- **Filters**: Active/Archived status
- **Grouping**: By template
- **Search**: By description, template, condition

### Menu Structure

```
ConfigMatrix
└── Pricing
    ├── Operations Fixed Pricing (renamed from "Fixed Price Tables")
    └── Sales Fixed Pricing (separate step pricing)
```

## Creating Fixed Pricing Rules

### Step 1: Access the Sales Fixed Pricing Menu

1. Navigate to **Matrix → Pricing → Sales Fixed Pricing**
2. Click **Create** to add a new rule

### Step 2: Configure Basic Information

- **Name**: Descriptive name for the rule (e.g., "Large Door Discount")
- **Description**: Detailed explanation of when the rule applies
- **Product Template**: Select the product this rule applies to
- **Sequence**: Display order in the list view
- **Priority**: Evaluation priority (lower numbers = higher priority)

### Step 3: Set Pricing and Conditions

- **Fixed Price**: The amount to add/subtract (negative = discount, positive = surcharge)
- **Conditions**: JavaScript expression that determines when the rule applies

### Step 4: Save and Activate

- **Active**: Check to enable the rule
- **Save** the record

## Best Practices

### 1. Priority Management

- **Use priority 10-50** for core business rules
- **Use priority 51-100** for standard rules
- **Use priority 101+** for fallback or special cases
- **Remember**: ALL matching rules are applied, so design conditions carefully to avoid conflicts

### 2. Condition Design

#### ✅ Good Practices
```javascript
// Clear, readable conditions
door_height >= 2000
door_type === 'sliding' && door_width >= 1200
Math.max(width, height) > 1500

// Use descriptive field names
product_category === 'premium'
is_custom_size === true
```

#### ❌ Avoid These
```javascript
// Overly complex conditions
(a && b) || (c && d) || (e && f) || (g && h)

// Unclear field references
field_123 === 'value'
temp_var > 1000

// Hard-coded magic numbers without context
value > 42
```

### 3. Pricing Strategy

#### ✅ Good Practices
- **Logical grouping**: Group related conditions together
- **Clear descriptions**: Use descriptive names for pricing rules
- **Reasonable values**: Set prices that make business sense
- **Sequence ordering**: Order rules from most specific to most general

#### ❌ Avoid These
- **Overlapping conditions**: Multiple rules that could apply simultaneously
- **Unclear pricing**: Vague descriptions of what the price covers
- **Extreme values**: Prices that are too high or too low
- **Poor sequencing**: Rules in random or illogical order

### 4. Template Organization

#### ✅ Good Practices
- **Template-specific rules**: Create rules for specific product types
- **Logical grouping**: Organize rules by product category or feature
- **Clear naming**: Use descriptive names for templates
- **Regular review**: Periodically review and update pricing rules

#### ❌ Avoid These
- **Global rules**: Rules that apply to all templates
- **Mixed categories**: Rules for different product types in same template
- **Unclear associations**: Templates with unclear purposes
- **Outdated rules**: Rules that are no longer relevant

## Security and Validation

### Input Validation

#### 1. Security Constraints
```python
@api.constrains('condition')
def _check_condition_security(self):
    """Validate additional condition for security"""
    dangerous_patterns = [
        'import', 'exec', 'eval', '__', 'open', 'file',
        'system', 'subprocess', 'os.', 'sys.', 'globals'
    ]
    # Check for dangerous patterns and raise ValidationError
```

#### 2. Safe Evaluation
- **Context Isolation**: Limited function access
- **No Builtins**: Restricted Python execution environment
- **Error Handling**: Graceful fallback on evaluation errors

### Access Control

#### 1. Model Access Rights
```csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_config_matrix_sales_fixed_pricing_admin,config.matrix.sales.fixed.price.admin,model_config_matrix_sales_fixed_price,group_config_matrix_admin,1,1,1,1
access_config_matrix_sales_fixed_pricing_user,config.matrix.sales.fixed.price.user,model_config_matrix_sales_fixed_price,group_config_matrix_user,1,0,0,0
```

#### 2. Record Rules
- **Template Scoping**: Users can only access rules for their accessible templates
- **Company Isolation**: Multi-company support with proper isolation

## Troubleshooting

### Common Issues

#### 1. Rule Not Applying

**Symptoms**: Fixed pricing rules never apply
**Causes**:
- Syntax errors in condition expressions
- Field names don't match configuration values
- JavaScript syntax not properly converted

**Solutions**:
```python
# Check condition syntax
print(f"Original condition: {rule.condition}")
print(f"Converted Python: {rule.env['config.matrix.calculated.field']._convert_js_to_python(rule.condition)}")

# Verify field names
print(f"Available fields: {list(configuration_values.keys())}")
```

#### 2. Multiple Rules Applying

**Symptoms**: Multiple fixed pricing rules apply when only one should
**Causes**:
- Overlapping conditions
- Incorrect sequence ordering
- Logic errors in condition expressions

**Solutions**:
```python
# Review rule sequence
rules = self.env['config.matrix.sales.fixed.price'].search([
    ('template_id', '=', template_id),
    ('active', '=', True)
], order='sequence ASC')

# Check for overlapping conditions
for rule in rules:
    print(f"Rule {rule.sequence}: {rule.condition}")
```

#### 3. Performance Issues

**Symptoms**: Slow pricing calculations
**Causes**:
- Too many fixed pricing rules
- Complex condition expressions
- Inefficient field lookups

**Solutions**:
```python
# Limit active rules
active_rules = self.env['config.matrix.sales.fixed.price'].search([
    ('template_id', '=', template_id),
    ('active', '=', True)
], limit=50)  # Limit to reasonable number

# Simplify conditions
# Instead of: Math.max(width, height) > 1500 && Math.min(width, height) < 800
# Use: (width > 1500 or height > 1500) and (width < 800 or height < 800)
```

### Debugging Tools

#### 1. Logging
```python
# Enable detailed logging
_logger.setLevel(logging.DEBUG)

# Check evaluation results
_logger.info(f"Evaluating condition: {condition}")
_logger.info(f"Configuration values: {configuration_values}")
_logger.info(f"Evaluation result: {result}")
```

#### 2. Condition Testing
```python
# Test condition evaluation
def test_condition(condition, test_values):
    """Test condition evaluation with sample values"""
    try:
        python_condition = self.env['config.matrix.calculated.field']._convert_js_to_python(condition)
        ctx = dict(test_values)
        # Add helper functions
        ctx.update(math_context)
        ctx.update(bool_context)
        
        result = safe_eval(python_condition, ctx)
        return result
    except Exception as e:
        print(f"Error: {e}")
        return False

# Test with sample data
test_values = {
    'door_height': 2000,
    'door_width': 1000,
    'door_type': 'sliding'
}
result = test_condition('door_height >= 2000 && door_type === "sliding"', test_values)
print(f"Test result: {result}")
```

## Integration with Existing Systems

### 1. Price Matrix Integration

Sales Fixed Pricing works alongside existing price matrices:
- **Price matrices** provide base pricing based on dimensions
- **Special condition matrices** provide additional matrix pricing
- **Sales fixed pricing rules** add conditional adjustments as separate step
- **Total price** = Matrix pricing + Special condition pricing + Fixed pricing adjustments

### 2. Configuration System Integration

Rules automatically evaluate during configuration:
- **Field values** are available for condition evaluation
- **Calculated fields** can be referenced in conditions
- **Real-time updates** as configuration changes

### 3. Sales Order Integration

Fixed pricing is applied when:
- **Creating sales orders** from configurations
- **Calculating line item prices**
- **Generating quotes** and proposals

## Advanced Features

### 1. Complex Conditions

You can create sophisticated pricing logic:

```python
# Multiple field conditions
door_height >= 2000 and door_width >= 1200 and door_type == 'sliding'

# Range conditions
800 <= width <= 1200 and height >= 1500

# Enumeration conditions
door_type in ['sliding', 'bi-fold'] and has_midrail == True

# Mathematical conditions
(door_height * door_width) >= 2400000  # Area >= 2.4m²
```

### 2. Helper Functions

Use built-in helper functions for calculations:

```python
# String operations
len(customer_name) > 10

# Mathematical operations
abs(door_height - 2000) <= 100  # Within 100mm of 2000mm

# Type conversions
float(width) >= 800.0
```

### 3. Dynamic Pricing

Create rules that adapt to configuration values:

```python
# Percentage-based adjustments
door_height >= 2500 and door_width >= 1500  # Large door premium

# Quantity-based pricing
quantity >= 10  # Bulk order discount

# Customer-specific pricing
customer_type == 'wholesale' and order_value >= 5000
```

## Future Enhancements

### Planned Features

#### 1. Advanced Condition Builder
- **Visual editor**: Drag-and-drop condition builder
- **Field picker**: Dropdown selection of available fields
- **Operator selection**: Predefined comparison operators
- **Validation**: Real-time condition validation

#### 2. Pricing Analytics
- **Usage tracking**: Monitor which rules are applied most
- **Performance metrics**: Track evaluation performance
- **Rule effectiveness**: Analyze pricing rule success rates
- **Optimization suggestions**: AI-powered rule optimization

#### 3. Bulk Operations
- **Mass import**: Excel/CSV import of pricing rules
- **Template copying**: Copy rules between templates
- **Batch updates**: Update multiple rules simultaneously
- **Rule templates**: Predefined rule templates for common scenarios

#### 4. Advanced Conditions
- **Date-based**: Time-sensitive pricing rules
- **Customer-based**: Customer-specific pricing
- **Quantity-based**: Volume discount rules
- **Seasonal**: Time-of-year pricing adjustments

### Integration Opportunities

#### 1. Sales Order Integration
- **Real-time pricing**: Apply fixed pricing during sales order creation
- **Quote generation**: Include fixed pricing in customer quotes
- **Approval workflows**: Require approval for high-value fixed pricing
- **Customer communication**: Explain fixed pricing to customers

#### 2. Manufacturing Integration
- **Cost calculation**: Include fixed pricing in manufacturing costs
- **BOM pricing**: Apply fixed pricing to bill of materials
- **Production planning**: Consider fixed pricing in production planning
- **Quality control**: Validate fixed pricing during quality checks

#### 3. Reporting and Analytics
- **Pricing reports**: Comprehensive pricing analysis
- **Margin analysis**: Impact of fixed pricing on margins
- **Customer profitability**: Customer-specific pricing analysis
- **Trend analysis**: Pricing trends over time

## Conclusion

The Sales Fixed Pricing system provides a powerful and flexible way to implement conditional pricing rules for all components in the ConfigMatrix system. By running as a separate step alongside matrix pricing and providing a robust condition evaluation system, it enables administrators to create sophisticated pricing strategies that respond dynamically to product configuration choices.

### Key Implementation Changes

1. **Separate Step Processing**: Sales fixed pricing now runs independently as a separate step after matrix pricing, not as a fallback mechanism.

2. **All Matching Rules**: The system processes ALL sales fixed pricing rules that meet their conditions, not just the first match.

3. **Comprehensive Integration**: Works alongside all matrix types (mesh, frame, mullion_mohair, plugh) and special condition matrices.

4. **Individual Breakdown Entries**: Each matching rule gets its own breakdown entry for better transparency and debugging.

### Key Benefits

- **Cumulative Pricing**: ALL matching rules are applied, allowing for complex pricing scenarios
- **Separate Step Processing**: Runs independently alongside matrix pricing for maximum flexibility
- **Transparent Breakdown**: Each applied rule appears in the pricing breakdown for easy debugging
- **Priority Control**: Fine-grained control over rule evaluation order
- **Flexible Conditions**: Support for complex JavaScript-like conditions

The system's architecture ensures:
- **Reliability**: Robust error handling and graceful degradation
- **Performance**: Efficient condition evaluation and caching
- **Security**: Safe expression evaluation with input validation
- **Flexibility**: Support for complex JavaScript-like conditions
- **Integration**: Seamless integration with existing pricing workflows
- **Transparency**: Clear breakdown of all pricing components

With proper implementation and following best practices, the Sales Fixed Pricing system can significantly enhance the ConfigMatrix pricing capabilities while maintaining system stability and performance.

For additional support or questions, please refer to the main ConfigMatrix documentation or contact your system administrator.
