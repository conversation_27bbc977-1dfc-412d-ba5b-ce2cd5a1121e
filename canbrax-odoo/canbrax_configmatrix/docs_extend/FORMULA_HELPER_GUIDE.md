# ConfigMatrix Formula Helper Guide

## Overview

The `ConfigMatrixFormulaHelper` is a centralized mixin that eliminates code duplication for JavaScript-to-Python formula conversion and evaluation across the ConfigMatrix system. This helper provides a consistent, maintainable, and secure way to handle formula evaluation throughout the codebase.

## Why This Helper Was Created

### Problem: Code Duplication
Before this helper, the same pattern was repeated across multiple models:

```python
# OLD PATTERN - Repeated everywhere
def evaluate_condition(self, condition, field_values):
    try:
        # Get the calculated field model as helper
        calc_field_model = self.env['config.matrix.calculated.field']
        
        # Convert JavaScript to Python
        python_formula = calc_field_model._convert_js_to_python(condition)
        
        # Create safe evaluation context
        eval_context = {
            'true': True, 'false': False,
            'True': True, 'False': False,
        }
        eval_context.update(field_values)
        
        # Add math functions
        math_context = {
            'round': round, 'ceil': math.ceil, 'floor': math.floor,
            'abs': abs, 'max': max, 'min': min, 'sum': sum
        }
        eval_context.update(math_context)
        
        # Evaluate with safe_eval
        result = safe_eval(python_formula, eval_context)
        return bool(result)
    except Exception as e:
        _logger.warning(f"Error evaluating condition: {e}")
        return False
```

### Solution: Centralized Helper
Now all models can use the helper mixin:

```python
# NEW PATTERN - Clean and consistent
def evaluate_condition(self, condition, field_values):
    try:
        return self.evaluate_condition(condition, field_values, default_result=False)
    except Exception as e:
        _logger.warning(f"Error evaluating condition: {e}")
        return False
```

## Features

### 1. JavaScript to Python Conversion
- Converts JavaScript operators (`===`, `!==`, `&&`, `||`) to Python equivalents
- Handles `Math.min()`, `Math.max()` functions
- Converts `Number()` function to `parseFloat()`
- Supports nested ternary operators
- Handles string concatenation properly

### 2. Safe Evaluation Context
- Automatically builds safe evaluation contexts
- Includes math functions (`min`, `max`, `round`, `ceil`, `floor`, etc.)
- Provides JavaScript-compatible functions (`parseFloat`, `parseInt`, `Number`)
- Handles boolean values (`true`, `false`, `True`, `False`)

### 3. Multiple Evaluation Methods
- `evaluate_formula()`: General formula evaluation
- `evaluate_condition()`: Boolean condition evaluation
- `evaluate_visibility_condition()`: Visibility condition with JSON support
- `calculate_component_quantity()`: Component quantity calculation
- `validate_formula_syntax()`: Formula validation
- `evaluate_formula_with_custom_context()`: Formula evaluation with custom functions

## Usage

### Basic Setup

```python
class MyModel(models.Model):
    _name = 'my.model'
    _inherit = ['config.matrix.formula.helper']
    
    def my_method(self):
        # Now you have access to all helper methods
        result = self.evaluate_formula('field1 + field2', {'field1': 10, 'field2': 20})
```

### Formula Evaluation

```python
# Simple arithmetic
result = self.evaluate_formula('width + height', {'width': 100, 'height': 200})
# Result: 300

# With math functions
result = self.evaluate_formula('Math.min(width, height)', {'width': 100, 'height': 200})
# Result: 100

# With JavaScript functions
result = self.evaluate_formula('Number(value) * 2', {'value': '50'})
# Result: 100.0
```

### Condition Evaluation

```python
# Simple conditions
result = self.evaluate_condition('width > 50', {'width': 100})
# Result: True

# Complex conditions
result = self.evaluate_condition('width > 50 && height < 300', {'width': 100, 'height': 200})
# Result: True

# With default value
result = self.evaluate_condition('invalid_condition', {}, default_result=True)
# Result: True (default when evaluation fails)
```

### Visibility Conditions

```python
# Regular conditions
result = self.evaluate_visibility_condition('field1', {'field1': True})
# Result: True

# JSON conditions (for complex logic)
json_condition = '__JSON__[{"condition": "field1", "logic": "and"}, {"condition": "field2", "logic": "or"}]'
result = self.evaluate_visibility_condition(json_condition, {'field1': True, 'field2': False})
# Result: True (field1 is True)
```

### Component Quantity Calculation

```python
# Simple quantity
qty = self.calculate_component_quantity('width / 1000', {'width': 2500})
# Result: 2.5

# With default
qty = self.calculate_component_quantity('', {}, default=1.0)
# Result: 1.0
```

### Formula Validation

```python
# Validate formula syntax
validation = self.validate_formula_syntax('width + height')
if validation['status'] == 'valid':
    print(f"Formula is valid: {validation['python_formula']}")
    print(f"Test result: {validation['test_result']}")
else:
    print(f"Formula error: {validation['error']}")
```

### Formula Evaluation with Custom Context

```python
# Define custom functions
custom_functions = {
    'get_fixed_price': lambda code: self._get_fixed_price(code, template),
    'get_labor_time': lambda: self._get_labor_time(field_values, template),
    'get_question': lambda q_num: self._get_question(q_num, field_values, template),
}

# Evaluate formula with custom context
result = self.evaluate_formula_with_custom_context(
    'get_fixed_price("LABOR") + get_labor_time()', 
    field_values, 
    custom_functions
)
```

## Controller Integration

### Using Helper in Controllers

Controllers can also leverage the formula helper for consistent formula evaluation:

```python
class ConfigMatrixConfigurationController(http.Controller):
    def _evaluate_formula(self, formula, field_values, template):
        """Evaluate a formula string with field values and template context"""
        if not formula:
            return 0.0

        try:
            # Use the formula helper for evaluation with custom context
            formula_helper = request.env['config.matrix.formula.helper'].sudo()
            
            # Define custom helper functions specific to this controller
            custom_functions = {
                'get_fixed_price': lambda code: self._get_fixed_price(code, template),
                'get_labor_time': lambda: self._get_labor_time(field_values, template),
                'get_question': lambda question_number: self._get_question(question_number, field_values, template),
            }
            
            # Use the helper's method for evaluation with custom context
            result = formula_helper.evaluate_formula_with_custom_context(
                formula, 
                field_values, 
                custom_functions, 
                default_value=0.0
            )
            
            return float(result) if result is not None else 0.0
        except Exception as e:
            _logger.warning(f"Error evaluating formula '{formula}': {e}")
            return 0.0
```

### Benefits of Controller Integration

1. **Consistent Evaluation**: Same logic across models and controllers
2. **Custom Functions**: Controllers can add domain-specific helper functions
3. **Error Handling**: Centralized error handling and fallbacks
4. **Performance**: Optimized context building and evaluation

## Migration Guide

### Step 1: Inherit from Helper

```python
# BEFORE
class MyModel(models.Model):
    _name = 'my.model'

# AFTER
class MyModel(models.Model):
    _name = 'my.model'
    _inherit = ['config.matrix.formula.helper']
```

### Step 2: Replace Manual Conversion

```python
# BEFORE
def evaluate_condition(self, condition, values):
    calc_field_model = self.env['config.matrix.calculated.field']
    python_formula = calc_field_model._convert_js_to_python(condition)
    
    ctx = dict(values)
    ctx.update({
        'true': True, 'false': False,
        'min': min, 'max': max, 'round': round
    })
    
    result = safe_eval(python_formula, ctx)
    return bool(result)

# AFTER
def evaluate_condition(self, condition, values):
    return self.evaluate_condition(condition, values, default_result=False)
```

### Step 3: Update Method Calls

```python
# BEFORE
if self._evaluate_condition(condition, field_values):
    # do something

# AFTER
if self.evaluate_condition(condition, field_values):
    # do something
```

## Benefits

### 1. **Maintainability**
- Single source of truth for formula conversion logic
- Easy to update and improve conversion rules
- Consistent behavior across all models

### 2. **Code Quality**
- Eliminates duplicate code
- Reduces the chance of bugs
- Easier to test and validate

### 3. **Performance**
- Optimized evaluation contexts
- Efficient math function handling
- Better error handling and recovery

### 4. **Security**
- Centralized safe evaluation
- Consistent context building
- Proper error handling

### 5. **Developer Experience**
- Simple, intuitive API
- Comprehensive documentation
- Easy to extend and customize

## Advanced Usage

### Custom Evaluation Contexts

```python
# Build context with specific options
context = self.build_evaluation_context(
    configuration_values={'width': 100, 'height': 200},
    include_math=True,           # Include math functions
    include_js_functions=False   # Don't include JavaScript functions
)

# Add custom functions
context['custom_function'] = lambda x: x * 2
```

### Error Handling

```python
try:
    result = self.evaluate_formula('complex_formula', values)
except Exception as e:
    _logger.error(f"Formula evaluation failed: {e}")
    # Handle error gracefully
    result = default_value
```

### Performance Optimization

```python
# For repeated evaluations, build context once
context = self.build_evaluation_context(config_values)

# Reuse context for multiple evaluations
result1 = safe_eval(self.convert_js_to_python(formula1), context)
result2 = safe_eval(self.convert_js_to_python(formula2), context)
```

## Testing

The helper includes comprehensive tests in `tests/test_formula_helper.py`:

```bash
# Run tests
python -m pytest canbrax_configmatrix/tests/test_formula_helper.py -v

# Run specific test
python -m pytest canbrax_configmatrix/tests/test_formula_helper.py::TestConfigMatrixFormulaHelper::test_js_to_python_conversion -v
```

## Troubleshooting

### Common Issues

1. **Import Error**: Make sure the helper is imported in `__init__.py`
2. **Method Not Found**: Ensure your model inherits from the helper
3. **Evaluation Fails**: Check that configuration values are properly formatted

### Debug Mode

```python
# Enable debug logging
import logging
logging.getLogger('config.matrix.formula.helper').setLevel(logging.DEBUG)

# Test conversion manually
python_formula = self.convert_js_to_python('Math.min(a, b)')
print(f"Converted: {python_formula}")
```

## Future Enhancements

### Planned Features
- Caching for frequently used formulas
- Advanced formula optimization
- Support for more JavaScript functions
- Formula dependency analysis
- Performance profiling tools

### Contributing
When adding new conversion rules or evaluation methods:

1. Add tests for new functionality
2. Update this documentation
3. Follow the existing code patterns
4. Ensure backward compatibility

## Conclusion

The `ConfigMatrixFormulaHelper` provides a robust, maintainable solution for formula evaluation across the ConfigMatrix system. By centralizing this functionality, we've eliminated code duplication, improved maintainability, and provided a consistent API for all formula-related operations.

For questions or contributions, please refer to the test files and existing implementations in the codebase.
