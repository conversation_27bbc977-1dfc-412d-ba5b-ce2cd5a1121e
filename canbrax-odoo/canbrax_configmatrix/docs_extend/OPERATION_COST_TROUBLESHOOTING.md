# Operation Cost Troubleshooting Guide

## Overview

This document provides comprehensive troubleshooting guidance for operation cost calculation issues in the ConfigMatrix system, particularly when UI and save config show different operation costs.

## Common Issues

### 1. Different Operation Costs Between UI and Save Config

**Symptoms:**
- UI shows different operation cost than saved configuration
- Different number of operations between UI and save
- Inconsistent total costs

**Root Causes:**
1. **Calculation Method Mismatch**: UI uses field/option mapping while save uses BOM-based calculation
2. **Missing Configuration ID**: UI calculation falls back to field/option mapping
3. **BOM Not Generated**: Configuration exists but BOM is not created
4. **Operation Template Issues**: Operation templates not properly configured

## Troubleshooting Steps

### Step 1: Check Calculation Method

**Look for these log entries:**
```
[OPERATION_COSTS] Using BOM-based calculation for config 12345
[OPERATION_COSTS] Using field/option mapping calculation (no config_id)
[OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config
```

**Expected Behavior:**
- After configuration is saved: Should use BOM-based calculation
- Before configuration is saved: May use field/option mapping (with warning)

**If using field/option mapping after save:**
1. Check if `config_id` is being passed to the API
2. Verify configuration exists and has BOM
3. Check JavaScript console for config ID issues

### Step 2: Verify Configuration and BOM

**Check these log entries:**
```
[OPERATION_COSTS_BOM] Calculating operation costs for config 12345
[OPERATION_COSTS_BOM] Total operation cost: 248.27 (operations: 53)
[OPERATION_COSTS_BOM] Processed 53 operations, total cost: 248.27
```

**If BOM calculation fails:**
```
[OPERATION_COSTS_BOM] Configuration 12345 not found
[OPERATION_COSTS_BOM] No BOM found for configuration 12345
```

**Troubleshooting:**
1. Verify configuration exists: `config.matrix.configuration` record
2. Check if BOM is generated: `bom_id` field should be set
3. Verify BOM has operations: `bom_id.operation_ids` should not be empty

### Step 3: Compare Operation Counts

**Look for these metrics:**
```
[OPERATION_COST_METRICS] {'config_id': 12345, 'operation_count': 53, 'total_cost': 248.27, 'calculation_method': 'bom_based'}
[OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 44, 'total_cost': 203.63, 'calculation_method': 'field_option_mapping'}
```

**Analysis:**
- **Same counts**: Issue is in cost calculation per operation
- **Different counts**: Issue is in operation discovery/mapping
- **BOM has more operations**: Field/option mapping is missing some operations
- **Field/option has more operations**: BOM generation is filtering out operations

### Step 4: Check Operation Template Configuration

**Look for these log entries:**
```
[OPERATION_COSTS_BOM] Error processing BOM operation 'Operation Name': ...
[OPERATION_COSTS_BOM] Using fallback calculation...
```

**Troubleshooting:**
1. Verify operation templates exist for BOM operations
2. Check operation template cost calculation methods
3. Verify field mappings in operation templates

## Log Analysis Patterns

### Pattern 1: Consistent BOM-Based Calculation
```
[OPERATION_COSTS] Using BOM-based calculation for config 12345
[OPERATION_COSTS_BOM] Calculating operation costs for config 12345
[OPERATION_COSTS_BOM] Total operation cost: 248.27 (operations: 53)
[OPERATION_COST_METRICS] {'config_id': 12345, 'operation_count': 53, 'total_cost': 248.27, 'calculation_method': 'bom_based'}
```
**Status**: ✅ Working correctly

### Pattern 2: Field/Option Mapping Fallback
```
[OPERATION_COSTS] Using field/option mapping calculation (no config_id)
[OPERATION_COSTS] WARNING: This may result in different operation costs than save config
[OPERATION_COSTS] Returning 44 operations with total cost 203.63
[OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config
```
**Status**: ⚠️ Expected behavior before configuration is saved

**Note**: This is normal when testing operation costs before saving the configuration. The system will use BOM-based calculation after the configuration is saved and a `config_id` is available.

### Pattern 3: BOM Calculation Error
```
[OPERATION_COSTS_BOM] Configuration 12345 not found
[OPERATION_COSTS_BOM] No BOM found for configuration 12345
```
**Status**: ❌ Configuration or BOM issue

### Pattern 4: Operation Processing Error
```
[OPERATION_COSTS_BOM] Error processing BOM operation 'Operation Name': ...
[OPERATION_COSTS_BOM] Using fallback calculation...
```
**Status**: ⚠️ Operation template issue (using fallback)

## Testing Workflow

### Correct Testing Sequence
1. **Configure Product**: Fill out configuration fields
2. **Save Configuration**: Click save to create configuration and BOM
3. **Test Operation Costs**: Now operation costs will use BOM-based calculation
4. **Verify Consistency**: UI and save config should show same operation costs

### Common Testing Mistake
**Problem**: Testing operation costs before saving configuration
**Result**: System uses field/option mapping (different from save config)
**Solution**: Save configuration first, then test operation costs

## Quick Fixes

### Fix 1: Ensure Config ID is Passed
**Problem**: UI uses field/option mapping instead of BOM-based calculation

**Solution**:
1. **First**: Save the configuration to get a `config_id`
2. Check JavaScript console for config ID
3. Verify `configId` is set in operation costs handler
4. Ensure configurator dispatches `config-id-changed` event

### Fix 2: Regenerate BOM
**Problem**: BOM is missing or incomplete

**Solution**:
```python
# In Odoo shell or through API
config = env['config.matrix.configuration'].browse(config_id)
config.generate_bom()
config.calculate_price()
```

### Fix 3: Check Operation Templates
**Problem**: Operation templates not configured properly

**Solution**:
1. Verify operation templates exist for all BOM operations
2. Check operation template cost calculation methods
3. Ensure field mappings are correct

## Prevention

### 1. Always Use BOM-Based Calculation
- Ensure `config_id` is available after configuration is saved
- Use BOM-based calculation for consistency
- Log warnings when field/option mapping is used

### 2. Comprehensive Logging
- Log key metrics for all calculations
- Include calculation method in logs
- Log warnings for potential inconsistencies

### 3. Validation
- Validate BOM generation after configuration save
- Check operation template configuration
- Verify operation counts match between methods

## Monitoring

### Key Metrics to Monitor
1. **Calculation Method Distribution**: Track BOM-based vs field/option mapping usage
2. **Operation Count Consistency**: Monitor differences between calculation methods
3. **Cost Variance**: Track cost differences between UI and save config
4. **Error Rates**: Monitor operation processing errors

### Alert Conditions
- Field/option mapping used after configuration save
- Significant cost differences between calculation methods
- High error rates in operation processing
- Missing BOMs for saved configurations

## Related Documentation

- [Developer Guide](06_DEVELOPER_GUIDE.md) - Technical implementation details
- [BOM Generation](03_BOM_GENERATION.md) - BOM generation process
- [Manufacturing Integration](04_MANUFACTURING_ORDER_CREATION.md) - Manufacturing workflow
- [Logging Guide](LOGGING_IMPLEMENTATION_GUIDE.md) - Logging implementation

## Support

For additional troubleshooting support:
1. Check the logs using the patterns above
2. Verify configuration and BOM status
3. Test with a simple configuration first
4. Contact development team with specific log excerpts
