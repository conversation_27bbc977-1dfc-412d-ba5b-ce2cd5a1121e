# Operation Cost Context Fix - Form State Management

## Problem Description

The operation cost calculation system had an issue where it would always use BOM-based calculation when a `config_id` was provided, even when the user was editing the form. This caused:

1. **Initial Load**: Correctly showed saved operation costs using BOM-based calculation
2. **Form Changes**: Still used BOM-based calculation instead of live field/option mapping
3. **Result**: Users couldn't see live operation cost changes as they modified the form

## Root Cause

The system lacked context awareness to distinguish between:
- **Initial Load**: When loading an existing configuration (should use BOM-based)
- **Form Changes**: When user modifies form values (should use field/option mapping for live updates)

## Solution Implementation

### Simplified State Management

**Why One Variable Instead of Two?**

The original implementation used two variables:
- `isInitialLoad` - Whether this is the first load of a configuration
- `hasFormChanged` - Whether the user has modified any form fields

However, this was unnecessarily complex. The decision logic was:
```javascript
if (this.isInitialLoad && this.configId && !this.hasFormChanged) {
    // Use BOM-based calculation
} else {
    // Use field/option mapping calculation
}
```

This can be simplified to a single boolean variable `useBomCalculation`:
```javascript
if (this.useBomCalculation && this.configId) {
    // Use BOM-based calculation
} else {
    // Use field/option mapping calculation
}
```

**Benefits of Single Variable:**
- ✅ **Simpler Logic**: One variable instead of two
- ✅ **Clearer Intent**: Variable name directly indicates calculation method
- ✅ **Easier Debugging**: Single state to track
- ✅ **Less Memory**: One boolean instead of two
- ✅ **Reduced Complexity**: Fewer state transitions to manage

### 1. Frontend State Management (`operation_costs_handler.js`)

#### Added Form State Tracking
```javascript
constructor() {
    // ... existing properties ...
    this.useBomCalculation = true; // Use BOM calculation initially, switch to field/option mapping when form changes
}
```

#### Context-Aware Calculation Logic
```javascript
// Determine calculation context based on single state variable
let calculationContext = 'field_option_mapping'; // Default to live calculation
let effectiveConfigId = null;

if (this.useBomCalculation && this.configId) {
    // Use BOM-based calculation for initial load or when explicitly set
    calculationContext = 'bom_based';
    effectiveConfigId = this.configId;
    console.log('[OperationCosts] ✅ Using BOM-based calculation');
} else {
    // Use field/option mapping for live calculation
    calculationContext = 'field_option_mapping';
    effectiveConfigId = null;
    console.log('[OperationCosts] 🔄 Using field/option mapping for live calculation');
}
```

#### Form Change Detection
```javascript
onFieldChange(event) {
    // Switch to field/option mapping calculation when form changes
    this.useBomCalculation = false;
    
    // Debounce the refresh to avoid too many calls
    clearTimeout(this.refreshTimeout);
    this.refreshTimeout = setTimeout(() => {
        this.refreshOperationCosts();
    }, 300);
}
```

#### State Reset on Config Change
```javascript
updateConfigId(newConfigId) {
    this.configId = newConfigId;
    // Reset to BOM calculation when config ID changes (new configuration loaded)
    this.useBomCalculation = true;
    console.log(`[OPERATION_COSTS_JS] Config ID updated to: ${this.configId}, switched to BOM calculation`);
}
```

### 2. Backend Context Handling (`configuration_controller.py`)

#### Enhanced Controller Method
```python
@http.route('/config_matrix/calculate_operation_costs', type='json', auth='public', website=True)
def calculate_operation_costs(self, template_id, field_values=None, config_id=None, calculation_context=None, **kw):
    """Calculate operation costs based on current field values with quantity multiplier"""
    _logger.info(f"[OPERATION_COSTS] Calculation context: {calculation_context}")
    _logger.info(f"[OPERATION_COSTS] Config ID: {config_id}")
    
    # CONTEXT-AWARE CALCULATION: Use calculation_context to determine method
    if calculation_context == 'bom_based' and config_id:
        _logger.info(f"[OPERATION_COSTS] Using BOM-based calculation for config {config_id} (initial load)")
        return self._calculate_operation_costs_from_bom(config_id, field_values, quantity_multiplier)
    else:
        _logger.info(f"[OPERATION_COSTS] Using field/option mapping calculation (form changes or no config_id)")
        # ... field/option mapping logic ...
```

## Behavior Flow

### 1. Initial Load (Existing Configuration)
```
1. Page loads with config_id
2. useBomCalculation = true
3. calculationContext = 'bom_based'
4. effectiveConfigId = config_id
5. Backend uses BOM-based calculation
6. Shows saved operation costs
```

### 2. Form Changes (User Editing)
```
1. User changes form field
2. onFieldChange() triggered
3. useBomCalculation = false
4. calculationContext = 'field_option_mapping'
5. effectiveConfigId = null
6. Backend uses field/option mapping
7. Shows live operation costs based on current form values
```

### 3. New Configuration Load
```
1. updateConfigId() called with new config_id
2. useBomCalculation = true (reset)
3. Next calculation uses BOM-based approach
4. Shows saved operation costs for new configuration
```

## API Changes

### Frontend Request Format
```javascript
const requestBody = {
    jsonrpc: "2.0",
    method: "call",
    params: {
        template_id: this.templateId,
        field_values: this.currentValues,
        config_id: effectiveConfigId,        // null for form changes
        calculation_context: calculationContext  // 'bom_based' or 'field_option_mapping'
    },
    id: new Date().getTime()
};
```

### Backend Response
No changes to response format - same as before.

## New Public Methods

### Frontend Methods
```javascript
// Reset form state (useful for external calls)
window.resetOperationCostsFormState()

// Get current form state
window.getOperationCostsFormState()
// Returns: { useBomCalculation: boolean, configId: number }

// Existing methods still work
window.refreshOperationCosts()
window.forceRefreshOperationCosts()
window.isOperationCostsReady()
```

## Logging Enhancements

### Frontend Logs
```
[OperationCosts] Calculation state: { useBomCalculation: true, configId: 123 }
[OperationCosts] ✅ Using BOM-based calculation with config_id: 123
[OperationCosts] Calculation state: { useBomCalculation: false, configId: 123 }
[OperationCosts] 🔄 Using field/option mapping for live calculation
[OperationCosts] Reason: Form has been modified
```

### Backend Logs
```
[OPERATION_COSTS] Calculation context: bom_based
[OPERATION_COSTS] Config ID: 123
[OPERATION_COSTS] Using BOM-based calculation for config 123 (initial load)

[OPERATION_COSTS] Calculation context: field_option_mapping
[OPERATION_COSTS] Config ID: None
[OPERATION_COSTS] Using field/option mapping calculation (form changes or no config_id)
```

## Testing Scenarios

### 1. Load Existing Configuration
1. Open configuration with config_id
2. Verify initial operation costs show saved values (BOM-based)
3. Check logs show "Initial load - using BOM-based calculation"

### 2. Edit Form Fields
1. Change any form field
2. Verify operation costs update with live values (field/option mapping)
3. Check logs show "Form changed - using field/option mapping"

### 3. Load Different Configuration
1. Load different configuration (config_id changes)
2. Verify operation costs reset to new saved values (BOM-based)
3. Check logs show form state reset

### 4. New Configuration Creation
1. Start new configuration (no config_id)
2. Verify operation costs use field/option mapping
3. Check logs show "no config_id available"

## Benefits

### 1. **Accurate Initial Load**
- Shows correct saved operation costs when loading existing configurations
- Uses BOM-based calculation for consistency with saved state

### 2. **Live Form Updates**
- Shows real-time operation cost changes as user edits form
- Uses field/option mapping for immediate feedback

### 3. **Context Awareness**
- System automatically determines appropriate calculation method
- No manual intervention required

### 4. **Backward Compatibility**
- Existing functionality preserved
- No breaking changes to API

### 5. **Enhanced Debugging**
- Clear logging shows which calculation method is used
- Form state tracking for troubleshooting

## Implementation Details

### State Management
- **useBomCalculation**: Boolean flag indicating whether to use BOM-based calculation (true) or field/option mapping (false)
- **configId**: Current configuration ID (null for new configurations)

### Calculation Contexts
- **bom_based**: Uses saved BOM operations for accurate saved state
- **field_option_mapping**: Uses current form values for live updates

### Event Handling
- **onFieldChange**: Switches to field/option mapping and triggers live calculation
- **updateConfigId**: Resets to BOM calculation when loading new configuration
- **refreshOperationCosts**: Uses single state variable to determine calculation method

## Future Enhancements

### 1. **Advanced State Management**
- Track individual field changes
- Selective recalculation based on changed fields
- Undo/redo functionality

### 2. **Performance Optimization**
- Cache calculation results for unchanged form states
- Debounce calculations more intelligently
- Background calculation for complex operations

### 3. **User Experience**
- Visual indicators showing calculation method
- Progress indicators for complex calculations
- Error handling for calculation failures

## Conclusion

This fix resolves the operation cost calculation context issue by:

1. **Adding form state tracking** to distinguish between initial load and form changes
2. **Implementing context-aware calculation** that uses appropriate method based on state
3. **Providing clear logging** for debugging and monitoring
4. **Maintaining backward compatibility** with existing functionality

The system now correctly shows:
- **Saved operation costs** when loading existing configurations
- **Live operation costs** when users modify form fields
- **Appropriate calculation method** based on the current context

This ensures users see accurate operation costs in all scenarios while maintaining system performance and reliability.
