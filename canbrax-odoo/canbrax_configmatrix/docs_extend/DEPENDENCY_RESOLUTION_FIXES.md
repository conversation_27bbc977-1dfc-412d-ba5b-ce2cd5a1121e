# Dependency Resolution Fixes - December 2024

## Overview

This document details the fixes applied to the ConfigMatrix calculated fields dependency resolution system to address type mismatch errors and incorrect field evaluation order.

## Problem Analysis

### Root Cause

The dependency resolution system was experiencing two main issues:

1. **Type Mismatch Errors**: Calculated field values were being treated as strings instead of their proper data types (number, boolean, string), causing comparison errors in formulas.

2. **Dependency Resolution Failure**: The topological sort algorithm was failing and falling back to sequence-based ordering, which didn't respect actual field dependencies.

### Error Examples

The following errors were occurring:

```
TypeError("'<=' not supported between instances of 'int' and 'str'")
```

This happened because:
- Formulas contained comparisons like `1911 <= _CALCULATED_left_height_door`
- `_CALCULATED_left_height_door` was being evaluated as a **string** instead of a **number**
- The system was trying to compare an integer (1911) with a string

## Solutions Implemented

### 1. Enhanced Type Conversion System

#### Backend Implementation (Python)

**File**: `models/config_matrix_calculated_field.py`

**Method**: `_ensure_proper_types()`

**Changes**:
- Added data type awareness for calculated fields
- Implemented proper type conversion based on field definitions
- Added fallback conversion for fields without explicit type definitions
- Enhanced logging for type conversion debugging

```python
@api.model
def _ensure_proper_types(self, context):
    """Ensure that calculated field values are properly converted based on their data types"""
    converted_context = dict(context)
    
    for key, value in context.items():
        if key.startswith('_CALCULATED_') and value is not None:
            # Get the field definition to determine data type
            field_def = self._get_field_definition(key)
            if field_def:
                data_type = field_def.get('result_type', 'string')
                
                if data_type == 'number':
                    # Convert to numeric if it's a string that looks like a number
                    if isinstance(value, str):
                        try:
                            numeric_value = float(value)
                            converted_context[key] = numeric_value
                            _logger.debug(f"[TYPE_CONVERSION] Converted {key} from '{value}' to {numeric_value}")
                        except (ValueError, TypeError):
                            # If conversion fails, keep as string
                            _logger.debug(f"[TYPE_CONVERSION] Could not convert {key} '{value}' to number, keeping as string")
                    elif isinstance(value, (int, float)):
                        converted_context[key] = float(value)
                        
                elif data_type == 'boolean':
                    # Convert to boolean
                    if isinstance(value, str):
                        if value.lower() in ('true', '1', 'yes', 'on'):
                            converted_context[key] = True
                        elif value.lower() in ('false', '0', 'no', 'off'):
                            converted_context[key] = False
                        else:
                            _logger.debug(f"[TYPE_CONVERSION] Could not convert {key} '{value}' to boolean, keeping as string")
                    elif isinstance(value, (int, float)):
                        converted_context[key] = bool(value)
                    else:
                        converted_context[key] = bool(value)
                        
                elif data_type == 'string':
                    # Ensure it's a string
                    converted_context[key] = str(value)
            else:
                # Fallback: try to convert to numeric if it looks like a number
                if isinstance(value, str):
                    try:
                        numeric_value = float(value)
                        converted_context[key] = numeric_value
                        _logger.debug(f"[TYPE_CONVERSION] Converted {key} from '{value}' to {numeric_value} (fallback)")
                    except (ValueError, TypeError):
                        # If conversion fails, keep as string
                        _logger.debug(f"[TYPE_CONVERSION] Could not convert {key} '{value}' to number, keeping as string")
    
        return converted_context
```

#### Frontend Implementation (JavaScript)

**File**: `static/src/js/visibility_conditions.js`

**Method**: `ensureProperTypes()`

**Changes**:
- Added data type awareness for calculated fields in frontend
- Implemented proper type conversion based on field definitions
- Added fallback conversion for fields without explicit type definitions
- Enhanced debug logging for type conversion troubleshooting

```javascript
// ENHANCED: Type conversion function to ensure proper data types
function ensureProperTypes(context) {
    const convertedContext = { ...context };
    
    for (const [key, value] of Object.entries(context)) {
        if (key.startsWith('_CALCULATED_') && value !== null && value !== undefined) {
            // Get field definition to determine data type
            const fieldDef = calculatedFieldsDefinitions.find(def => def.name === key);
            const dataType = fieldDef ? fieldDef.result_type : 'string';
            
            if (dataType === 'number') {
                // Convert to numeric if it's a string that looks like a number
                if (typeof value === 'string') {
                    try {
                        const numericValue = parseFloat(value);
                        if (!isNaN(numericValue)) {
                            convertedContext[key] = numericValue;
                            if (window.debugConditions) {
                                console.debug(`[TYPE_CONVERSION] Converted ${key} from '${value}' to ${numericValue}`);
                            }
                        }
                    } catch (e) {
                        if (window.debugConditions) {
                            console.debug(`[TYPE_CONVERSION] Could not convert ${key} '${value}' to number, keeping as string`);
                        }
                    }
                } else if (typeof value === 'number') {
                    convertedContext[key] = value;
                }
            } else if (dataType === 'boolean') {
                // Convert to boolean
                if (typeof value === 'string') {
                    const lowerValue = value.toLowerCase();
                    if (['true', '1', 'yes', 'on'].includes(lowerValue)) {
                        convertedContext[key] = true;
                    } else if (['false', '0', 'no', 'off'].includes(lowerValue)) {
                        convertedContext[key] = false;
                    } else {
                        if (window.debugConditions) {
                            console.debug(`[TYPE_CONVERSION] Could not convert ${key} '${value}' to boolean, keeping as string`);
                        }
                    }
                } else if (typeof value === 'number') {
                    convertedContext[key] = Boolean(value);
                } else {
                    convertedContext[key] = Boolean(value);
                }
            } else if (dataType === 'string') {
                // Ensure it's a string
                convertedContext[key] = String(value);
            }
        } else if (key.startsWith('_CALCULATED_') && typeof value === 'string') {
            // Fallback: try to convert to numeric if it looks like a number
            try {
                const numericValue = parseFloat(value);
                if (!isNaN(numericValue)) {
                    convertedContext[key] = numericValue;
                    if (window.debugConditions) {
                        console.debug(`[TYPE_CONVERSION] Converted ${key} from '${value}' to ${numericValue} (fallback)`);
                    }
                }
            } catch (e) {
                if (window.debugConditions) {
                    console.debug(`[TYPE_CONVERSION] Could not convert ${key} '${value}' to number, keeping as string`);
                }
            }
        }
    }
    
    return convertedContext;
}

// ENHANCED: Ensure field result has proper type
function ensureFieldType(value, dataType) {
    if (value === null || value === undefined) {
        return null;
    }
    
    switch (dataType) {
        case 'number':
            if (typeof value === 'string') {
                const numericValue = parseFloat(value);
                return isNaN(numericValue) ? value : numericValue;
            }
            return typeof value === 'number' ? value : parseFloat(value) || 0;
            
        case 'boolean':
            if (typeof value === 'string') {
                const lowerValue = value.toLowerCase();
                return ['true', '1', 'yes', 'on'].includes(lowerValue);
            }
            return Boolean(value);
            
        case 'string':
        default:
            return String(value);
    }
}
```

### 2. Improved Dependency Resolution

#### Backend Implementation (Python)

**File**: `models/config_matrix_calculated_field.py`

**Method**: `_topological_sort_with_sequence()`

**Changes**:
- Enhanced error handling for circular dependencies
- Added fallback mechanism for failed topological sort
- Improved logging for dependency resolution debugging
- Better handling of edge cases

```python
def _topological_sort_with_sequence(self, dependency_graph, field_sequences):
    """Perform topological sort with sequence consideration for fields with same dependency level"""
    # Kahn's algorithm for topological sorting with sequence-based tie-breaking
    in_degree = {node: 0 for node in dependency_graph}
    
    # Calculate in-degrees (how many dependencies each node has)
    for node, dependencies in dependency_graph.items():
        in_degree[node] = len(dependencies)
    
    # Find nodes with no incoming edges (no dependencies), sorted by sequence
    available_nodes = [node for node, degree in in_degree.items() if degree == 0]
    available_nodes.sort(key=lambda x: field_sequences.get(x, 10))
    
    result = []
    
    while available_nodes:
        # Sort available nodes by sequence for consistent ordering
        available_nodes.sort(key=lambda x: field_sequences.get(x, 10))
        
        # Process the first available node
        current_node = available_nodes.pop(0)
        result.append(current_node)
        
        # Update in-degrees for dependent nodes
        for dependent_node in dependency_graph:
            if current_node in dependency_graph[dependent_node]:
                in_degree[dependent_node] -= 1
                if in_degree[dependent_node] == 0:
                    available_nodes.append(dependent_node)
    
    # Check for circular dependencies
    if len(result) != len(dependency_graph):
        _logger.warning(f"Circular dependency detected in calculated fields. "
                       f"Processed {len(result)} out of {len(dependency_graph)} fields.")
        # Add remaining nodes in sequence order as fallback
        remaining_nodes = [node for node in dependency_graph if node not in result]
        remaining_nodes.sort(key=lambda x: field_sequences.get(x, 10))
        result.extend(remaining_nodes)
    
    return result
```

#### Frontend Implementation (JavaScript)

**File**: `static/src/js/visibility_conditions.js`

**Method**: `topologicalSortWithSequence()`

**Changes**:
- Enhanced error handling for circular dependencies
- Added fallback mechanism for failed topological sort
- Improved logging for dependency resolution debugging
- Added sequence-based tie-breaking for consistent ordering

```javascript
// ENHANCED: Topological sort with sequence consideration
function topologicalSortWithSequence(dependencyGraph, fieldSequences) {
    // Kahn's algorithm for topological sorting with sequence-based tie-breaking
    const inDegree = {};
    
    // Initialize in-degrees
    for (const node of Object.keys(dependencyGraph)) {
        inDegree[node] = 0;
    }
    
    // Calculate in-degrees (how many dependencies each node has)
    for (const [node, dependencies] of Object.entries(dependencyGraph)) {
        inDegree[node] = dependencies.length;
    }
    
    // Find nodes with no incoming edges (no dependencies), sorted by sequence
    let availableNodes = Object.keys(dependencyGraph).filter(node => inDegree[node] === 0);
    availableNodes.sort((a, b) => (fieldSequences[a] || 10) - (fieldSequences[b] || 10));
    
    const result = [];
    
    while (availableNodes.length > 0) {
        // Sort available nodes by sequence for consistent ordering
        availableNodes.sort((a, b) => (fieldSequences[a] || 10) - (fieldSequences[b] || 10));
        
        // Process the first available node
        const currentNode = availableNodes.shift();
        result.push(currentNode);
        
        // Update in-degrees for dependent nodes
        for (const [dependentNode, dependencies] of Object.entries(dependencyGraph)) {
            if (dependencies.includes(currentNode)) {
                inDegree[dependentNode]--;
                if (inDegree[dependentNode] === 0) {
                    availableNodes.push(dependentNode);
                }
            }
        }
    }
    
    // Check for circular dependencies
    if (result.length !== Object.keys(dependencyGraph).length) {
        console.warn(`Circular dependency detected in calculated fields. Processed ${result.length} out of ${Object.keys(dependencyGraph).length} fields.`);
        // Add remaining nodes in sequence order as fallback
        const remainingNodes = Object.keys(dependencyGraph).filter(node => !result.includes(node));
        remainingNodes.sort((a, b) => (fieldSequences[a] || 10) - (fieldSequences[b] || 10));
        result.push(...remainingNodes);
    }
    
    return result;
}

// ENHANCED: Sort fields by dependencies using improved algorithm
function sortFieldsByDependencies(calculatedFields) {
    // Create dependency graph
    const dependencyGraph = {};
    const fieldSequences = {};
    
    calculatedFields.forEach(field => {
        const fieldName = field.name;
        const formula = field.formula;
        const sequence = field.sequence || 10;
        
        // Extract dependencies from formula
        const dependencies = extractDependenciesFromFormula(formula);
        
        // Filter to only include calculated field dependencies
        const calculatedDependencies = dependencies.filter(dep => dep.startsWith('_CALCULATED_'));
        
        dependencyGraph[fieldName] = calculatedDependencies;
        fieldSequences[fieldName] = sequence;
    });
    
    if (window.debugConditions) {
        console.log('[DEPENDENCY_DEBUG] Dependency graph:', dependencyGraph);
        console.log('[DEPENDENCY_DEBUG] Field sequences:', fieldSequences);
    }
    
    try {
        // Try topological sort first
        const sortedFields = topologicalSortWithSequence(dependencyGraph, fieldSequences);
        if (window.debugConditions) {
            console.log('[DEPENDENCY_DEBUG] Topological sort successful:', sortedFields);
        }
        
        // Convert back to field objects in sorted order
        const sortedFieldObjects = [];
        const fieldMap = new Map(calculatedFields.map(field => [field.name, field]));
        
        sortedFields.forEach(fieldName => {
            const field = fieldMap.get(fieldName);
            if (field) {
                sortedFieldObjects.push(field);
            }
        });
        
        return sortedFieldObjects;
    } catch (e) {
        console.error('[DEPENDENCY_DEBUG] Topological sort failed:', e);
        console.log('[DEPENDENCY_DEBUG] Using sequence-based fallback');
        
        // Fallback to sequence-based sorting
        const fallbackSorted = calculatedFields.slice().sort((a, b) => (a.sequence || 10) - (b.sequence || 10));
        if (window.debugConditions) {
            console.log('[DEPENDENCY_DEBUG] Using sequence-based fallback:', fallbackSorted.map(f => f.name));
        }
        return fallbackSorted;
    }
}
```

### 3. Enhanced Error Handling

**File**: `models/config_matrix_calculated_field.py`

**Method**: `_sort_fields_by_dependencies()`

**Changes**:
- Added comprehensive error handling and logging
- Implemented fallback to sequence-based sorting
- Enhanced debugging information
- Better error reporting

```python
@api.model
def _sort_fields_by_dependencies(self, calculated_fields):
    """Sort fields by dependencies using improved topological sort with sequence consideration"""
    # Create dependency graph and field info
    dependency_graph = {}
    field_info = {}
    field_sequences = {}
    
    for field in calculated_fields:
        field_name = field['name']
        formula = field['formula']
        sequence = field.get('sequence', 10)
        
        # Extract dependencies from formula
        dependencies = self._extract_dependencies_from_formula(formula)
        
        # Filter to only include calculated field dependencies
        calculated_dependencies = [dep for dep in dependencies if dep.startswith('_CALCULATED_')]
        
        dependency_graph[field_name] = calculated_dependencies
        field_info[field_name] = field
        field_sequences[field_name] = sequence
    
    _logger.info(f"[DEPENDENCY_DEBUG] Dependency graph: {dependency_graph}")
    _logger.info(f"[DEPENDENCY_DEBUG] Field sequences: {field_sequences}")
    
    try:
        # Try topological sort first
        sorted_fields = self._topological_sort_with_sequence(dependency_graph, field_sequences)
        _logger.info(f"[DEPENDENCY_DEBUG] Topological sort successful: {sorted_fields}")
        return sorted_fields
    except Exception as e:
        _logger.error(f"[DEPENDENCY_DEBUG] Topological sort failed: {e}")
        _logger.info(f"[DEPENDENCY_DEBUG] Using sequence-based fallback")
        
        # Fallback to sequence-based sorting
        fallback_sorted = sorted(dependency_graph.keys(), key=lambda x: field_sequences.get(x, 10))
        _logger.info(f"[DEPENDENCY_DEBUG] Using sequence-based fallback: {fallback_sorted}")
        return fallback_sorted
```

### 4. Formula Helper Integration

**File**: `models/config_matrix_formula_helper.py`

**Method**: `build_evaluation_context()`

**Changes**:
- Integrated with the new type conversion system
- Enhanced context building for formula evaluation
- Better handling of different data types

```python
def build_evaluation_context(self, configuration_values=None, include_math=True, include_js_functions=True):
    """
    Build a safe evaluation context for formula evaluation
    
    Args:
        configuration_values (dict): Configuration field values
        include_math (bool): Include math functions in context
        include_js_functions (bool): Include JavaScript-compatible functions
        
    Returns:
        dict: Safe evaluation context
    """
    context = {}
    
    # Add configuration values if provided
    if configuration_values:
        # Use the enhanced type conversion system
        context.update(self._ensure_proper_types(configuration_values))
    
    # Add boolean context
    bool_context = {
        'true': True,
        'false': False,
        'True': True,
        'False': False,
    }
    context.update(bool_context)
    
    # Add math functions if requested
    if include_math:
        math_context = {
            'abs': abs,
            'min': min,
            'max': max,
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor,
            'sqrt': math.sqrt,
            'pow': pow,
            'log': math.log,
            'log10': math.log10,
            'exp': math.exp,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'pi': math.pi,
            'e': math.e,
        }
        context.update(math_context)
    
    # Add JavaScript-compatible functions if requested
    if include_js_functions:
        js_context = {
            'parseFloat': float,
            'parseInt': int,
            'Number': float,
            'Math': math,
        }
        context.update(js_context)
    
    return context
```

## Testing and Verification

### Test Cases

1. **Type Conversion Test**:
   ```python
   # Test numeric conversion
   context = {'_CALCULATED_height': '2100.0'}
   converted = self._ensure_proper_types(context)
   assert isinstance(converted['_CALCULATED_height'], float)
   assert converted['_CALCULATED_height'] == 2100.0
   
   # Test boolean conversion
   context = {'_CALCULATED_is_large': 'true'}
   converted = self._ensure_proper_types(context)
   assert isinstance(converted['_CALCULATED_is_large'], bool)
   assert converted['_CALCULATED_is_large'] == True
   ```

2. **Dependency Resolution Test**:
   ```python
   # Test dependency sorting
   fields = [
       {'name': '_CALCULATED_field_a', 'formula': 'width * height', 'sequence': 10},
       {'name': '_CALCULATED_field_b', 'formula': '_CALCULATED_field_a * 2', 'sequence': 20}
   ]
   sorted_fields = self._sort_fields_by_dependencies(fields)
   assert sorted_fields[0] == '_CALCULATED_field_a'
   assert sorted_fields[1] == '_CALCULATED_field_b'
   ```

3. **Formula Evaluation Test**:
   ```python
   # Test formula evaluation with proper types
   context = {'width': 1000.0, 'height': 2100.0}
   formula = 'width * height / 1000000'
   result = self._evaluate_formula(formula, context)
   assert result == 2.1
   ```

### Debug Logging

The fixes include comprehensive debug logging to help identify issues:

```
[DEPENDENCY_DEBUG] Dependency graph: {'_CALCULATED_field_a': [], '_CALCULATED_field_b': ['_CALCULATED_field_a']}
[DEPENDENCY_DEBUG] Field sequences: {'_CALCULATED_field_a': 10, '_CALCULATED_field_b': 20}
[DEPENDENCY_DEBUG] Topological sort successful: ['_CALCULATED_field_a', '_CALCULATED_field_b']
[TYPE_CONVERSION] Converted _CALCULATED_height from '2100.0' to 2100.0
```

## Performance Impact

### Positive Impacts

1. **Reduced Errors**: Eliminates type mismatch errors that were causing formula evaluation failures
2. **Better Caching**: Proper type conversion enables more effective caching
3. **Improved Reliability**: Enhanced error handling prevents silent failures

### Considerations

1. **Type Conversion Overhead**: Minimal overhead for type conversion operations
2. **Logging Impact**: Debug logging can be disabled in production
3. **Memory Usage**: Slight increase in memory usage for type conversion context

## Migration Notes

### Backward Compatibility

The fixes are fully backward compatible:
- Existing calculated fields continue to work
- No changes required to existing formulas
- Enhanced functionality is additive only

### Configuration Updates

No configuration changes are required. The fixes are automatically applied to all calculated fields.

## Monitoring and Maintenance

### Key Metrics to Monitor

1. **Type Conversion Success Rate**: Monitor `[TYPE_CONVERSION]` log entries
2. **Dependency Resolution Success**: Monitor `[DEPENDENCY_DEBUG]` log entries
3. **Formula Evaluation Errors**: Monitor formula evaluation error rates

### Maintenance Tasks

1. **Regular Log Review**: Check for type conversion failures
2. **Dependency Analysis**: Monitor dependency resolution performance
3. **Error Rate Monitoring**: Track formula evaluation success rates

## Future Enhancements

### Planned Improvements

1. **Advanced Type Inference**: Automatic type detection from formulas
2. **Performance Optimization**: Caching for type conversion results
3. **Enhanced Debugging**: Visual dependency graph representation
4. **Type Validation**: Pre-validation of formula types

### Extension Points

1. **Custom Type Converters**: Support for custom data types
2. **Type Validation Rules**: Configurable type validation
3. **Performance Metrics**: Detailed performance monitoring
4. **Error Recovery**: Advanced error recovery mechanisms

## Conclusion

The dependency resolution fixes address critical issues in the ConfigMatrix calculated fields system:

1. **Type Safety**: Proper type conversion ensures formula evaluation works correctly
2. **Reliability**: Enhanced error handling prevents silent failures
3. **Performance**: Better dependency resolution improves evaluation efficiency
4. **Maintainability**: Comprehensive logging aids in debugging and maintenance

These fixes ensure that the calculated fields system works reliably and efficiently, providing a solid foundation for complex product configurations.

