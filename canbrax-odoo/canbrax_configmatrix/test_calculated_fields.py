#!/usr/bin/env python3
"""
Test script for the improved calculated fields dependency resolution system.
This script demonstrates how the new system handles dependencies between calculated fields.
"""

import logging
import sys
import os
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_dependencies_from_formula(formula):
    """Extract all variable names from a formula string"""
    if not formula:
        return []
    
    # Pattern: word characters and underscores, but not starting with number
    variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
    variables = re.findall(variable_pattern, formula)
    
    # Filter out built-in JavaScript functions and constants
    built_ins = ['min', 'max', 'abs', 'round', 'ceil', 'floor', 'sqrt', 'pow', 
                  'log', 'log10', 'exp', 'sin', 'cos', 'tan', 'pi', 'e',
                  'parseFloat', 'parseInt', 'Number', 'Math', 'true', 'false', 'null']
    
    return [var for var in variables if var not in built_ins]

def analyze_calculated_field_dependencies(calculated_fields):
    """Analyze dependencies between calculated fields"""
    dependency_map = {}
    
    for field in calculated_fields:
        field_name = field['name']
        formula = field['formula']
        
        # Find dependencies in the formula
        dependencies = extract_dependencies_from_formula(formula)
        
        # Filter to only include calculated field dependencies
        calculated_dependencies = [dep for dep in dependencies if dep.startswith('_CALCULATED_')]
        
        dependency_map[field_name] = {
            'all_dependencies': dependencies,
            'calculated_dependencies': calculated_dependencies,
            'dependency_count': len(calculated_dependencies)
        }
    
    # Count fields with dependencies
    fields_with_dependencies = sum(1 for info in dependency_map.values() if info['dependency_count'] > 0)
    
    return {
        'dependency_map': dependency_map,
        'fields_with_dependencies': fields_with_dependencies,
        'total_fields': len(calculated_fields)
    }

def topological_sort(dependency_graph):
    """Perform topological sort on dependency graph using Kahn's algorithm"""
    # Create a copy of the graph to avoid modifying the original
    graph = {k: set(v) for k, v in dependency_graph.items()}
    
    # Calculate in-degrees
    in_degree = {node: 0 for node in graph}
    for dependencies in graph.values():
        for dep in dependencies:
            if dep in in_degree:
                in_degree[dep] += 1
            else:
                in_degree[dep] = 1
    
    # Initialize queue with nodes having no incoming edges
    queue = [node for node, degree in in_degree.items() if degree == 0]
    result = []
    
    while queue:
        # Remove a node from the queue
        node = queue.pop(0)
        result.append(node)
        
        # Reduce in-degree of all neighbors
        if node in graph:
            for neighbor in graph[node]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
    
    # Check for circular dependencies
    if len(result) != len(graph):
        # Circular dependency detected
        remaining = [node for node in graph if node not in result]
        logger.warning(f"Circular dependency detected in nodes: {remaining}")
        # Fallback to sequence-based ordering
        return list(graph.keys())
    
    return result

def get_calculation_order_suggestion(analysis):
    """Get suggested calculation order based on dependency analysis"""
    dependency_map = analysis['dependency_map']
    
    # Build dependency graph for topological sort
    dependency_graph = {}
    for field_name, info in dependency_map.items():
        dependency_graph[field_name] = set(info['calculated_dependencies'])
    
    # Perform topological sort
    try:
        sorted_order = topological_sort(dependency_graph)
        return {
            'calculation_order': sorted_order,
            'has_circular_dependencies': False
        }
    except Exception as e:
        logger.error(f"Error in topological sort: {e}")
        # Fallback to sequence-based ordering
        return {
            'calculation_order': list(dependency_graph.keys()),
            'has_circular_dependencies': True
        }

def test_dependency_resolution():
    """Test the dependency resolution system"""
    print("Testing ConfigMatrix Calculated Field Dependency Resolution")
    print("=" * 60)
    
    # Sample calculated fields (similar to the real data)
    calculated_fields = [
        {
            'name': '_CALCULATED_largest_door_height',
            'formula': 'sws_wscrn_hand_built_window_screen === \'no\' ? (parseFloat(sws_wscrn_make_window_screen_height_mm) || 0) : (sws_wscrn_hand_built_type === \'angled_out_of_square_bow_different_heights_widths\' ? (Math.max((Number(sws_wscrn_square_make_height_mm) + Number(sws_wscrn_top_left_top_out_of_square_dimension_mm) + Number(sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm)), (Number(sws_wscrn_square_make_height_mm) + Number(sws_wscrn_top_right_top_out_of_square_dimension_mm) + Number(sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm))) || 0) : "")',
            'description': 'Max height of the window screen',
            'sequence': 3,
            'category': 'basic',
            'data_type': 'number',
            'is_pricing_matrix': False
        },
        {
            'name': '_CALCULATED_largest_door_width',
            'formula': 'sws_wscrn_hand_built_window_screen === \'no\' ? (parseFloat(sws_wscrn_make_window_screen_width_mm) || 0) : (sws_wscrn_hand_built_type === \'angled_out_of_square_bow_different_heights_widths\' ? (Math.max((Number(sws_wscrn_square_make_width_mm) + Number(sws_wscrn_top_left_side_out_of_square_dimension_mm) + Number(sws_wscrn_top_right_side_out_of_square_dimension_mm)), (Number(sws_wscrn_square_make_width_mm) + Number(sws_wscrn_middle_left_side_out_of_square_dimension_mm) + Number(sws_wscrn_middle_right_side_out_of_square_dimension_mm)), (Number(sws_wscrn_square_make_width_mm) + Number(sws_wscrn_bottom_left_side_out_of_square_dimension_mm) + Number(sws_wscrn_bottom_right_side_out_of_square_dimension_mm))) || 0) : "")',
            'description': 'Max width of the window screen.',
            'sequence': 3,
            'category': 'basic',
            'data_type': 'number',
            'is_pricing_matrix': False
        },
        {
            'name': '_CALCULATED_Middle_width',
            'formula': '_CALCULATED_largest_door_width',
            'description': 'Middle width calculation',
            'sequence': 4,
            'category': 'basic',
            'data_type': 'number',
            'is_pricing_matrix': False
        },
        {
            'name': '_CALCULATED_CommandeX_Midrail_Qty',
            'formula': '_CALCULATED_Middle_width > 1200 ? 2 : 1',
            'description': 'CommandeX Midrail Quantity',
            'sequence': 5,
            'category': 'basic',
            'data_type': 'number',
            'is_pricing_matrix': False
        }
    ]
    
    print(f"Testing with {len(calculated_fields)} calculated fields")
    print()
    
    # Test 1: Dependency Analysis
    print("1. Dependency Analysis")
    print("-" * 30)
    analysis = analyze_calculated_field_dependencies(calculated_fields)
    
    print(f"Total fields: {analysis['total_fields']}")
    print(f"Fields with dependencies: {analysis['fields_with_dependencies']}")
    print()
    
    for field_name, info in analysis['dependency_map'].items():
        if info['dependency_count'] > 0:
            print(f"{field_name}:")
            print(f"  Dependencies: {info['calculated_dependencies']}")
            print(f"  All variables: {info['all_dependencies']}")
            print()
    
    # Test 2: Calculation Order Suggestion
    print("2. Calculation Order Suggestion")
    print("-" * 30)
    order_suggestion = get_calculation_order_suggestion(analysis)
    
    if order_suggestion['has_circular_dependencies']:
        print("⚠️  Circular dependencies detected! Using sequence-based ordering.")
    else:
        print("✅ No circular dependencies detected.")
    
    print("Suggested calculation order:")
    for i, field_name in enumerate(order_suggestion['calculation_order'], 1):
        print(f"  {i}. {field_name}")
    print()
    
    # Test 3: Simulate Multi-Pass Calculation
    print("3. Multi-Pass Calculation Simulation")
    print("-" * 30)
    
    # Sample input values
    test_values = {
        'sws_wscrn_hand_built_window_screen': 'no',
        'sws_wscrn_make_window_screen_height_mm': '1425',
        'sws_wscrn_make_window_screen_width_mm': '945',
        'sws_wscrn_hand_built_type': 'angled_out_of_square_bow_different_heights_widths',
        'sws_wscrn_square_make_height_mm': '1400',
        'sws_wscrn_square_make_width_mm': '900',
        'sws_wscrn_top_left_top_out_of_square_dimension_mm': '25',
        'sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm': '0',
        'sws_wscrn_top_right_top_out_of_square_dimension_mm': '25',
        'sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm': '0',
        'sws_wscrn_top_left_side_out_of_square_dimension_mm': '25',
        'sws_wscrn_top_right_side_out_of_square_dimension_mm': '25',
        'sws_wscrn_middle_left_side_out_of_square_dimension_mm': '20',
        'sws_wscrn_middle_right_side_out_of_square_dimension_mm': '20',
        'sws_wscrn_bottom_left_side_out_of_square_dimension_mm': '0',
        'sws_wscrn_bottom_right_side_out_of_square_dimension_mm': '0'
    }
    
    print("Input values:")
    for key, value in test_values.items():
        print(f"  {key}: {value}")
    print()
    
    # Simulate the calculation process
    results = {}
    context = dict(test_values)
    
    # Add math functions
    context.update({
        'min': min,
        'max': max,
        'parseFloat': lambda x: float(x) if x not in [None, ''] else 0,
        'parseInt': lambda x: int(float(x)) if x not in [None, ''] else 0,
        'Number': lambda x: float(x) if x not in [None, ''] else 0,
    })
    
    # Initialize results
    for field in calculated_fields:
        results[field['name']] = None
    
    # Multi-pass calculation
    max_passes = 5
    for pass_num in range(max_passes):
        print(f"Pass {pass_num + 1}:")
        fields_calculated = 0
        
        for field in calculated_fields:
            field_name = field['name']
            
            if results[field_name] is not None:
                continue
                
            try:
                # Update context with current results
                context.update(results)
                
                # Convert formula (simplified version)
                formula = field['formula']
                formula = formula.replace('===', '==')
                formula = formula.replace('&&', ' and ')
                formula = formula.replace('||', ' or ')
                formula = formula.replace('Math.max(', 'max(')
                formula = formula.replace('Math.min(', 'min(')
                formula = formula.replace('Number(', 'parseFloat(')
                
                # Try to evaluate
                try:
                    result = eval(formula, context)
                    results[field_name] = result
                    context[field_name] = result
                    fields_calculated += 1
                    print(f"  ✅ {field_name} = {result}")
                except NameError as ne:
                    print(f"  ⏳ {field_name} - waiting for dependencies: {ne}")
                except Exception as e:
                    print(f"  ❌ {field_name} - error: {e}")
                    
            except Exception as e:
                print(f"  ❌ {field_name} - processing error: {e}")
        
        print(f"  Pass {pass_num + 1} completed: {fields_calculated} fields calculated")
        print()
        
        if fields_calculated == 0:
            print("No more fields can be calculated. Stopping.")
            break
    
    # Final results
    print("Final Results:")
    print("-" * 30)
    for field_name, result in results.items():
        status = "✅" if result is not None else "❌"
        print(f"{status} {field_name}: {result}")
    
    print()
    print("Test completed!")

if __name__ == "__main__":
    test_dependency_resolution()
