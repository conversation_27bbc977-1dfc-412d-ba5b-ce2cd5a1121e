#!/usr/bin/env python3
"""
Quick script to assign BasiX cut plans to matrix cells.
Run this in Odoo shell to automatically configure the matrix.
"""

import json

def assign_basix_cut_plans(env):
    """Assign BasiX cut plans to appropriate matrix cells"""
    
    # Find the BasiX matrix
    matrix = env['mesh.cut.matrix'].search([
        ('mesh_series', '=', 'basix'),
        ('name', 'ilike', 'BasiX')
    ], limit=1)
    
    if not matrix:
        print("❌ BasiX matrix not found")
        return False
    
    print(f"✓ Found matrix: {matrix.name}")
    print(f"✓ Matrix ID: {matrix.id}")
    
    # Find cut plans
    cut_plans = matrix.cut_plan_ids
    print(f"✓ Found {len(cut_plans)} cut plans:")
    for plan in cut_plans:
        print(f"  - {plan.name} (ID: {plan.id}): {plan.cut_width}x{plan.cut_height}mm")
    
    if not cut_plans:
        print("❌ No cut plans found. Please create cut plans first.")
        return False
    
    # Get current matrix data
    try:
        matrix_data = json.loads(matrix.matrix_data or '{}')
        print(f"✓ Current matrix has {len(matrix_data)} configured cells")
    except:
        matrix_data = {}
        print("✓ Starting with empty matrix data")
    
    # Assign cut plans to cells
    assignments = []
    
    for plan in cut_plans:
        # Cell key format: "height_width"
        cell_key = f"{int(plan.cut_height)}_{int(plan.cut_width)}"
        
        # Assign cut plan to cell
        matrix_data[cell_key] = {
            'cut_plan_id': plan.id,
            'cut_plan_name': plan.name,
            'has_cut_plan': True,
            'master_size': f'{int(plan.cut_width)}x{int(plan.cut_height)}',
            'efficiency': 1.0,
            'byproduct_count': len(plan.byproduct_ids),
            'arrow_direction': 'auto'  # Default arrow direction
        }
        
        assignments.append(f"Cell {cell_key} → {plan.name}")
        print(f"✓ Assigned {plan.name} to cell {cell_key}")
    
    # Save matrix data
    matrix.matrix_data = json.dumps(matrix_data)
    print(f"✓ Updated matrix with {len(assignments)} assignments")
    
    # Test the lookup
    print("\n🧪 Testing matrix lookup for 1200x2400mm:")
    result = matrix._get_matrix_cut_plan(1200, 2400)
    if result:
        print("✅ SUCCESS! Matrix lookup now works:")
        print(f"  - Cut plan: {result['cut_plan'].name}")
        print(f"  - Master sheet: {result['product'].name if result.get('product') else 'Not found'}")
        print(f"  - Matrix cell: {result.get('matrix_cell_width')}x{result.get('matrix_cell_height')}mm")
    else:
        print("❌ Matrix lookup still not working")
    
    print(f"\n📋 Summary:")
    for assignment in assignments:
        print(f"  ✓ {assignment}")
    
    return True

# Auto-run if executed directly
if __name__ == '__main__':
    print("BasiX Cut Plan Assignment Script")
    print("=" * 50)
    print("Run this in Odoo shell:")
    print("$ odoo shell -d your_database")
    print(">>> exec(open('canbrax_configmatrix/scripts/assign_basix_cut_plans.py').read())")
    print(">>> assign_basix_cut_plans(env)")
