#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set up BasiX matrix with proper cut plan assignments.
Run this after module installation to ensure matrix cells are properly configured.

Usage:
    python setup_basix_matrix.py
"""

import json

def setup_basix_matrix(env):
    """Set up BasiX matrix with cut plan assignments"""
    
    # Find the BasiX matrix
    matrix = env['mesh.cut.matrix'].search([
        ('mesh_series', '=', 'basix'),
        ('active', '=', True)
    ], limit=1)
    
    if not matrix:
        print("❌ BasiX matrix not found. Please install the module first.")
        return False
    
    print(f"✓ Found BasiX matrix: {matrix.name}")
    
    # Find cut plans
    cut_plans = matrix.cut_plan_ids
    print(f"✓ Found {len(cut_plans)} cut plans:")
    for plan in cut_plans:
        print(f"  - {plan.name}: {plan.cut_width}x{plan.cut_height}mm")
    
    # Set up matrix data
    matrix_data = {}
    
    # Assign 1200x2400 cut plan to appropriate cells
    plan_1200x2400 = cut_plans.filtered(lambda p: p.cut_width == 1200 and p.cut_height == 2400)
    if plan_1200x2400:
        # Cell key format: "height_width"
        cell_key = "2400_1200"
        matrix_data[cell_key] = {
            'cut_plan_id': plan_1200x2400[0].id,
            'cut_plan_name': plan_1200x2400[0].name,
            'has_cut_plan': True,
            'master_size': '1200x2400',
            'efficiency': 1.0,
            'byproduct_count': 0
        }
        print(f"✓ Assigned {plan_1200x2400[0].name} to cell {cell_key}")
    
    # Assign 975x1500 cut plan to appropriate cells
    plan_975x1500 = cut_plans.filtered(lambda p: p.cut_width == 975 and p.cut_height == 1500)
    if plan_975x1500:
        # Cell key format: "height_width"
        cell_key = "1500_975"
        matrix_data[cell_key] = {
            'cut_plan_id': plan_975x1500[0].id,
            'cut_plan_name': plan_975x1500[0].name,
            'has_cut_plan': True,
            'master_size': '975x1500',
            'efficiency': 1.0,
            'byproduct_count': 0
        }
        print(f"✓ Assigned {plan_975x1500[0].name} to cell {cell_key}")
    
    # Update matrix data
    matrix.matrix_data = json.dumps(matrix_data)
    print(f"✓ Updated matrix data with {len(matrix_data)} configured cells")
    
    # Test the matrix lookup
    print("\n🧪 Testing matrix lookup for 1200x2400mm:")
    result = matrix.get_best_fit(1200, 2400)
    if result:
        print(f"✓ Matrix lookup successful:")
        print(f"  - Type: {result.get('type')}")
        print(f"  - Source: {result.get('source')}")
        if result.get('cut_plan'):
            print(f"  - Cut plan: {result['cut_plan'].name}")
        if result.get('product'):
            print(f"  - Master sheet: {result['product'].name}")
        print(f"  - Efficiency: {result.get('efficiency', 0)*100:.1f}%")
    else:
        print("❌ Matrix lookup failed")
    
    return True

if __name__ == '__main__':
    print("BasiX Matrix Setup Script")
    print("=" * 50)
    print("This script should be run from within Odoo shell:")
    print("$ odoo shell -d your_database")
    print(">>> exec(open('canbrax_configmatrix/scripts/setup_basix_matrix.py').read())")
    print(">>> setup_basix_matrix(env)")
