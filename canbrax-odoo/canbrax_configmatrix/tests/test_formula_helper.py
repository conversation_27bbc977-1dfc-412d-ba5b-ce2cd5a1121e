# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class TestConfigMatrixFormulaHelper(TransactionCase):
    """Test cases for the ConfigMatrix Formula Helper"""

    def setUp(self):
        super().setUp()
        # Get the formula helper model
        self.formula_helper = self.env['config.matrix.formula.helper']

    def test_js_to_python_conversion(self):
        """Test JavaScript to Python conversion"""
        # Test basic operators
        self.assertEqual(
            self.formula_helper.convert_js_to_python('a === b'),
            'a == b'
        )
        self.assertEqual(
            self.formula_helper.convert_js_to_python('a !== b'),
            'a != b'
        )
        self.assertEqual(
            self.formula_helper.convert_js_to_python('a && b'),
            'a and b'
        )
        self.assertEqual(
            self.formula_helper.convert_js_to_python('a || b'),
            'a or b'
        )

        # Test Math functions
        self.assertEqual(
            self.formula_helper.convert_js_to_python('Math.min(a, b)'),
            'min(a, b)'
        )
        self.assertEqual(
            self.formula_helper.convert_js_to_python('Math.max(a, b)'),
            'max(a, b)'
        )

        # Test Number function
        self.assertEqual(
            self.formula_helper.convert_js_to_python('Number(value)'),
            'parseFloat(value)'
        )

    def test_ternary_operator_conversion(self):
        """Test ternary operator conversion"""
        # Simple ternary
        self.assertEqual(
            self.formula_helper.convert_js_to_python('a ? b : c'),
            '(b if (a) else c)'
        )

        # Nested ternary
        self.assertEqual(
            self.formula_helper.convert_js_to_python('a ? b ? c : d : e'),
            '(c if (b) else d if (a) else e)'
        )

    def test_string_concatenation_conversion(self):
        """Test string concatenation conversion"""
        # Variable + string
        self.assertEqual(
            self.formula_helper.convert_js_to_python('field + "mm"'),
            'str(field) + "mm"'
        )

        # String + variable
        self.assertEqual(
            self.formula_helper.convert_js_to_python('"Width: " + width'),
            '"Width: " + str(width)'
        )

    def test_evaluation_context_building(self):
        """Test evaluation context building"""
        config_values = {'field1': 10, 'field2': 20}
        context = self.formula_helper.build_evaluation_context(config_values)

        # Check configuration values are included
        self.assertIn('field1', context)
        self.assertIn('field2', context)
        self.assertEqual(context['field1'], 10)
        self.assertEqual(context['field2'], 20)

        # Check boolean context
        self.assertIn('true', context)
        self.assertIn('false', context)
        self.assertIn('True', context)
        self.assertIn('False', context)

        # Check math functions
        self.assertIn('min', context)
        self.assertIn('max', context)
        self.assertIn('round', context)

        # Check JavaScript functions
        self.assertIn('parseFloat', context)
        self.assertIn('parseInt', context)
        self.assertIn('Number', context)

    def test_formula_evaluation(self):
        """Test formula evaluation"""
        config_values = {'width': 100, 'height': 200}
        
        # Simple arithmetic
        result = self.formula_helper.evaluate_formula('width + height', config_values)
        self.assertEqual(result, 300)

        # With math functions
        result = self.formula_helper.evaluate_formula('Math.min(width, height)', config_values)
        self.assertEqual(result, 100)

        # With JavaScript functions
        result = self.formula_helper.evaluate_formula('Number(width) * 2', config_values)
        self.assertEqual(result, 200)

    def test_condition_evaluation(self):
        """Test condition evaluation"""
        config_values = {'width': 100, 'height': 200}
        
        # Simple condition
        result = self.formula_helper.evaluate_condition('width > 50', config_values)
        self.assertTrue(result)

        # Complex condition
        result = self.formula_helper.evaluate_condition('width > 50 && height < 300', config_values)
        self.assertTrue(result)

        # False condition
        result = self.formula_helper.evaluate_condition('width > 200', config_values)
        self.assertFalse(result)

    def test_visibility_condition_evaluation(self):
        """Test visibility condition evaluation"""
        field_values = {'field1': True, 'field2': False}
        
        # Simple condition
        result = self.formula_helper.evaluate_visibility_condition('field1', field_values)
        self.assertTrue(result)

        # JSON condition marker
        json_condition = '__JSON__[{"condition": "field1", "logic": "and"}, {"condition": "field2", "logic": "or"}]'
        result = self.formula_helper.evaluate_visibility_condition(json_condition, field_values)
        self.assertTrue(result)  # field1 is True, so result is True

    def test_component_quantity_calculation(self):
        """Test component quantity calculation"""
        config_values = {'width': 1000, 'height': 2000}
        
        # Simple formula
        result = self.formula_helper.calculate_component_quantity('width / 1000', config_values)
        self.assertEqual(result, 1.0)

        # Complex formula
        result = self.formula_helper.calculate_component_quantity('Math.ceil(width / 500)', config_values)
        self.assertEqual(result, 2.0)

        # With default value
        result = self.formula_helper.calculate_component_quantity('', config_values, default=5.0)
        self.assertEqual(result, 5.0)

    def test_formula_validation(self):
        """Test formula validation"""
        # Valid formula
        result = self.formula_helper.validate_formula_syntax('width + height')
        self.assertEqual(result['status'], 'valid')
        self.assertIsNotNone(result['python_formula'])

        # Invalid formula (empty)
        result = self.formula_helper.validate_formula_syntax('')
        self.assertEqual(result['status'], 'error')

    def test_error_handling(self):
        """Test error handling in formula evaluation"""
        config_values = {'width': 100}
        
        # Formula with undefined variable
        result = self.formula_helper.evaluate_formula('undefined_var + width', config_values, default_value='error')
        self.assertEqual(result, 'error')

        # Invalid syntax
        result = self.formula_helper.evaluate_formula('width +', config_values, default_value='syntax_error')
        self.assertEqual(result, 'syntax_error')

    def test_performance_with_large_context(self):
        """Test performance with large evaluation context"""
        # Create large configuration values
        config_values = {f'field_{i}': i for i in range(1000)}
        
        # Test evaluation performance
        import time
        start_time = time.time()
        
        result = self.formula_helper.evaluate_formula('field_500 + field_600', config_values)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete in reasonable time (less than 1 second)
        self.assertLess(execution_time, 1.0)
        self.assertEqual(result, 1100)

    def test_complex_formulas(self):
        """Test complex formula scenarios"""
        config_values = {
            'width': 1000,
            'height': 2000,
            'has_midrail': True,
            'midrail_height': 1200
        }
        
        # Complex conditional formula
        formula = 'has_midrail ? Math.max(width, height) + midrail_height : width * height / 1000'
        result = self.formula_helper.evaluate_formula(formula, config_values)
        self.assertEqual(result, 3200)  # max(1000, 2000) + 1200 = 2000 + 1200 = 3200

        # Formula with multiple math functions
        formula = 'Math.ceil(width / 500) * Math.floor(height / 1000)'
        result = self.formula_helper.evaluate_formula(formula, config_values)
        self.assertEqual(result, 4)  # ceil(1000/500) * floor(2000/1000) = 2 * 2 = 4

    def test_evaluate_formula_with_custom_context(self):
        """Test formula evaluation with custom functions in context"""
        config_values = {'width': 100, 'height': 200}
        
        # Custom function that doubles the input
        custom_functions = {
            'double': lambda x: x * 2,
            'add_ten': lambda x: x + 10
        }
        
        # Formula using custom functions
        formula = 'double(width) + add_ten(height)'
        result = self.formula_helper.evaluate_formula_with_custom_context(
            formula, config_values, custom_functions
        )
        # double(100) + add_ten(200) = 200 + 210 = 410
        self.assertEqual(result, 410)
        
        # Test with default value
        result = self.formula_helper.evaluate_formula_with_custom_context(
            '', config_values, custom_functions, default_value=999
        )
        self.assertEqual(result, 999)
        
        # Test error handling
        result = self.formula_helper.evaluate_formula_with_custom_context(
            'undefined_function(width)', config_values, custom_functions, default_value=0
        )
        self.assertEqual(result, 0)
