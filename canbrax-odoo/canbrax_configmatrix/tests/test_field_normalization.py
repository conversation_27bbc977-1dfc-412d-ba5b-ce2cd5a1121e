# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
import logging

_logger = logging.getLogger(__name__)


class TestFieldNormalization(TransactionCase):
    """Test field value normalization system"""

    def setUp(self):
        super().setUp()
        
        # Create a test template
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Normalization Template',
            'code': 'TEST_NORM',
            'description': 'Template for testing field normalization'
        })
        
        # Create a test section
        self.section = self.env['config.matrix.section'].create({
            'name': 'Test Section',
            'template_id': self.template.id,
            'sequence': 10
        })
        
        # Create test fields with different types
        self.integer_field = self.env['config.matrix.field'].create({
            'name': 'Test Integer Field',
            'technical_name': 'test_integer',
            'field_type': 'integer',
            'section_id': self.section.id,
            'question_number': 1
        })
        
        self.float_field = self.env['config.matrix.field'].create({
            'name': 'Test Float Field',
            'technical_name': 'test_float',
            'field_type': 'float',
            'section_id': self.section.id,
            'question_number': 2
        })
        
        self.boolean_field = self.env['config.matrix.field'].create({
            'name': 'Test Boolean Field',
            'technical_name': 'test_boolean',
            'field_type': 'boolean',
            'section_id': self.section.id,
            'question_number': 3
        })
        
        self.selection_field = self.env['config.matrix.field'].create({
            'name': 'Test Selection Field',
            'technical_name': 'test_selection',
            'field_type': 'selection',
            'section_id': self.section.id,
            'question_number': 4
        })
        
        # Create an operation template for testing
        self.operation_template = self.env['config.matrix.operation.template'].create({
            'name': 'Test Operation Template',
            'config_template_id': self.template.id,
            'cost_formula': 'test_integer * test_float + (10 if test_boolean else 0)',
            'duration_formula': 'test_integer + test_float'
        })

    def test_normalizer_basic_functionality(self):
        """Test basic normalization functionality"""
        normalizer = self.env['config.matrix.field.normalizer']
        
        # Test data with mixed types (simulating UI input)
        test_field_values = {
            str(self.integer_field.id): '5',  # String integer
            'test_integer': '5',  # String integer by technical name
            str(self.float_field.id): '3.14',  # String float
            'test_float': '3.14',  # String float by technical name
            str(self.boolean_field.id): 'true',  # String boolean
            'test_boolean': 'true',  # String boolean by technical name
            str(self.selection_field.id): 'option1',  # String selection
            'test_selection': 'option1',  # String selection by technical name
            '_CALCULATED_some_field': 42.5,  # Calculated field (should be preserved)
        }
        
        # Normalize the values
        normalized = normalizer.normalize_field_values(
            test_field_values, 
            self.template.id,
            {'source': 'test'}
        )
        
        # Verify normalization results
        self.assertEqual(normalized[str(self.integer_field.id)], 5)  # Should be int
        self.assertEqual(normalized['test_integer'], 5)  # Should be int
        self.assertEqual(normalized[str(self.float_field.id)], 3.14)  # Should be float
        self.assertEqual(normalized['test_float'], 3.14)  # Should be float
        self.assertEqual(normalized[str(self.boolean_field.id)], True)  # Should be bool
        self.assertEqual(normalized['test_boolean'], True)  # Should be bool
        self.assertEqual(normalized[str(self.selection_field.id)], 'option1')  # Should be string
        self.assertEqual(normalized['test_selection'], 'option1')  # Should be string
        self.assertEqual(normalized['_CALCULATED_some_field'], 42.5)  # Should be preserved

    def test_normalizer_edge_cases(self):
        """Test normalization edge cases"""
        normalizer = self.env['config.matrix.field.normalizer']
        
        # Test edge cases
        edge_case_values = {
            str(self.integer_field.id): '',  # Empty string
            str(self.float_field.id): None,  # None value
            str(self.boolean_field.id): '0',  # String zero (should be False)
            str(self.selection_field.id): '  option2  ',  # String with whitespace
        }
        
        normalized = normalizer.normalize_field_values(
            edge_case_values,
            self.template.id,
            {'source': 'test_edge_cases'}
        )
        
        # Verify edge case handling
        self.assertEqual(normalized[str(self.integer_field.id)], 0)  # Empty string -> 0
        self.assertEqual(normalized[str(self.float_field.id)], 0.0)  # None -> 0.0
        self.assertEqual(normalized[str(self.boolean_field.id)], False)  # '0' -> False
        self.assertEqual(normalized[str(self.selection_field.id)], 'option2')  # Trimmed

    def test_operation_template_with_normalization(self):
        """Test that operation templates use normalized values"""
        # Test data with string values (simulating UI input)
        test_config_values = {
            'test_integer': '10',  # String
            'test_float': '2.5',   # String
            'test_boolean': 'true' # String
        }
        
        # Calculate cost - should use normalized values
        cost = self.operation_template.get_calculated_cost(test_config_values)
        
        # Expected: 10 * 2.5 + (10 if True else 0) = 25 + 10 = 35
        self.assertEqual(cost, 35.0)
        
        # Calculate duration - should use normalized values
        duration = self.operation_template.get_calculated_duration(test_config_values)
        
        # Expected: 10 + 2.5 = 12.5
        self.assertEqual(duration, 12.5)

    def test_normalization_with_invalid_template(self):
        """Test normalization with invalid template ID"""
        normalizer = self.env['config.matrix.field.normalizer']
        
        test_values = {'test_field': 'test_value'}
        
        # Test with non-existent template
        normalized = normalizer.normalize_field_values(
            test_values,
            99999,  # Non-existent template ID
            {'source': 'test_invalid'}
        )
        
        # Should return original values as fallback
        self.assertEqual(normalized, test_values)

    def test_normalization_performance(self):
        """Test normalization performance with large field sets"""
        normalizer = self.env['config.matrix.field.normalizer']
        
        # Create a large set of field values
        large_field_values = {}
        for i in range(100):
            large_field_values[f'field_{i}'] = str(i)  # All as strings
        
        # Add our test fields
        large_field_values.update({
            str(self.integer_field.id): '42',
            str(self.float_field.id): '3.14159',
            str(self.boolean_field.id): 'true'
        })
        
        # Normalize - should handle large datasets efficiently
        normalized = normalizer.normalize_field_values(
            large_field_values,
            self.template.id,
            {'source': 'test_performance'}
        )
        
        # Verify known fields are normalized correctly
        self.assertEqual(normalized[str(self.integer_field.id)], 42)
        self.assertEqual(normalized[str(self.float_field.id)], 3.14159)
        self.assertEqual(normalized[str(self.boolean_field.id)], True)
        
        # Verify unknown fields default to text (string) normalization
        self.assertEqual(normalized['field_0'], '0')
        self.assertEqual(normalized['field_99'], '99')

    def test_context_type_detection(self):
        """Test that context type is properly detected and logged"""
        normalizer = self.env['config.matrix.field.normalizer']
        
        test_values = {str(self.integer_field.id): '123'}
        
        # Test different context types
        contexts = [
            {'source': 'ui_field_option_mapping'},
            {'source': 'backend_save_config'},
            {'source': 'template_calculation', 'context_type': 'UI_FIELD_OPTION_MAPPING'}
        ]
        
        for context in contexts:
            normalized = normalizer.normalize_field_values(
                test_values,
                self.template.id,
                context
            )
            
            # Should normalize correctly regardless of context
            self.assertEqual(normalized[str(self.integer_field.id)], 123)

    def test_convenience_method(self):
        """Test the convenience method for template calculations"""
        normalizer = self.env['config.matrix.field.normalizer']
        
        test_values = {
            'test_integer': '7',
            'test_float': '1.5'
        }
        
        # Use convenience method
        normalized = normalizer.normalize_for_template_calculation(
            test_values,
            self.template.id,
            'UI_FIELD_OPTION_MAPPING'
        )
        
        # Should normalize correctly
        self.assertEqual(normalized['test_integer'], 7)
        self.assertEqual(normalized['test_float'], 1.5)
