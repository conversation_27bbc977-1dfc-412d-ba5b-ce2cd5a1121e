# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class TestDynamicPricing(TransactionCase):
    """Test the new dynamic pricing system"""

    def setUp(self):
        super().setUp()
        
        # Create test product template
        self.product_template = self.env['product.template'].create({
            'name': 'Test Configurable Product',
            'type': 'product',
        })
        
        # Create test configuration template
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Dynamic Pricing Template',
            'code': 'TEST_DYNAMIC_001',
            'product_template_id': self.product_template.id,
            'panel_quantity': '1',
        })

    def test_no_matrices_available(self):
        """Test pricing when no matrices are available"""
        result = self.template.get_configuration_price({
            'door_height': 2000,
            'door_width': 1000
        })
        
        self.assertEqual(result['price'], 0.0)
        self.assertEqual(len(result['breakdown']), 0)

    def test_matrix_without_conditions(self):
        """Test matrix without conditions (always applies)"""
        # Create a matrix without conditions
        matrix = self.env['config.matrix.price.matrix'].create({
            'name': 'Always Apply Matrix',
            'is_sale_price_matrix': True,
            'matrix_id': self.template.id,
            'special_conditions': '',  # No conditions
            'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'matrix_data': '{"3000_3000": 100.0}',
            'height_calculated_field_id': self._create_calculated_field('door_height'),
            'width_calculated_field_id': self._create_calculated_field('door_width'),
        })
        
        result = self.template.get_configuration_price({
            'door_height': 2000,
            'door_width': 1000
        })
        
        self.assertEqual(result['price'], 100.0)
        self.assertEqual(len(result['breakdown']), 1)
        self.assertEqual(result['breakdown'][0]['matrix_name'], 'Always Apply Matrix')
        self.assertEqual(result['breakdown'][0]['has_conditions'], False)

    def test_matrix_with_conditions(self):
        """Test matrix with conditions"""
        # Create a matrix with conditions
        matrix = self.env['config.matrix.price.matrix'].create({
            'name': 'Conditional Matrix',
            'is_sale_price_matrix': True,
            'matrix_id': self.template.id,
            'special_conditions': 'door_height >= 2000',
            'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'matrix_data': '{"3000_3000": 150.0}',
            'height_calculated_field_id': self._create_calculated_field('door_height'),
            'width_calculated_field_id': self._create_calculated_field('door_width'),
        })
        
        # Test with condition that passes
        result = self.template.get_configuration_price({
            'door_height': 2500,  # >= 2000, should pass
            'door_width': 1000
        })
        
        self.assertEqual(result['price'], 150.0)
        self.assertEqual(len(result['breakdown']), 1)
        self.assertEqual(result['breakdown'][0]['has_conditions'], True)
        
        # Test with condition that fails
        result = self.template.get_configuration_price({
            'door_height': 1500,  # < 2000, should fail
            'door_width': 1000
        })
        
        self.assertEqual(result['price'], 0.0)
        self.assertEqual(len(result['breakdown']), 0)

    def test_multiple_matrices(self):
        """Test multiple matrices applying"""
        # Create matrix without conditions
        matrix1 = self.env['config.matrix.price.matrix'].create({
            'name': 'Always Apply Matrix',
            'is_sale_price_matrix': True,
            'matrix_id': self.template.id,
            'special_conditions': '',
            'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'matrix_data': '{"3000_3000": 100.0}',
            'height_calculated_field_id': self._create_calculated_field('door_height'),
            'width_calculated_field_id': self._create_calculated_field('door_width'),
        })
        
        # Create matrix with conditions
        matrix2 = self.env['config.matrix.price.matrix'].create({
            'name': 'Conditional Matrix',
            'is_sale_price_matrix': True,
            'matrix_id': self.template.id,
            'special_conditions': 'door_height >= 2000',
            'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'matrix_data': '{"3000_3000": 150.0}',
            'height_calculated_field_id': self._create_calculated_field('door_height'),
            'width_calculated_field_id': self._create_calculated_field('door_width'),
        })
        
        result = self.template.get_configuration_price({
            'door_height': 2500,  # Both matrices should apply
            'door_width': 1000
        })
        
        self.assertEqual(result['price'], 250.0)  # 100 + 150
        self.assertEqual(len(result['breakdown']), 2)

    def test_quantity_multiplier(self):
        """Test quantity multiplier application"""
        matrix = self.env['config.matrix.price.matrix'].create({
            'name': 'Test Matrix',
            'is_sale_price_matrix': True,
            'matrix_id': self.template.id,
            'special_conditions': '',
            'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'matrix_data': '{"3000_3000": 100.0}',
            'height_calculated_field_id': self._create_calculated_field('door_height'),
            'width_calculated_field_id': self._create_calculated_field('door_width'),
        })
        
        # Test with quantity multiplier
        result = self.template.get_configuration_price({
            'door_height': 2000,
            'door_width': 1000,
            'test_quantity': '2'  # Should multiply by 2
        })
        
        self.assertEqual(result['price'], 200.0)  # 100 * 2

    def test_dynamic_pricing_evaluation(self):
        """Test the dynamic pricing evaluation method"""
        # Create test matrices
        matrix1 = self.env['config.matrix.price.matrix'].create({
            'name': 'Matrix 1',
            'is_sale_price_matrix': True,
            'matrix_id': self.template.id,
            'special_conditions': '',
            'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'matrix_data': '{"3000_3000": 100.0}',
            'height_calculated_field_id': self._create_calculated_field('door_height'),
            'width_calculated_field_id': self._create_calculated_field('door_width'),
        })
        
        matrix2 = self.env['config.matrix.price.matrix'].create({
            'name': 'Matrix 2',
            'is_sale_price_matrix': True,
            'matrix_id': self.template.id,
            'special_conditions': 'door_height >= 2000',
            'height_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'width_ranges': '[{"min": 0, "max": 3000, "label": "3000"}]',
            'matrix_data': '{"3000_3000": 150.0}',
            'height_calculated_field_id': self._create_calculated_field('door_height'),
            'width_calculated_field_id': self._create_calculated_field('door_width'),
        })
        
        # Test evaluation
        result = self.template.test_dynamic_pricing_evaluation({
            'door_height': 2500,
            'door_width': 1000
        })
        
        self.assertEqual(len(result['available_price_matrices']), 2)
        self.assertEqual(len(result['applicable_matrices']), 2)
        self.assertEqual(result['total_price'], 250.0)

    def _create_calculated_field(self, name):
        """Helper method to create a calculated field"""
        return self.env['config.matrix.calculated.field'].create({
            'name': name,
            'description': f'Test {name} field',
            'field_type': 'float',
            'formula': name,  # Simple formula that returns the field value
        })
