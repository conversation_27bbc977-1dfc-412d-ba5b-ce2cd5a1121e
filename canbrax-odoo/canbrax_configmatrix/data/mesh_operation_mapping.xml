<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 
        This file creates operation mappings that trigger mesh cut operations
        when the calculated fields indicate mesh is required.
        
        Note: These mappings will need to be created after templates are set up,
        as they reference specific calculated fields that may be template-specific.
        -->
        
        <!-- Global Operation Mapping for Mesh Cut Operations -->
        <!-- Operation mappings need to be created for specific fields in templates -->
        <!-- This will be handled by a post-install hook or wizard -->

        <!--
        Example of how to create field operation mapping:
        
        <record id="mesh_operation_mapping_example" model="config.matrix.field.operation.mapping">
            <field name="field_id" ref="field_id_for_calculated_mesh_operation_required"/>
            <field name="operation_template_id" ref="operation_template_mesh_cut"/>
            <field name="sequence">100</field>
            <field name="condition">_CALCULATED_mesh_operation_required == True</field>
            <field name="name">Auto Mesh Cut Operation</field>
            <field name="notes">Automatically creates mesh cut operations when mesh is required</field>
        </record>
        -->
        
        <!-- 
        Alternative approach: Create calculated field that can be used as a trigger
        This calculated field would be created in each template that needs mesh operations
        -->
        
    </data>
</odoo>
