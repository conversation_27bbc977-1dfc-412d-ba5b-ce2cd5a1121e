-- STEP-BY-STEP cleanup script for mesh byproduct constraint errors
-- Run each query ONE AT A TIME in pgAdmin

-- STEP 1: See what stock moves are causing the problem
SELECT 'Problematic stock moves:' as info;
SELECT sm.id as move_id, sm.product_id, p.name as product_name, p.default_code
FROM stock_move sm
JOIN product_product pp ON sm.product_id = pp.id
JOIN product_template p ON pp.product_tmpl_id = p.id
WHERE p.name LIKE '%Off-cut%' OR p.default_code LIKE '%OFF%' OR p.mesh_type IN ('planned', 'unplanned');

-- STEP 2: Delete stock move lines first (run this query)
DELETE FROM stock_move_line WHERE move_id IN (
    SELECT sm.id FROM stock_move sm
    JOIN product_product pp ON sm.product_id = pp.id
    JOIN product_template p ON pp.product_tmpl_id = p.id
    WHERE p.name LIKE '%Off-cut%' OR p.default_code LIKE '%OFF%' OR p.mesh_type IN ('planned', 'unplanned')
);

-- STEP 3: Delete stock moves (run this query)
DELETE FROM stock_move WHERE product_id IN (
    SELECT pp.id FROM product_product pp
    JOIN product_template p ON pp.product_tmpl_id = p.id
    WHERE p.name LIKE '%Off-cut%' OR p.default_code LIKE '%OFF%' OR p.mesh_type IN ('planned', 'unplanned')
);

-- STEP 4: Delete stock quants (run this query)
DELETE FROM stock_quant WHERE product_id IN (
    SELECT pp.id FROM product_product pp
    JOIN product_template p ON pp.product_tmpl_id = p.id
    WHERE p.name LIKE '%Off-cut%' OR p.default_code LIKE '%OFF%' OR p.mesh_type IN ('planned', 'unplanned')
);

-- STEP 5: Now delete byproduct records (run this query)
DELETE FROM mesh_cut_byproduct;

-- STEP 6: Reset sequence (run this query)
SELECT setval('mesh_cut_byproduct_id_seq', 1, false);

-- STEP 7: Verify cleanup (run this query)
SELECT 'Cleanup completed' as status, COUNT(*) as remaining_byproducts FROM mesh_cut_byproduct;
