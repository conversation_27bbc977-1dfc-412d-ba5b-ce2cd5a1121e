#!/usr/bin/env python3
"""
Test script to debug mesh byproduct integration
Run this in Odoo shell to test the complete flow
"""

def test_mesh_byproduct_flow():
    """Test the complete mesh byproduct integration flow"""
    print("=== MESH BYPRODUCT INTEGRATION DEBUG ===")
    print()
    
    # Test with a specific sales order
    so_name = "S00044"  # Replace with actual SO number
    
    # Find the sales order
    so = env['sale.order'].search([('name', '=', so_name)], limit=1)
    if not so:
        print(f"❌ Sales Order {so_name} not found")
        return
    
    print(f"✓ Found Sales Order: {so.name}")
    print(f"  State: {so.state}")
    print(f"  Lines: {len(so.order_line)}")
    print()
    
    # Check configured lines
    configured_lines = so.order_line.filtered(lambda l: l.is_configured and l.config_id)
    print(f"📋 CONFIGURED LINES: {len(configured_lines)}")
    
    for line in configured_lines:
        config = line.config_id
        print(f"  Line {line.id}: {line.product_id.name}")
        print(f"    Config ID: {config.id}")
        print(f"    BOM ID: {config.bom_id.id if config.bom_id else 'None'}")
        
        # Check mesh operations
        mesh_ops = env['mesh.cut.operation'].search([('config_id', '=', config.id)])
        print(f"    Mesh Operations: {len(mesh_ops)}")
        
        for op in mesh_ops:
            print(f"      Op {op.id}: {op.name}")
            print(f"        State: {op.state}")
            print(f"        Source Type: {op.source_type}")
            print(f"        Cut Plan: {op.cut_plan_id.name if op.cut_plan_id else 'None'}")
            if op.cut_plan_id:
                print(f"        Cut Plan Byproducts: {len(op.cut_plan_id.byproduct_ids)}")
                for bp in op.cut_plan_id.byproduct_ids:
                    print(f"          - {bp.product_id.name if bp.product_id else 'No Product'} (qty: {bp.quantity})")
            
            # Check operation byproducts
            print(f"        Operation Byproducts: {len(op.byproduct_ids)}")
            for bp_line in op.byproduct_ids:
                print(f"          - {bp_line.product_id.name if bp_line.product_id else 'No Product'} (qty: {bp_line.quantity})")
        
        # Check BOM byproducts
        if config.bom_id:
            bom = config.bom_id
            print(f"    BOM Byproducts: {len(bom.byproduct_ids)}")
            for bp in bom.byproduct_ids:
                print(f"      - {bp.product_id.name} (qty: {bp.product_qty})")
        print()
    
    # Check Manufacturing Orders
    mos = env['mrp.production'].search([('origin', 'like', so.name)])
    print(f"🏭 MANUFACTURING ORDERS: {len(mos)}")
    
    for mo in mos:
        print(f"  MO {mo.name}: {mo.product_id.name}")
        print(f"    State: {mo.state}")
        print(f"    Config ID: {mo.config_id.id if mo.config_id else 'None'}")
        print(f"    BOM ID: {mo.bom_id.id if mo.bom_id else 'None'}")
        print(f"    Mesh Operations: {len(mo.mesh_cut_operation_ids)}")
        print(f"    Byproduct Moves: {len(mo.move_byproduct_ids)}")
        
        for bp_move in mo.move_byproduct_ids:
            print(f"      - {bp_move.product_id.name} (qty: {bp_move.product_uom_qty})")
        print()

if __name__ == "__main__":
    # This would be run in Odoo shell
    print("Run this in Odoo shell:")
    print("exec(open('canbrax_configmatrix/test_mesh_byproduct_integration.py').read())")
    print("test_mesh_byproduct_flow()")
