### ON PRODUCT CONFIGURATION UI 

[OperationCosts] Operation 0: {name: 'Receive Material Time', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 1: {name: 'QC & Colour Consitency CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 2: {name: 'Cut CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 3: {name: 'Cut Plugh for CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 4: {name: 'CNC Load / Unload Extrusion & Set Up (Bottom Flush Bolt in Non-Lock Door)', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 5: {name: 'CNC Centre Lock Cut Out', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 6: {name: 'Insert Plugh into CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 7: {name: 'Assemble CommandeX Door with Crimped Corners', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 8: {name: 'Plugh CommandeX Door with Assembly Press', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 9: {name: 'QC CommandeX Door', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 10: {name: 'Cut Mesh or Sheet', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 11: {name: 'Load Door (Delivery) / Customer Collect (Pick Up)', question_number: 171, field_name: 'Number of Security Hinges (Pick Up)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 12: {name: 'Delivery Time - Time allocated to CD1 even for pick up orders', question_number: 171, field_name: 'Number of Security Hinges (Pick Up)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 13: {name: 'CNC Load / Unload Extrusion & Set Up 3', question_number: 160, field_name: 'Non-Lock Door Striker Cut Outs', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 14: {name: 'CNC Non-Lock Door Centre Striker Cut Out', question_number: 160, field_name: 'Non-Lock Door Striker Cut Outs', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 15: {name: 'CNC Load / Unload Extrusion & Set Up 3', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 16: {name: 'CNC Flush Bolt Cut Out', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 17: {name: 'CNC Flush Bolt Cut Out (Bottom Flush Bolt in Non-Lock Door)', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 18: {name: 'Rivet Flush Bolts into Non-Lock Door', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 19: {name: 'CNC Flush Bolt Hole in Top or Btm (Bottom)', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 20: {name: 'CNC Flush Bolt Hole in Top or Btm (Top)', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 21: {name: 'CNC Load / Unload Extrusion & Set Up (Non-Lock Door)', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 22: {name: 'CNC Load / Unload Extrusion & Set Up (Lock Door)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 23: {name: 'CNC Hinge Holes (Lock Door)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 24: {name: 'CNC Load / Unload Extrusion & Set Up (Non-Lock Door)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 25: {name: 'CNC Hinge Holes (Non-Lock Door)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 26: {name: 'Pick Hinges', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 27: {name: 'QC Picked Hardware (x Qty of Hinges)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1756436379:263 [OperationCosts] Operation 28: {name: 'Wrap Hardware (x Qty of Hinges)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
# ### Operations: 238.19974399999998 


### ON LOG FILE 

2025-08-29 03:03:06,599 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: Successfully created mesh panel operations for config 71362 
2025-08-29 03:03:06,609 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Found 59 operations in BOM 71331 
2025-08-29 03:03:06,620 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Receive Material Time' from template: $5.6000000000000005 
2025-08-29 03:03:06,625 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'QC & Colour Consitency CommandeX Door Frame' from template: $5.2937800896 
2025-08-29 03:03:06,628 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Cut CommandeX Door Frame' from template: $20.98666864 
2025-08-29 03:03:06,632 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Cut Plugh for CommandeX Door Frame' from template: $8.2747436352 
2025-08-29 03:03:06,635 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Load / Unload Extrusion & Set Up (Bottom Flush Bolt in Non-Lock Door)' from template: $2.64 
2025-08-29 03:03:06,638 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Centre Lock Cut Out' from template: $2.64 
2025-08-29 03:03:06,641 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Insert Plugh into CommandeX Door Frame' from template: $21.84326736 
2025-08-29 03:03:06,645 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Assemble CommandeX Door with Crimped Corners' from template: $45.38260018560001 
2025-08-29 03:03:06,649 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Plugh CommandeX Door with Assembly Press' from template: $25.2353982912 
2025-08-29 03:03:06,653 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Clamp Product (even when midrail/brace is used) (per Panel)' from template: $4.48 
2025-08-29 03:03:06,655 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Clamp Product (even when midrail/brace is used) (per Panel) (Clamp Width)' from template: $4.48 
2025-08-29 03:03:06,658 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'QC CommandeX Door' from template: $24.378799571200002 
2025-08-29 03:03:06,661 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Cut Mesh or Sheet' from template: $16.3267716032 
2025-08-29 03:03:06,663 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Cut Extrusions on Upcut Saw' from template: $0.0 
2025-08-29 03:03:06,664 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Load / Unload Extrusion & Set Up 2' from template: $0.0 
2025-08-29 03:03:06,666 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Extra Time to Cut French Door Mullion' from template: $0.0 
2025-08-29 03:03:06,667 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from template: $0.0 
2025-08-29 03:03:06,668 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from template: $0.0 
2025-08-29 03:03:06,669 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from template: $0.0 
2025-08-29 03:03:06,670 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Insert Top & Bottom French Door Mullion Caps' from template: $0.0 
2025-08-29 03:03:06,672 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from template: $0.0 
2025-08-29 03:03:06,675 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Pick Hardware' from template: $1.36 
2025-08-29 03:03:06,679 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'QC Picked Hardware (Cylinder)' from template: $0.4 
2025-08-29 03:03:06,682 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Wrap Hardware (Cylinder)' from template: $0.4 
2025-08-29 03:03:06,686 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Install Centre Lock' from template: $6.48 
2025-08-29 03:03:06,689 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Pick Centre Lock Site Kit' from template: $1.36 
2025-08-29 03:03:06,692 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'QC Picked Hardware (Lock Site Kit)' from template: $0.4 
2025-08-29 03:03:06,695 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Wrap Hardware (Lock Site Kit)' from template: $0.4 
2025-08-29 03:03:06,699 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC 3PT / Double Auxiliary Lock Cut Outs' from template: $4.0 
2025-08-29 03:03:06,703 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Install 3PT / Double Auxiliary Locks' from template: $10.0 
2025-08-29 03:03:06,706 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Pick 3PT / Double Auxiliary Lock Site Kit' from template: $1.36 
2025-08-29 03:03:06,708 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'QC Picked Hardware (3PT Site Kit)' from template: $0.4 
2025-08-29 03:03:06,711 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Wrap Hardware (3PT Site Kit)' from template: $0.4 
2025-08-29 03:03:06,715 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Notch Slots for 3PT / Double Auxiliary Locks in French Door Mullion or T-Section Mullion' from template: $1.44 
2025-08-29 03:03:06,720 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Remove Breakaway Sections for 3PT Notches in French Door Mullion or T-Section Mullion' from template: $1.2 
2025-08-29 03:03:06,723 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Load / Unload Extrusion & Set Up 3' from template: $2.64 
2025-08-29 03:03:06,726 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Non-Lock Door Centre Striker Cut Out' from template: $1.36 
2025-08-29 03:03:06,728 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Load / Unload Extrusion & Set Up 3' from template: $2.64 
2025-08-29 03:03:06,732 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Flush Bolt Cut Out' from template: $2.64 
2025-08-29 03:03:06,735 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Flush Bolt Cut Out (Bottom Flush Bolt in Non-Lock Door)' from template: $2.64 
2025-08-29 03:03:06,784 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-08-29 03:03:06,786 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Rivet Flush Bolts into Non-Lock Door' from template: $18.720000000000002 
2025-08-29 03:03:06,789 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Flush Bolt Hole in Top or Btm (Bottom)' from template: $1.36 
2025-08-29 03:03:06,792 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Flush Bolt Hole in Top or Btm (Top)' from template: $1.36 
2025-08-29 03:03:06,795 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Load / Unload Extrusion & Set Up (Non-Lock Door)' from template: $2.64 
2025-08-29 03:03:06,800 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' from template: $11.36 
2025-08-29 03:03:06,804 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Cut Extrusions on Upcut Saw (Midrail)' from template: $4.0 
2025-08-29 03:03:06,807 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Cut Plugh for CommandeX Midrail' from template: $2.72 
2025-08-29 03:03:06,810 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Insert Plugh into CommandeX Midrail' from template: $3.3600000000000003 
2025-08-29 03:03:06,814 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Extra Time to Assemble Product with CommandeX Midrail' from template: $12.64 
2025-08-29 03:03:06,818 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Plugh CommandeX Midrail by Hand' from template: $10.56 
2025-08-29 03:03:06,821 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from template: $3.597714624 
2025-08-29 03:03:06,825 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from template: $5.6000000000000005 
2025-08-29 03:03:06,827 204786 INFO canbrax_dev_11 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Operation 'CNC Load / Unload Extrusion & Set Up (Lock Door)' from template: $2.64 

# ### TOTAL OPERATION COSTS: $321.39974399999994