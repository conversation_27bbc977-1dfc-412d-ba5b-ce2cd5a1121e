<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="800" height="600">
  <!-- Grid background -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>
  
  <!-- Master sheet outline (1670 x 1125) -->
  <rect x="100" y="150" width="334" height="225" fill="white" stroke="black" stroke-width="3"/>
  
  <!-- Product area (1100 x 620) - bottom right -->
  <rect x="167" y="275" width="220" height="124" fill="rgba(255, 165, 0, 0.3)" stroke="orange" stroke-width="2"/>
  
  <!-- 1st Cut line (horizontal at 620mm from top) -->
  <line x1="100" y1="275" x2="434" y2="275" stroke="magenta" stroke-width="3"/>
  <text x="450" y="280" font-size="16" fill="magenta" font-weight="bold">1st Cut</text>
  
  <!-- 2nd Cut line (vertical at 1100mm from left) -->
  <line x1="320" y1="150" x2="320" y2="399" stroke="orange" stroke-width="3"/>
  <text x="325" y="420" font-size="16" fill="orange" font-weight="bold">2nd Cut</text>
  
  <!-- Offcut areas -->
  <!-- Top left offcut area -->
  <rect x="100" y="150" width="220" height="124" fill="rgba(156, 39, 176, 0.2)" stroke="purple" stroke-width="1"/>
  <text x="150" y="200" font-size="14" fill="purple">1/2</text>
  <text x="150" y="220" font-size="14" fill="purple">of</text>
  <text x="150" y="240" font-size="14" fill="purple">O/C</text>
  
  <!-- Top right offcut area -->
  <rect x="320" y="150" width="114" height="124" fill="rgba(156, 39, 176, 0.2)" stroke="purple" stroke-width="1"/>
  <text x="350" y="200" font-size="14" fill="purple">1/2</text>
  <text x="350" y="220" font-size="14" fill="purple">of</text>
  <text x="350" y="240" font-size="14" fill="purple">O/C</text>
  
  <!-- Bottom left offcut area -->
  <rect x="100" y="275" width="220" height="124" fill="rgba(156, 39, 176, 0.2)" stroke="purple" stroke-width="1"/>
  
  <!-- Offcut circles and labels -->
  <ellipse cx="210" cy="212" rx="40" ry="25" fill="none" stroke="purple" stroke-width="2"/>
  <text x="190" y="210" font-size="12" fill="black" font-weight="bold">6 x 12</text>
  <text x="185" y="225" font-size="12" fill="black" font-weight="bold">Plan O/C</text>
  
  <ellipse cx="377" cy="212" rx="40" ry="25" fill="none" stroke="purple" stroke-width="2"/>
  <text x="357" y="210" font-size="12" fill="black" font-weight="bold">6 x 12</text>
  <text x="352" y="225" font-size="12" fill="black" font-weight="bold">Plan O/C</text>
  
  <!-- Dimension labels -->
  <!-- Width dimension (1670) -->
  <text x="50" y="270" font-size="24" fill="black" font-weight="bold">1670</text>
  
  <!-- Height dimension (1125) -->
  <text x="250" y="440" font-size="24" fill="black" font-weight="bold">1125</text>
  
  <!-- Product dimensions -->
  <text x="250" y="350" font-size="20" fill="orange" font-weight="bold">1100</text>
  <text x="120" y="320" font-size="20" fill="magenta" font-weight="bold">620</text>
  
  <!-- Header text -->
  <rect x="200" y="50" width="300" height="60" fill="rgba(156, 39, 176, 0.3)" stroke="red" stroke-width="2"/>
  <text x="220" y="75" font-size="14" fill="red" font-weight="bold">** SPLIT OFFCUT IN 1/2 TO CREATE</text>
  <text x="240" y="95" font-size="14" fill="red" font-weight="bold">2 - 6x12 PLANNED OFFCUTS **</text>
  
  <!-- Cross symbol -->
  <g transform="translate(280, 320)">
    <line x1="-8" y1="0" x2="8" y2="0" stroke="black" stroke-width="3"/>
    <line x1="0" y1="-8" x2="0" y2="8" stroke="black" stroke-width="3"/>
  </g>
  
  <!-- Hatched area (waste) -->
  <defs>
    <pattern id="hatch" patternUnits="userSpaceOnUse" width="4" height="4">
      <path d="M-1,1 l2,-2 M0,4 l4,-4 M3,5 l2,-2" stroke="black" stroke-width="1"/>
    </pattern>
  </defs>
  <rect x="387" y="275" width="47" height="124" fill="url(#hatch)" stroke="black" stroke-width="1"/>
</svg>
