/**
 * SVG Renderer Fix
 *
 * This script fixes issues with SVG conditional layers not loading when related questions are answered.
 * It enhances the existing SVG rendering functionality by:
 * 1. Improving field value tracking
 * 2. Ensuring SVG components are properly updated when field values change
 * 3. Adding better error handling and debugging
 */

document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let svgComponents = [];
    let fieldValues = {};
    let templateId = null;
    let isLoading = false;
    let updateTimeout = null;
    let svgContainer = null;

    // Field name mapping - maps condition field names to technical names
    const fieldNameMapping = {
        // Add mappings here as needed
        'sand_slide_top_track_type': 'top_track_type',
        'sand_slide_frame_colour': 'frame_colour',
        'location': 'location'
    };

    // Initialize the fix
    function initialize() {
        // Get the SVG container
        svgContainer = document.getElementById('svg-container');
        if (!svgContainer) {
            console.error('[SVG-FIX] SVG container not found');
            return;
        }

        // Get the template ID
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
            console.error('[SVG-FIX] Template ID input not found');
            return;
        }
        templateId = parseInt(templateIdInput.value) || 0;

        // Load SVG components
        loadSvgComponents();

        // Add event listeners to all configuration fields
        addFieldEventListeners();

        // Set up mutation observer to watch for dynamic field changes
        setupMutationObserver();
    }

    // Load SVG components from the server with performance optimization
    function loadSvgComponents() {
        // Prevent multiple simultaneous loads
        if (isLoading) {
            return;
        }
        isLoading = true;

        // Show loading indicator
        if (svgContainer) {
            svgContainer.innerHTML = '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading visualization...</div>';
        }

        // Collect current field values first
        collectFieldValues();

        // Fetch SVG components with current field values
        fetch('/config_matrix/get_svg_components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: templateId,
                    config_values: fieldValues,
                    include_field_layers: true
                },
                id: new Date().getTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.result && data.result.success && data.result.components) {
                svgComponents = data.result.components;

                // Collect current field values
                collectFieldValues();

                // Render SVG with current field values
                renderSvg().catch(error => console.error('[SVG-FIX] Error in initial render:', error));
            } else {
                console.error('[SVG-FIX] Error loading SVG components:', data);
                if (svgContainer) {
                    svgContainer.innerHTML = '<div class="alert alert-danger">Error loading SVG components</div>';
                }
            }
        })
        .catch(error => {
            console.error('[SVG-FIX] Error fetching SVG components:', error);
            if (svgContainer) {
                svgContainer.innerHTML = '<div class="alert alert-danger">Error loading SVG components</div>';
            }
        })
        .finally(() => {
            isLoading = false;
        });
    }

    // Add event listeners to all configuration fields
    function addFieldEventListeners() {
        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');

        // Add event listeners to each field
        configFields.forEach(field => {
            // Get field information
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!fieldId || !technicalName) {
                console.warn('[SVG-FIX] Field missing ID or technical name:', field);
                return;
            }

            // console.log('[SVG-FIX] Adding event listener to field:', technicalName, 'type:', fieldType);

            // Add change event listener with debouncing
            field.addEventListener('change', function() {
                handleFieldChange(field, technicalName, fieldType);
            });

            // For text and number fields, also listen for input events for real-time updates
            if (fieldType === 'text' || fieldType === 'number') {
                field.addEventListener('input', function () {
                    handleFieldChange(field, technicalName, fieldType);
                });
            }

            // For boolean fields, also listen for click events
            if (fieldType === 'boolean') {
                field.addEventListener('click', function () {
                    handleFieldChange(field, technicalName, fieldType);
                });
            }
        });
    }

    // Handle field change events with proper debouncing and value tracking
    function handleFieldChange(field, technicalName, fieldType) {
        // Clear any existing timeout
        if (updateTimeout) {
            clearTimeout(updateTimeout);
        }

        // Get the field value
        let value = getFieldValue(field, fieldType);

        // Update the field value in the global object
        fieldValues[technicalName] = value;

        console.log(`[SVG-FIX] Field ${technicalName} changed to:`, value);

        // Debounce the SVG update to prevent excessive renders
        updateTimeout = setTimeout(() => {
            renderSvg().catch(error => console.error('[SVG-FIX] Error in field change render:', error));
        }, 300); // 300ms delay
    }

    // Set up mutation observer to watch for dynamic field changes
    function setupMutationObserver() {
        // Watch for changes in field values that happen programmatically
        const observer = new MutationObserver(function (mutations) {
            let shouldUpdate = false;

            mutations.forEach(function (mutation) {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'value' || mutation.attributeName === 'checked')) {
                    const field = mutation.target;
                    if (field.classList.contains('config-field')) {
                        const technicalName = field.getAttribute('data-technical-name');
                        if (technicalName) {
                            const fieldType = field.getAttribute('data-field-type');
                            const newValue = getFieldValue(field, fieldType);

                            // Check if value actually changed
                            if (fieldValues[technicalName] !== newValue) {
                                fieldValues[technicalName] = newValue;
                                console.log(`[SVG-FIX] Dynamic field change detected: ${technicalName} = ${newValue}`);
                                shouldUpdate = true;
                            }
                        }
                    }
                }
            });

            if (shouldUpdate) {
                // Debounce the update
                if (updateTimeout) {
                    clearTimeout(updateTimeout);
                }
                updateTimeout = setTimeout(() => {
                    renderSvg().catch(error => console.error('[SVG-FIX] Error in mutation observer render:', error));
                }, 300);
            }
        });

        // Start observing all config fields for attribute changes
        const configFields = document.querySelectorAll('.config-field');
        configFields.forEach(field => {
            observer.observe(field, {
                attributes: true,
                attributeFilter: ['value', 'checked']
            });
        });

        // Also set up periodic checking for field value changes
        setInterval(checkForFieldValueChanges, 1000); // Check every second
    }

    // Check for field value changes that might not trigger events
    function checkForFieldValueChanges() {
        const configFields = document.querySelectorAll('.config-field');
        let hasChanges = false;

        configFields.forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!technicalName) return;

            const currentValue = getFieldValue(field, fieldType);
            const storedValue = fieldValues[technicalName];

            if (currentValue !== storedValue) {
                fieldValues[technicalName] = currentValue;
                console.log(`[SVG-FIX] Periodic check detected change: ${technicalName} = ${currentValue}`);
                hasChanges = true;
            }
        });

        if (hasChanges) {
            // Debounce the update
            if (updateTimeout) {
                clearTimeout(updateTimeout);
            }
            updateTimeout = setTimeout(() => {
                renderSvg().catch(error => console.error('[SVG-FIX] Error in periodic check render:', error));
            }, 300);
        }
    }

    // Get the value of a field based on its type
    function getFieldValue(field, fieldType) {
        switch (fieldType) {
            case 'boolean':
                return field.checked;
            case 'number':
                return parseFloat(field.value) || 0;
            case 'selection':
                // Check if this is an unselected option
                if (!field.value || field.value === '-- Select an option --' || field.selectedIndex === 0) {
                    return '';
                }
                // Get the actual option value from data-option-value attribute
                const selectedOption = field.options[field.selectedIndex];
                return selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : field.value;
            case 'date':
                return field.value; // Date in YYYY-MM-DD format
            case 'text':
                return field.value;
            default:
                return field.value;
        }
    }

    // Collect current values of all fields
    function collectFieldValues() {
        console.log('[SVG-FIX] 🔧 Collecting field values...');

        // Clear existing values
        fieldValues = {};

        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');
        console.log('[SVG-FIX] Found', configFields.length, 'config fields');

        // Collect values from each field
        configFields.forEach(field => {
            // Get field information
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');
            const fieldId = field.getAttribute('data-field-id');

            if (!technicalName) {
                return;
            }

            // Get the field value
            const value = getFieldValue(field, fieldType);

            // Store the value - ensure empty selections are stored as empty strings
            if (fieldType === 'selection' && (value === null || value === undefined || value === '-- Select an option --')) {
                fieldValues[technicalName] = '';
            } else {
                fieldValues[technicalName] = value;
            }
        });

        // Add mapped field values for SVG conditions
        for (const [conditionName, technicalName] of Object.entries(fieldNameMapping)) {
            if (fieldValues[technicalName] !== undefined) {
                fieldValues[conditionName] = fieldValues[technicalName];
            }
        }

        // Add special values for common condition values
        fieldValues['no'] = 'no';
        fieldValues['yes'] = 'yes';
        fieldValues['bbq'] = 'bbq';
        fieldValues['black_custom_matt_gn248a'] = 'black_custom_matt_gn248a';

        // Add calculated field mappings for SVG conditions
        // Map _CALCULATED_door_width to door_width for SVG conditions
        if (fieldValues['_CALCULATED_door_width'] !== undefined) {
            fieldValues['door_width'] = fieldValues['_CALCULATED_door_width'];
        }

        // Make field values available globally
        window.fieldValues = fieldValues;
    }

    // Get calculated fields from backend
    async function getCalculatedFieldsFromBackend() {
        try {
            if (!templateId) {
                console.warn('[SVG-FIX] No template ID for calculated fields');
                return {};
            }

            // console.log('[SVG-FIX] Fetching calculated fields for template:', templateId);
            const response = await fetch('/config_matrix/calculate_field_values', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        template_id: templateId,
                        field_values: fieldValues
                    }
                })
            });

            const result = await response.json();
            let calculatedFields = {};
            if (result.result && result.result.result) {
                calculatedFields = result.result.result;
            } else if (result.result) {
                calculatedFields = result.result;
            } else {
                console.error('[SVG-FIX] Error from backend:', result.error);
                return {};
            }

            // Store backend results globally to avoid frontend re-evaluation
            window.backendCalculatedFields = calculatedFields;

            return calculatedFields;
        } catch (error) {
            console.error('[SVG-FIX] Failed to fetch calculated fields:', error);
            return {};
        }
    }

    // Render the SVG with current field values
    async function renderSvg() {
        console.log('[SVG-FIX] 🚀 renderSvg() called');
        console.log('[SVG-FIX] Current fieldValues before render:', fieldValues);

        if (!svgContainer) {
            console.error('[SVG-FIX] SVG container not found');
            return;
        }

        // Reload SVG components with current field values to get field/option layers
        console.log('[SVG-FIX] Reloading SVG components with current field values...');
        try {
            const response = await fetch('/config_matrix/get_svg_components', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    jsonrpc: "2.0",
                    method: "call",
                    params: {
                        template_id: templateId,
                        config_values: fieldValues,
                        include_field_layers: true
                    },
                    id: new Date().getTime()
                })
            });

            const data = await response.json();
            if (data.result && data.result.success && data.result.components) {
                svgComponents = data.result.components;
                console.log('[SVG-FIX] Reloaded', svgComponents.length, 'SVG components');
            }
        } catch (error) {
            console.error('[SVG-FIX] Error reloading SVG components:', error);
        }

        // Fetch calculated fields from backend and merge with field values
        try {
            console.log('[SVG-FIX] About to fetch calculated fields...');
            const calculatedFields = await getCalculatedFieldsFromBackend();
            console.log('[SVG-FIX] Raw calculated fields response:', calculatedFields);
            Object.assign(fieldValues, calculatedFields);
            console.log('[SVG-FIX] Merged calculated fields:', Object.keys(calculatedFields));
            console.log('[SVG-FIX] Final fieldValues after merge:', fieldValues);
        } catch (error) {
            console.error('[SVG-FIX] Failed to get calculated fields:', error);
        }

        if (svgComponents.length === 0) {
            console.warn('[SVG-FIX] No SVG components available');
            svgContainer.innerHTML = '<div class="alert alert-warning">No SVG components available</div>';
            return;
        }

        // Find base component
        const baseComponent = svgComponents.find(comp => comp.component_type === 'base');
        if (!baseComponent) {
            console.error('[SVG-FIX] No base SVG component found');
            svgContainer.innerHTML = '<div class="alert alert-warning">No base SVG component found</div>';
            return;
        }

        // Start with base SVG content
        let svgContent = processTemplate(baseComponent.svg_content);

        // Add other layers based on conditions
        let addedLayers = 0;
        svgComponents.forEach(component => {
            if (component.component_type !== 'layer') {
                return;
            }

            // Evaluate condition if present
            if (component.condition) {
                const conditionResult = evaluateCondition(component.condition);
                if (!conditionResult) {
                    return;
                }
            }

            // Process template with current config values
            let layerSvg = processTemplate(component.svg_content);

            // Check if the layer content is a complete SVG or just a fragment
            if (layerSvg.trim().startsWith('<svg')) {
                // Extract the content inside the SVG tags
                const svgMatch = layerSvg.match(/<svg[^>]*>([\s\S]*)<\/svg>/i);
                if (svgMatch && svgMatch[1]) {
                    layerSvg = svgMatch[1];
                }
            }

            // Add to SVG content - before the closing </svg> tag
            svgContent = svgContent.replace('</svg>', layerSvg + '</svg>');
            addedLayers++;
        });

        // Update the SVG display - use a safe method to insert SVG
        try {
            // Create a temporary div to parse the SVG
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = svgContent;

            // Clear the container
            svgContainer.innerHTML = '';

            // Append the SVG element
            if (tempDiv.firstChild) {
                svgContainer.appendChild(tempDiv.firstChild);
            } else {
                console.error('[SVG-FIX] Failed to parse SVG content');
                svgContainer.innerHTML = '<div class="alert alert-warning">Failed to parse SVG content</div>';
            }
        } catch (e) {
            console.error('[SVG-FIX] Error rendering SVG:', e);
            svgContainer.innerHTML = '<div class="alert alert-warning">Error rendering SVG</div>';
        }
    }

    // Process template with field values
    function processTemplate(template) {
        if (!template) {
            return '';
        }

        // Replace ${fieldName} with actual values
        return template.replace(/\${(\w+)}/g, function(match, fieldName) {
            const value = fieldValues[fieldName];
            if (value !== undefined && value !== null && value !== '') {
                return String(value);
            } else {
                // For missing variables, try common alternatives
                if (fieldName === 'lock_x_position' && fieldValues['lock_x'] !== undefined) {
                    return String(fieldValues['lock_x']);
                }
                if (fieldName === 'lock_y_position' && fieldValues['lock_y'] !== undefined) {
                    return String(fieldValues['lock_y']);
                }
                // Return 0 for position variables to prevent SVG errors
                if (fieldName.includes('_x') || fieldName.includes('_y') || fieldName.includes('position')) {
                    return '0';
                }
                // Return empty string for other missing variables
                return '';
            }
        });
    }

    // Evaluate a condition with field values
    function evaluateCondition(condition) {
        if (!condition || condition === 'true') {
            return true;
        }

        try {
            // Handle equality conditions (e.g., "field == value")
            const equalityMatch = condition.match(/^\s*(\w+)\s*(==|===|!=|!==|>=|<=|>|<)\s*['"]?([^'"]*?)['"]?\s*$/);
            if (equalityMatch) {
                const [_, fieldName, operator, value] = equalityMatch;

                // Check if we need to use a mapped field name
                let actualFieldName = fieldName;
                for (const [conditionName, technicalName] of Object.entries(fieldNameMapping)) {
                    if (fieldName === conditionName && fieldValues[technicalName] !== undefined) {
                        actualFieldName = technicalName;
                        break;
                    }
                }

                const fieldValue = fieldValues[fieldName];

                // Special case for option values - check if the field value equals the option value
                // For example, if condition is "top_track_type == no", we need to check if the field value is "no"

                // If the field is empty/undefined/null and we're checking for equality with a value,
                // the condition should be false (component hidden)
                if ((fieldValue === undefined || fieldValue === null || fieldValue === '') &&
                    (operator === '==' || operator === '===')) {
                    return false;
                }

                // If the field is empty/undefined/null and we're checking for inequality with a value,
                // the condition should be true (component visible)
                if ((fieldValue === undefined || fieldValue === null || fieldValue === '') &&
                    (operator === '!=' || operator === '!==')) {
                    return true;
                }

                switch (operator) {
                    case '==':
                    case '===':
                        return String(fieldValue) === String(value);
                    case '!=':
                    case '!==':
                        return String(fieldValue) !== String(value);
                    case '>':
                        return Number(fieldValue) > Number(value);
                    case '<':
                        return Number(fieldValue) < Number(value);
                    case '>=':
                        return Number(fieldValue) >= Number(value);
                    case '<=':
                        return Number(fieldValue) <= Number(value);
                }
            }

            // Extract field names from the condition
            const fieldNames = condition.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

            // Create a context with field values
            const context = { ...fieldValues };

            // Add missing fields with default values
            fieldNames.forEach(fieldName => {
                if (context[fieldName] === undefined) {
                    context[fieldName] = '';
                }
            });

            // Create a function with the field values in scope
            const func = new Function(...Object.keys(context), `return ${condition};`);

            // Call the function with the field values
            return func(...Object.values(context));
        } catch (e) {
            // Only log errors for debugging when needed
            // console.error('[SVG-FIX] Error evaluating condition:', e, 'with values:', fieldValues);
            return false;
        }
    }

    // Initialize after a short delay to ensure DOM is ready
    setTimeout(initialize, 1000);

    // Re-scan for new fields that might be added dynamically
    function rescanFields() {
        console.log('[SVG-FIX] Re-scanning for new fields...');
        addFieldEventListeners();
        setupMutationObserver();
    }

    // Force update SVG with current field values
    function forceUpdateSvg() {
        console.log('[SVG-FIX] Force updating SVG...');
        collectFieldValues();
        renderSvg().catch(error => console.error('[SVG-FIX] Error in force update:', error));
    }

    // Make functions available globally
    window.svgRendererFix = {
        initialize,
        loadSvgComponents,
        renderSvg,
        evaluateCondition,
        fieldValues,
        rescanFields,
        forceUpdateSvg,
        handleFieldChange,
        getFieldValue
    };
});
