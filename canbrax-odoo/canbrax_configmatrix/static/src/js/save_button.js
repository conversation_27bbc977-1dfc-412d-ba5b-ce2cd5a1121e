/**
 * Simple Save Fix
 *
 * This script makes three simple changes:
 * 1. Removes the validation popup when saving
 * 2. Makes the floating save button always visible
 * 3. Ensures ALL fields (green and yellow) are saved without filtering
 */

// Wait for document ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('[Simple Save Fix] Script loaded');
    console.log('[Simple Save Fix] TESTING - Looking for total price element...');

    // Test if we can find the element immediately
    setTimeout(function() {
        const testElement = document.getElementById('configuration-total-price');
        console.log('[Simple Save Fix] TESTING - Total price element found:', testElement);
        if (testElement) {
            console.log('[Simple Save Fix] TESTING - Total price element text:', testElement.textContent);
        }
    }, 2000);

    // Wait a bit to ensure the original scripts have run
    setTimeout(function() {
        applySimpleSaveFix();
    }, 500);

    function applySimpleSaveFix() {
        console.log('[Simple Save Fix] Applying fixes...');

        // 1. Override the validateNumberFields function to always return true
        if (window.validateNumberFields) {
            console.log('[Simple Save Fix] Overriding validateNumberFields function');
            window.validateNumberFields = function() {
                console.log('[Simple Save Fix] validateNumberFields called - always returning true');
                return true;
            };
        }

        // 2. Make the floating save button always visible and add proper click handler
        const floatingSaveBtn = document.getElementById('floating-save-btn');
        if (floatingSaveBtn) {
            console.log('[Simple Save Fix] Making floating save button always visible');

            // Make the button always visible
            floatingSaveBtn.style.opacity = '1';
            floatingSaveBtn.style.pointerEvents = 'auto';

            // Position it in the bottom right corner
            floatingSaveBtn.style.position = 'fixed';
            floatingSaveBtn.style.bottom = '20px';
            floatingSaveBtn.style.right = '20px';
            floatingSaveBtn.style.zIndex = '1000';

            // Override any scroll event handlers
            window.addEventListener('scroll', function() {
                floatingSaveBtn.style.opacity = '1';
                floatingSaveBtn.style.pointerEvents = 'auto';
            }, true);

            // Add click handler for floating save button
            floatingSaveBtn.addEventListener('click', function (event) {
                console.log('[Simple Save Fix] Floating save button clicked - preparing form submission');

                // Prevent default form submission
                event.preventDefault();

                // Make sure all hidden fields are included
                document.querySelectorAll('.conditional-field-pending').forEach(function (row) {
                    // Make sure the row is visible for form submission
                    row.style.display = '';

                    // Make sure any inputs in this row are enabled
                    row.querySelectorAll('input, select, textarea').forEach(function (input) {
                        input.disabled = false;
                    });
                });

                // Generate config data using the globally accessible function
                if (window.generateConfigData && typeof window.generateConfigData === 'function') {
                    try {
                        const configData = window.generateConfigData();
                        const configDataField = document.getElementById('config-data-field');
                        if (configDataField) {
                            configDataField.value = configData;
                            console.log('[Simple Save Fix] Config data generated and set for floating button');
                        } else {
                            console.warn('[Simple Save Fix] Config data field not found');
                        }
                    } catch (error) {
                        console.error('[Simple Save Fix] Error generating config data for floating button:', error);
                    }
                } else {
                    console.warn('[Simple Save Fix] generateConfigData function not available for floating button');
                }

                // GRAB THE FUCKING AGGREGATED TOTAL AND SEND IT
                const configForm = document.getElementById('config-form');
                console.log('[Simple Save Fix] FLOATING - Config form found:', !!configForm);

                // Try multiple elements to find the total
                let totalPrice = null;
                let sourceElement = null;

                // Try the aggregated total first
                const grandTotalElement = document.getElementById('configuration-total-price');
                console.log('[Simple Save Fix] FLOATING - Grand total element found:', !!grandTotalElement);
                if (grandTotalElement) {
                    totalPrice = grandTotalElement.textContent.replace('$', '').replace(',', '').trim();
                    sourceElement = 'configuration-total-price';
                    console.log('[Simple Save Fix] FLOATING - Grand total text:', grandTotalElement.textContent);
                }

                // Fallback to operation costs if grand total is 0 or not found
                if (!totalPrice || parseFloat(totalPrice) === 0) {
                    const operationCostsElement = document.getElementById('configuration-operation-costs');
                    const componentPriceElement = document.getElementById('configuration-price');
                    console.log('[Simple Save Fix] FLOATING - Operation costs element found:', !!operationCostsElement);
                    console.log('[Simple Save Fix] FLOATING - Component price element found:', !!componentPriceElement);

                    if (operationCostsElement && componentPriceElement) {
                        const operationCost = parseFloat(operationCostsElement.textContent.replace('$', '').replace(',', '').trim()) || 0;
                        const componentCost = parseFloat(componentPriceElement.textContent.replace('$', '').replace(',', '').trim()) || 0;
                        totalPrice = (operationCost + componentCost).toString();
                        sourceElement = 'calculated from operations + components';
                        console.log('[Simple Save Fix] FLOATING - Calculated total:', totalPrice, 'from operations:', operationCost, 'components:', componentCost);
                    }
                }

                if (totalPrice && configForm && parseFloat(totalPrice) > 0) {
                    console.log('[Simple Save Fix] FLOATING - FOUND TOTAL:', totalPrice, 'from:', sourceElement);

                    // Add hidden field with the total price
                    let totalPriceField = document.getElementById('total-price-field');
                    if (!totalPriceField) {
                        totalPriceField = document.createElement('input');
                        totalPriceField.type = 'hidden';
                        totalPriceField.id = 'total-price-field';
                        totalPriceField.name = 'total_price';
                        configForm.appendChild(totalPriceField);
                    }
                    totalPriceField.value = totalPrice;
                    console.log('[Simple Save Fix] FLOATING - TOTAL PRICE FIELD SET TO:', totalPrice);
                } else {
                    console.error('[Simple Save Fix] FLOATING - COULD NOT FIND VALID TOTAL. totalPrice:', totalPrice, 'configForm:', !!configForm);
                }

                // Now submit the form programmatically
                if (configForm) {
                    console.log('[Simple Save Fix] Submitting form via floating button');
                    configForm.submit();
                } else {
                    console.error('[Simple Save Fix] Config form not found');
                }
            });
        }

        // 3. CRITICAL: Override any functions that might filter out fields
        // Note: Removed clearAllIssues override to allow auto-clearing of hidden fields
        // The auto-clear functionality needs these functions to work properly

        // 4. Override form submission to ensure all fields are included and generate config data
        const configForm = document.getElementById('config-form');
        if (configForm) {
            console.log('[Simple Save Fix] Overriding form submission handler');

            configForm.addEventListener('submit', function(event) {
                console.log('[Simple Save Fix] Form submission - ensuring all fields are included');

                // Make sure all hidden fields are included
                document.querySelectorAll('.conditional-field-pending').forEach(function(row) {
                    // Make sure the row is visible for form submission
                    row.style.display = '';

                    // Make sure any inputs in this row are enabled
                    row.querySelectorAll('input, select, textarea').forEach(function(input) {
                        input.disabled = false;
                    });
                });

                // Generate config data using the globally accessible function
                if (window.generateConfigData && typeof window.generateConfigData === 'function') {
                    try {
                        const configData = window.generateConfigData();
                        const configDataField = document.getElementById('config-data-field');
                        if (configDataField) {
                            configDataField.value = configData;
                            console.log('[Simple Save Fix] Config data generated and set');
                        } else {
                            console.warn('[Simple Save Fix] Config data field not found');
                        }
                    } catch (error) {
                        console.error('[Simple Save Fix] Error generating config data:', error);
                    }
                } else {
                    console.warn('[Simple Save Fix] generateConfigData function not available');
                }

                // GRAB THE FUCKING AGGREGATED TOTAL AND SEND IT (REGULAR FORM SUBMISSION)
                console.log('[Simple Save Fix] REGULAR - Config form found:', !!configForm);

                // Try multiple elements to find the total
                let totalPrice = null;
                let sourceElement = null;

                // Try the aggregated total first
                const grandTotalElement = document.getElementById('configuration-total-price');
                console.log('[Simple Save Fix] REGULAR - Grand total element found:', !!grandTotalElement);
                if (grandTotalElement) {
                    totalPrice = grandTotalElement.textContent.replace('$', '').replace(',', '').trim();
                    sourceElement = 'configuration-total-price';
                    console.log('[Simple Save Fix] REGULAR - Grand total text:', grandTotalElement.textContent);
                }

                // Fallback to operation costs if grand total is 0 or not found
                if (!totalPrice || parseFloat(totalPrice) === 0) {
                    const operationCostsElement = document.getElementById('configuration-operation-costs');
                    const componentPriceElement = document.getElementById('configuration-price');
                    console.log('[Simple Save Fix] REGULAR - Operation costs element found:', !!operationCostsElement);
                    console.log('[Simple Save Fix] REGULAR - Component price element found:', !!componentPriceElement);

                    if (operationCostsElement && componentPriceElement) {
                        const operationCost = parseFloat(operationCostsElement.textContent.replace('$', '').replace(',', '').trim()) || 0;
                        const componentCost = parseFloat(componentPriceElement.textContent.replace('$', '').replace(',', '').trim()) || 0;
                        totalPrice = (operationCost + componentCost).toString();
                        sourceElement = 'calculated from operations + components';
                        console.log('[Simple Save Fix] REGULAR - Calculated total:', totalPrice, 'from operations:', operationCost, 'components:', componentCost);
                    }
                }

                if (totalPrice && configForm && parseFloat(totalPrice) > 0) {
                    console.log('[Simple Save Fix] REGULAR - FOUND TOTAL:', totalPrice, 'from:', sourceElement);

                    // Add hidden field with the total price
                    let totalPriceField = document.getElementById('total-price-field');
                    if (!totalPriceField) {
                        totalPriceField = document.createElement('input');
                        totalPriceField.type = 'hidden';
                        totalPriceField.id = 'total-price-field';
                        totalPriceField.name = 'total_price';
                        configForm.appendChild(totalPriceField);
                    }
                    totalPriceField.value = totalPrice;
                    console.log('[Simple Save Fix] REGULAR - TOTAL PRICE FIELD SET TO:', totalPrice);
                } else {
                    console.error('[Simple Save Fix] REGULAR - COULD NOT FIND VALID TOTAL. totalPrice:', totalPrice, 'configForm:', !!configForm);
                }

                // Let the form submit normally
                return true;
            }, true);
        }

        console.log('[Simple Save Fix] Fixes applied successfully');
    }
});
