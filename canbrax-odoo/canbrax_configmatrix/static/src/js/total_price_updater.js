/**
 * Total Price Updater for Product Configurator
 * Handles updating the total price field by aggregating component, operation, and matrix prices
 */

class TotalPriceUpdater {
    constructor() {
        this.initialized = false;
        this.init();
    }

    init() {
        if (this.initialized) return;
        
        console.log('[TOTAL PRICE UPDATER] Initializing...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
        
        this.initialized = true;
    }

    setup() {
        console.log('[TOTAL PRICE UPDATER] Setting up...');
        
        // Update on page load with delays to ensure all components are loaded
        setTimeout(() => this.updateTotalPriceField(), 1000);
        setTimeout(() => this.updateTotalPriceField(), 3000);
        setTimeout(() => this.updateTotalPriceField(), 5000);
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Hook into operation costs handler if available
        this.hookIntoOperationCostsHandler();
        
        console.log('[TOTAL PRICE UPDATER] Setup complete');
    }

    setupEventListeners() {
        // Update before form submission
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'config-form') {
                console.log('[TOTAL PRICE UPDATER] Form submitting, updating total price...');
                this.updateTotalPriceField();
            }
        });

        // Listen for custom events that might indicate price changes
        document.addEventListener('priceUpdated', () => {
            console.log('[TOTAL PRICE UPDATER] Price update event received');
            setTimeout(() => this.updateTotalPriceField(), 100);
        });

        // Listen for field changes that might affect pricing
        document.addEventListener('fieldValueChanged', () => {
            console.log('[TOTAL PRICE UPDATER] Field value change event received');
            setTimeout(() => this.updateTotalPriceField(), 200);
        });
    }

    hookIntoOperationCostsHandler() {
        // Wait for operation costs handler to be available
        const checkHandler = () => {
            if (window.operationCostsHandler && typeof window.operationCostsHandler.updateDisplay === 'function') {
                console.log('[TOTAL PRICE UPDATER] Hooking into operation costs handler');
                
                // Hook into the updateDisplay method
                const originalUpdateDisplay = window.operationCostsHandler.updateDisplay;
                window.operationCostsHandler.updateDisplay = function() {
                    try {
                        // Call original method
                        originalUpdateDisplay.call(this);
                        
                        // Update total price after a short delay
                        setTimeout(() => {
                            if (window.totalPriceUpdater) {
                                window.totalPriceUpdater.updateTotalPriceField();
                            }
                        }, 100);
                    } catch (error) {
                        console.error('[TOTAL PRICE UPDATER] Error in hooked updateDisplay:', error);
                    }
                };

                // Hook into the updateTotalAggregatedPrice method
                if (window.operationCostsHandler.updateTotalAggregatedPrice) {
                    const originalUpdateTotalAggregatedPrice = window.operationCostsHandler.updateTotalAggregatedPrice;
                    window.operationCostsHandler.updateTotalAggregatedPrice = function() {
                        try {
                            // Call original method
                            originalUpdateTotalAggregatedPrice.call(this);
                            
                            // Update total price after a short delay
                            setTimeout(() => {
                                if (window.totalPriceUpdater) {
                                    window.totalPriceUpdater.updateTotalPriceField();
                                }
                            }, 100);
                        } catch (error) {
                            console.error('[TOTAL PRICE UPDATER] Error in hooked updateTotalAggregatedPrice:', error);
                        }
                    };
                }
            } else {
                // Retry after a short delay
                setTimeout(checkHandler, 500);
            }
        };
        
        checkHandler();
    }

    updateTotalPriceField() {
        console.log('[TOTAL PRICE UPDATER] updateTotalPriceField called');

        const totalPriceField = document.getElementById('total-price-field');
        const componentPriceField = document.getElementById('field-price');
        const matrixPriceField = document.getElementById('field-price-matrix');

        if (!totalPriceField) {
            console.error('[TOTAL PRICE UPDATER] total-price-field not found');
            return;
        }

        // Get individual price components
        const componentElement = document.getElementById('configuration-price');
        const operationElement = document.getElementById('configuration-operation-costs');
        const matrixElement = document.getElementById('configuration-price-matrix');

        const componentPrice = componentElement ? (parseFloat(componentElement.textContent.replace('$', '').replace(',', '').trim()) || 0) : 0;
        const operationPrice = operationElement ? (parseFloat(operationElement.textContent.replace('$', '').replace(',', '').trim()) || 0) : 0;
        const matrixPrice = matrixElement ? (parseFloat(matrixElement.textContent.replace('$', '').replace(',', '').trim()) || 0) : 0;

        // Calculate total
        const totalPrice = componentPrice + operationPrice + matrixPrice;

        // Update all hidden fields
        totalPriceField.value = totalPrice.toFixed(2);

        if (componentPriceField) {
            componentPriceField.value = componentPrice.toFixed(2);
            console.log('[TOTAL PRICE UPDATER] Set component price field to:', componentPrice.toFixed(2));
        }

        if (matrixPriceField) {
            matrixPriceField.value = matrixPrice.toFixed(2);
            console.log('[TOTAL PRICE UPDATER] Set matrix price field to:', matrixPrice.toFixed(2));
        }

        console.log('[TOTAL PRICE UPDATER] Updated fields - Components:', componentPrice.toFixed(2), 'Operations:', operationPrice.toFixed(2), 'Matrix:', matrixPrice.toFixed(2), 'Total:', totalPrice.toFixed(2));

        // Dispatch custom event for other components to listen to
        const event = new CustomEvent('totalPriceUpdated', {
            detail: {
                componentPrice: componentPrice,
                operationPrice: operationPrice,
                matrixPrice: matrixPrice,
                totalPrice: totalPrice
            }
        });
        document.dispatchEvent(event);
    }

    // Public method for manual updates
    forceUpdate() {
        console.log('[TOTAL PRICE UPDATER] Force update requested');
        this.updateTotalPriceField();
    }

    // Method to get current total price
    getCurrentTotalPrice() {
        const totalPriceField = document.getElementById('total-price-field');
        if (totalPriceField) {
            return parseFloat(totalPriceField.value) || 0;
        }
        return 0;
    }

    // Method to check if all price components are available
    arePriceComponentsReady() {
        const componentElement = document.getElementById('configuration-price');
        const operationElement = document.getElementById('configuration-operation-costs');
        const matrixElement = document.getElementById('configuration-price-matrix');
        
        return !!(componentElement && operationElement && matrixElement);
    }
}

// Create global instance when script loads
window.totalPriceUpdater = new TotalPriceUpdater();

// Global functions for other scripts to use
window.updateTotalPrice = () => {
    if (window.totalPriceUpdater) {
        window.totalPriceUpdater.updateTotalPriceField();
    } else {
        console.warn('[TOTAL PRICE UPDATER] Total price updater not available');
    }
};

window.forceUpdateTotalPrice = () => {
    if (window.totalPriceUpdater) {
        window.totalPriceUpdater.forceUpdate();
    } else {
        console.warn('[TOTAL PRICE UPDATER] Total price updater not available');
    }
};

window.getCurrentTotalPrice = () => {
    if (window.totalPriceUpdater) {
        return window.totalPriceUpdater.getCurrentTotalPrice();
    }
    return 0;
};

window.arePriceComponentsReady = () => {
    if (window.totalPriceUpdater) {
        return window.totalPriceUpdater.arePriceComponentsReady();
    }
    return false;
};

console.log('[TOTAL PRICE UPDATER] Global functions registered: updateTotalPrice(), forceUpdateTotalPrice(), getCurrentTotalPrice(), arePriceComponentsReady()');

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TotalPriceUpdater;
}
