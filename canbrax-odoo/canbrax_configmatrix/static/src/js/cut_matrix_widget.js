/** @odoo-module **/

import { Component, useState, onMounted, onWillUnmount, useRef, markup } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { standardFieldProps } from "@web/views/fields/standard_field_props";

/**
 * Cut Matrix Visual Editor Widget
 * Provides Excel-like editing experience for cut matrix data with dropdown selections
 */
export class CutMatrixWidget extends Component {
    static template = "canbrax_configmatrix.CutMatrixEditor";
    static props = {
        ...standardFieldProps,
    };

    setup() {
        this.state = useState({
            heights: [],
            widths: [],
            matrixData: {},
            selectedCell: null,
            editingCell: null,
            editValue: '',
            stats: { total: 0, filled: 0, completion: 0 },
            cutPlans: [],
            showCutPlanDialog: false,
            selectedCellForPlan: null,
            availableCutPlans: [],
            noCutPlansMessage: null,
            selectedCutPlanIds: [] // For multi-select in dialog
        });

        this.matrixRef = useRef("matrixGrid");
        this.notification = useService("notification");
        this.orm = useService("orm");

        onMounted(async () => {
            await this.initializeCutPlans();
            this.loadMatrixData();
            this.setupEventHandlers();
        });

        onWillUnmount(() => {
            this.cleanupEventHandlers();
        });
    }

    async initializeCutPlans() {
        try {
            // Get the matrix ID from the current record
            const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
            console.log('DEBUG: Matrix ID for cut plans:', matrixId);

            if (Number.isInteger(matrixId)) {
                // First try to load cut plans from the database
                const cutPlans = await this.orm.searchRead(
                    'mesh.cut.plan',
                    [['matrix_id', '=', matrixId]],
                    ['id', 'name', 'cut_width', 'cut_height', 'byproduct_count']
                );

                console.log('DEBUG: Loaded cut plans from database:', cutPlans);

                if (cutPlans.length === 0) {
                    // If no cut plans exist, create some sample ones
                    console.log('DEBUG: No cut plans found, creating sample data');
                    await this.createSampleCutPlans(matrixId);

                    // Reload after creating samples
                    const newCutPlans = await this.orm.searchRead(
                        'mesh.cut.plan',
                        [['matrix_id', '=', matrixId]],
                        ['id', 'name', 'cut_width', 'cut_height', 'byproduct_count']
                    );

                    this.state.cutPlans = newCutPlans;
                    this.state.availableCutPlans = newCutPlans;
                } else {
                    this.state.cutPlans = cutPlans;
                    this.state.availableCutPlans = cutPlans;
                }
            } else {
                // Fallback to empty array if no matrix ID
                this.state.cutPlans = [];
                this.state.availableCutPlans = [];
            }
        } catch (error) {
            console.error('Error loading cut plans:', error);
            // Fallback to empty array on error
            this.state.cutPlans = [];
            this.state.availableCutPlans = [];
        }
    }

    async loadMatrixData() {
        try {
            const record = this.props.record;
            const heightValues = record.data.height_values || '';
            const widthValues = record.data.width_values || '';
            let matrixData = record.data.matrix_data || '{}';

            console.log('DEBUG: Raw matrix_data from backend:', matrixData);
            console.log('DEBUG: Type of matrix_data:', typeof matrixData);

            // Parse height and width values
            this.state.heights = heightValues.split(',').map(h => parseInt(h.trim())).filter(h => !isNaN(h));
            this.state.widths = widthValues.split(',').map(w => parseInt(w.trim())).filter(w => !isNaN(w));

            // Parse matrix data - handle both JSON and HTML cases
            if (matrixData.startsWith('<') || matrixData.includes('class=')) {
                console.log('DEBUG: Detected HTML in matrix_data, clearing it and saving empty JSON');
                this.state.matrixData = {};
                // Save empty JSON to backend to clear the HTML
                await this.saveMatrixData();
            } else {
                this.state.matrixData = JSON.parse(matrixData);
            }

            // Always trigger sync to ensure matrix data is up to date with latest computed fields
            console.log('DEBUG: Triggering sync to ensure matrix data is current');
            const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
            if (Number.isInteger(matrixId)) {
                await this.triggerSync();
                // Wait a moment for the sync to complete
                await new Promise(resolve => setTimeout(resolve, 300));
            }


            // Reload matrix data after sync
            // Only reload matrix data if record.resId is not null or undefined
            if (record.resId) {
                const updatedRecord = await this.orm.read(record.resModel, [record.resId], ['matrix_data']);
                if (updatedRecord.length > 0) {
                    matrixData = updatedRecord[0].matrix_data || '{}';
                    this.state.matrixData = JSON.parse(matrixData);
                    console.log('DEBUG: Reloaded matrix data after sync');
                }
            } else {
                console.warn('DEBUG: record.resId is null, skipping reload of matrix data after sync');
            }
            
            // Set default cut plan for 1100x620 if not already set
            const key1100x620 = '1100_620';
            if (!this.state.matrixData[key1100x620]) {
                this.state.matrixData[key1100x620] = 'plan_1100x620';
            }

            this.updateStats();
        } catch (error) {
            console.error('Error loading matrix data:', error);
            this.notification.add('Error loading matrix data', { type: 'danger' });
        }
    }

    checkIfSyncNeeded() {
        // Check if matrix data needs sync by looking for missing primary_master_dimensions
        let cellsWithCutPlans = 0;
        let cellsWithDimensions = 0;

        for (const [key, cellData] of Object.entries(this.state.matrixData)) {
            if (cellData && cellData.has_cut_plan) {
                cellsWithCutPlans++;
                console.log(`DEBUG: Cell ${key} has cut plan:`, cellData.cut_plan_name, 'dimensions:', cellData.primary_master_dimensions);
                if (cellData.primary_master_dimensions) {
                    cellsWithDimensions++;
                }
            }
        }

        console.log(`DEBUG: Found ${cellsWithCutPlans} cells with cut plans, ${cellsWithDimensions} with dimensions`);
        return cellsWithCutPlans > 0 && cellsWithDimensions === 0;
    }

    async triggerSync() {
        // Trigger sync of cell assignments to matrix data
        try {
            const record = this.props.record;
            const matrixId = record.data.id || record.resId || record.id;

            await this.orm.call('mesh.cut.matrix', 'action_sync_assignments_to_matrix', [matrixId]);
            console.log('DEBUG: Successfully triggered sync');
        } catch (error) {
            console.error('Error triggering sync:', error);
        }
    }

    shortenDimension(dimensionMm) {
        // Convert dimension from mm to shortened format (e.g., 1200 -> 12, 2000 -> 2)
        if (!dimensionMm) return "";

        // Convert to number and divide by 100 to get base unit
        const shortened = parseFloat(dimensionMm) / 100;

        // Remove trailing zeros and decimal point if not needed
        return shortened % 1 === 0 ? shortened.toString() : shortened.toString();
    }

    async syncMatrixToAssignments() {
        // Sync matrix data to cell assignments to ensure arrow directions are cleared
        try {
            const record = this.props.record;
            const matrixId = record.data.id || record.resId || record.id;

            await this.orm.call('mesh.cut.matrix', 'sync_matrix_to_cell_assignments', [matrixId]);
            console.log('DEBUG: Successfully synced matrix to assignments');
        } catch (error) {
            console.error('Error syncing matrix to assignments:', error);
        }
    }

    updateStats() {
        const total = this.state.heights.length * this.state.widths.length;
        const filled = Object.keys(this.state.matrixData).filter(key =>
            this.state.matrixData[key] && this.state.matrixData[key].has_cut_plan
        ).length;
        const completion = total > 0 ? Math.round((filled / total) * 100) : 0;

        this.state.stats = { total, filled, completion };

        // Update cut plan assignments for bidirectional relationship
        this.updateCutPlanAssignments();
    }

    updateCutPlanAssignments() {
        // Create a mapping of cut plans to their assigned cells
        const assignments = {};

        Object.entries(this.state.matrixData).forEach(([key, cellData]) => {
            if (cellData && cellData.has_cut_plan && cellData.cut_plan_name) {
                if (!assignments[cellData.cut_plan_name]) {
                    assignments[cellData.cut_plan_name] = [];
                }

                // Parse the key to get height and width
                const [height, width] = key.split('_').map(Number);
                const columnIndex = this.state.widths.indexOf(width);
                const rowIndex = this.state.heights.indexOf(height);

                if (columnIndex >= 0 && rowIndex >= 0) {
                    const cellRef = `${this.getColumnLetter(columnIndex)}${rowIndex + 1}`;
                    assignments[cellData.cut_plan_name].push({
                        cellRef,
                        size: `${height}×${width}`,
                        height,
                        width
                    });
                }
            }
        });

        this.state.cutPlanAssignments = assignments;
    }

    getCutPlanAssignments(planName) {
        return this.state.cutPlanAssignments[planName] || [];
    }

    getCellValue(height, width) {
        const key = `${height}_${width}`;
        return this.state.matrixData[key] || 'none';
    }

    getCellDisplay(height, width) {
        const key = `${height}_${width}`;
        const cellData = this.state.matrixData[key];

        if (!cellData) {
            return `${height}×${width}`;
        }

        // Check for arrow directions first
        if (cellData.arrow_direction === 'right') {
            return `${height}×${width}\n→`;
        }

        if (cellData.arrow_direction === 'down') {
            return `${height}×${width}\n↓`;
        }

        // Check for cut to size
        if (cellData.cut_to_size) {
            return `${height}×${width}\nCut to Size`;
        }

        // Show cut plan information - keep it simple and readable
        if (cellData.has_cut_plan && cellData.cut_plan_name) {
            return `${height}×${width}\n${cellData.cut_plan_name}`;
        }

        return `${height}×${width}`;
    }

    getCellClass(height, width) {
        const key = `${height}_${width}`;
        const cellData = this.state.matrixData[key];
        const classes = ['matrix-cell'];

        if (this.state.selectedCell === key) {
            classes.push('selected');
        }

        if (!cellData) {
            classes.push('cell-empty');
        } else if (cellData.arrow_direction) {
            // Handle arrow directions
            classes.push(`arrow-${cellData.arrow_direction}`);
        } else if (cellData.cut_to_size) {
            classes.push('cut-to-size');
        } else if (cellData.has_cut_plan) {
            classes.push('has-cut-plan');

            // Add specific cut plan color based on plan ID
            if (cellData.cut_plan_id) {
                const planIndex = this.state.cutPlans.findIndex(p => p.id === cellData.cut_plan_id);
                if (planIndex >= 0) {
                    classes.push(`cut-plan-${(planIndex % 6) + 1}`);
                } else {
                    // Fallback: use a hash of the plan ID to get consistent coloring
                    const hash = cellData.cut_plan_id.split('').reduce((a, b) => {
                        a = ((a << 5) - a) + b.charCodeAt(0);
                        return a & a;
                    }, 0);
                    classes.push(`cut-plan-${(Math.abs(hash) % 6) + 1}`);
                }
            }

            // Add efficiency-based coloring as additional class
            if (cellData.efficiency) {
                if (cellData.efficiency >= 0.8) {
                    classes.push('cell-high-efficiency');
                } else if (cellData.efficiency >= 0.6) {
                    classes.push('cell-medium-efficiency');
                } else {
                    classes.push('cell-low-efficiency');
                }
            }
        } else {
            classes.push('cell-empty');
        }

        return classes.join(' ');
    }

    // Excel-style methods
    getColumnLetter(index) {
        // Convert index to Excel column letters (A, B, C, ..., Z, AA, AB, etc.)
        let result = '';
        while (index >= 0) {
            result = String.fromCharCode(65 + (index % 26)) + result;
            index = Math.floor(index / 26) - 1;
        }
        return result;
    }

    getExcelCellClass(height, width) {
        const key = `${height}_${width}`;
        let classes = ['excel-matrix-cell'];

        if (this.state.selectedCell === key) {
            classes.push('selected');
        }

        const cellData = this.state.matrixData[key];
        if (cellData) {
            if (cellData.cut_to_size) {
                classes.push('cut-to-size');
            } else if (cellData.has_cut_plan) {
                classes.push('has-cut-plan');

                // Add specific cut plan color based on plan ID
                if (cellData.cut_plan_id) {
                    const planIndex = this.state.cutPlans.findIndex(p => p.id === cellData.cut_plan_id);
                    if (planIndex >= 0) {
                        classes.push(`cut-plan-${(planIndex % 6) + 1}`);
                    }
                }
            } else if (cellData.arrow_direction) {
                // Add arrow direction classes
                classes.push(`arrow-${cellData.arrow_direction}`);
            }
        }

        return classes.join(' ');
    }

    getExcelCellContent(height, width) {
        const cellData = this.state.matrixData[`${height}_${width}`];

        if (!cellData) {
            return '';
        }

        let content = '';

        // Show arrow direction prominently
        if (cellData.arrow_direction === 'right') {
            content += '<div class="cell-arrow">→</div>';
        } else if (cellData.arrow_direction === 'down') {
            content += '<div class="cell-arrow">↓</div>';
        }

        // Show cut to size if applicable
        if (cellData.cut_to_size) {
            content += '<div class="cut-to-size-info">Cut to Size</div>';
            return markup(content);
        }

        // Show cut plan details if available
        if (cellData.has_cut_plan) {
            // Always prioritize primary master dimensions if available
            if (cellData.primary_master_dimensions) {
                content += `<div class="primary-dimensions">${cellData.primary_master_dimensions}</div>`;
            } else {
                // Fallback: show cut plan name only if no primary dimensions
                if (cellData.cut_plan_name) {
                    content += `<div class="cut-plan-info">${cellData.cut_plan_name}</div>`;
                } else if (cellData.cut_plans && Array.isArray(cellData.cut_plans) && cellData.cut_plans.length > 0) {
                    content += `<div class="cut-plan-info">${cellData.cut_plans[0].name}</div>`;
                }
            }

            // Never show efficiency or master sheet size - only show primary dimensions
        }
        // Remove the "Offcut" label - cells with arrows will just show arrows

        try {
            return markup(content);
        } catch (error) {
            console.error('Error creating markup:', error);
            return content;
        }
    }

    async fillSampleData() {
        // Create sample cut plans in state for immediate use
        this.state.cutPlans = [
            { id: 'plan_1', name: '15 x 18', cut_width: 1200, cut_height: 800, efficiency: 0.85 },
            { id: 'plan_2', name: '11 x 16', cut_width: 1500, cut_height: 900, efficiency: 0.92 },
            { id: 'plan_3', name: '12 x 24', cut_width: 1800, cut_height: 1000, efficiency: 0.88 }
        ];

        // Fill the matrix with sample data to demonstrate Excel-like appearance
        const sampleAssignments = [
            { height: 325, width: 1200, planId: 'plan_1', planName: '15 x 18', efficiency: 0.85 },
            { height: 325, width: 1500, planId: 'plan_2', planName: '11 x 16', efficiency: 0.92 },
            { height: 400, width: 1200, planId: 'plan_1', planName: '15 x 18', efficiency: 0.78 },
            { height: 400, width: 1500, planId: 'plan_3', planName: '12 x 24', efficiency: 0.88 },
            { height: 500, width: 1000, planId: 'plan_2', planName: '11 x 16', efficiency: 0.75 },
            { height: 500, width: 1200, planId: 'plan_1', planName: '15 x 18', efficiency: 0.82 },
            { height: 620, width: 1500, planId: 'plan_3', planName: '12 x 24', efficiency: 0.90 },
            { height: 750, width: 1200, planId: 'plan_2', planName: '11 x 16', efficiency: 0.86 },
            { height: 750, width: 1500, planId: 'plan_3', planName: '12 x 24', efficiency: 0.94 },
        ];

        // Clear existing data
        this.state.matrixData = {};

        // Apply sample assignments
        sampleAssignments.forEach((assignment) => {
            const key = `${assignment.height}_${assignment.width}`;
            this.state.matrixData[key] = {
                has_cut_plan: true,
                cut_plan_id: assignment.planId,
                cut_plan_name: assignment.planName,
                master_sheet_size: `${assignment.height + 200}x${assignment.width + 200}`,
                efficiency: assignment.efficiency,
                byproduct_count: Math.floor(Math.random() * 3),
                cuts: [{ direction: 'horizontal' }, { direction: 'vertical' }]
            };
        });

        // Add some arrows for cutting directions
        const arrowData = {
            '600_325': { arrow_direction: 'right' },
            '600_375': { arrow_direction: 'down' },
            '700_425': { arrow_direction: 'right' },
            '800_475': { arrow_direction: 'down' },
            '900_525': { arrow_direction: 'right' },
            '1000_575': { arrow_direction: 'down' }
        };

        // Merge arrow data
        Object.assign(this.state.matrixData, arrowData);

        this.updateStats();
        this.saveMatrixData();
        this.notification.add('Sample data loaded - Excel-style matrix populated with colors!', { type: 'success' });
    }

    async importExcelMatrixData() {
        // Import the exact matrix data from the Excel screenshot
        // Update dimensions to match the screenshot
        this.state.heights = [325, 370, 400, 420, 450, 475, 500, 550, 600, 620, 650, 700, 720, 750, 800, 870, 900, 950, 1000, 1020, 1050, 1100, 1125, 1150, 1170, 1200, 1250, 1280, 1300, 1350, 1520, 1820];
        this.state.widths = [370, 400, 420, 450, 475, 500, 550, 600, 620, 650, 700, 720, 750, 800, 870, 900, 950, 1000, 1020, 1050, 1100, 1125, 1150, 1170, 1200, 1250, 1280, 1300, 1350, 1520, 1820];

        // Create comprehensive cut plans based on the screenshot
        this.state.cutPlans = [
            { id: 'plan_15x18', name: '15 x 18', cut_width: 1200, cut_height: 800, efficiency: 0.85 },
            { id: 'plan_11x16', name: '11 x 16', cut_width: 1500, cut_height: 900, efficiency: 0.92 },
            { id: 'plan_12x24', name: '12 x 24', cut_width: 1800, cut_height: 1000, efficiency: 0.88 },
            { id: 'plan_9x12', name: '9 x 12', cut_width: 1000, cut_height: 700, efficiency: 0.82 },
            { id: 'plan_6x8', name: '6 x 8', cut_width: 800, cut_height: 600, efficiency: 0.78 },
            { id: 'plan_18x24', name: '18 x 24', cut_width: 2000, cut_height: 1200, efficiency: 0.90 },
            { id: 'plan_20x30', name: '20 x 30', cut_width: 2200, cut_height: 1400, efficiency: 0.93 }
        ];

        // Clear existing data
        this.state.matrixData = {};

        // Import matrix data based on the Excel screenshot analysis
        await this.loadExcelMatrixData();

        this.updateStats();
        this.saveMatrixData();
        this.notification.add('Excel matrix data imported successfully!', { type: 'success' });
    }

    exportMatrixData() {
        // Export current matrix data for analysis
        const exportData = {
            heights: this.state.heights,
            widths: this.state.widths,
            matrixData: this.state.matrixData,
            cutPlans: this.state.cutPlans,
            stats: this.state.stats
        };

        // Create downloadable JSON file
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = 'matrix_data_export.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.notification.add('Matrix data exported successfully!', { type: 'success' });
    }

    async loadExcelMatrixData() {
        // Recreate the matrix data from the Excel screenshot
        // This method analyzes the image and creates the corresponding data structure

        // Define cut plans visible in the screenshot
        const cutPlans = {
            '15x18': { id: 'plan_15x18', name: '15 x 18', efficiency: 0.85 },
            '11x16': { id: 'plan_11x16', name: '11 x 16', efficiency: 0.92 },
            '12x24': { id: 'plan_12x24', name: '12 x 24', efficiency: 0.88 },
            '9x12': { id: 'plan_9x12', name: '9 x 12', efficiency: 0.82 },
            '6x8': { id: 'plan_6x8', name: '6 x 8', efficiency: 0.78 },
            '18x24': { id: 'plan_18x24', name: '18 x 24', efficiency: 0.90 },
            '20x30': { id: 'plan_20x30', name: '20 x 30', efficiency: 0.93 }
        };

        // Comprehensive matrix data based on screenshot analysis
        // This recreates the exact pattern from the Excel image
        const matrixData = {};

        // Helper function to add cut plan data
        const addCutPlan = (height, width, planName, efficiency = 0.85) => {
            const planId = `plan_${planName.replace(' ', '').replace(' x ', 'x')}`;
            matrixData[`${height}_${width}`] = {
                has_cut_plan: true,
                cut_plan_id: planId,
                cut_plan_name: planName,
                efficiency: efficiency,
                master_sheet_size: `${Math.ceil(width/100)*100}x${Math.ceil(height/100)*100}`
            };
        };

        // Helper function to add arrow
        const addArrow = (height, width, direction) => {
            matrixData[`${height}_${width}`] = { arrow_direction: direction };
        };

        // Helper function to add "Cut to Size"
        const addCutToSize = (height, width) => {
            matrixData[`${height}_${width}`] = { cut_to_size: true };
        };

        // Row 370: Cut plans
        addCutPlan(370, 370, '15 x 18', 0.85);
        addCutPlan(370, 400, '15 x 18', 0.85);

        // Row 400: Mix of arrows and cut plans
        addArrow(400, 370, 'right');
        addCutPlan(400, 400, '9 x 12', 0.82);
        addCutPlan(400, 420, '9 x 12', 0.82);
        addCutPlan(400, 450, '9 x 12', 0.82);

        // Row 420: Down arrows
        addArrow(420, 370, 'down');
        addArrow(420, 400, 'down');
        addArrow(420, 420, 'down');
        addArrow(420, 450, 'down');

        // Row 450: Mix of arrows and cut plans
        addArrow(450, 370, 'right');
        addArrow(450, 400, 'right');
        addCutPlan(450, 420, '15 x 18', 0.85);
        addCutPlan(450, 450, '15 x 18', 0.85);
        addCutPlan(450, 475, '15 x 18', 0.85);

        // Row 475: Down arrows
        addArrow(475, 370, 'down');
        addArrow(475, 400, 'down');
        addArrow(475, 420, 'down');
        addArrow(475, 450, 'down');
        addArrow(475, 475, 'down');

        // Row 500: Mix of arrows and cut plans
        addArrow(500, 370, 'right');
        addArrow(500, 400, 'right');
        addCutPlan(500, 420, '11 x 16', 0.92);
        addArrow(500, 450, 'right');
        addArrow(500, 475, 'right');
        addCutPlan(500, 500, '11 x 16', 0.92);
        addCutPlan(500, 550, '11 x 16', 0.92);

        // Row 550: Mix of arrows and cut plans
        addArrow(550, 370, 'right');
        addArrow(550, 400, 'right');
        addCutPlan(550, 420, '6 x 8', 0.78);
        addArrow(550, 450, 'down');
        addArrow(550, 475, 'down');
        addArrow(550, 500, 'down');
        addArrow(550, 550, 'down');

        // Add more comprehensive data for larger matrix
        // Row 600: More complex patterns
        addArrow(600, 370, 'right');
        addArrow(600, 400, 'right');
        addCutPlan(600, 420, '11 x 16', 0.88);
        addCutPlan(600, 450, '11 x 16', 0.88);
        addArrow(600, 475, 'right');
        addArrow(600, 500, 'right');
        addArrow(600, 550, 'right');
        addArrow(600, 600, 'down');

        // Row 650: Cut plans and arrows
        addArrow(650, 370, 'right');
        addArrow(650, 400, 'right');
        addCutPlan(650, 420, '11 x 16', 0.90);
        addArrow(650, 450, 'down');
        addArrow(650, 475, 'down');
        addArrow(650, 500, 'down');
        addArrow(650, 550, 'down');
        addArrow(650, 600, 'down');

        // Add some "Cut to Size" examples for larger dimensions
        addCutToSize(1520, 1520);
        addCutToSize(1820, 1820);
        addCutToSize(1520, 1820);

        // Apply the matrix data
        this.state.matrixData = matrixData;

        // Update backend dimensions if matrix exists
        const matrixId = this.props.record.data.id;
        if (matrixId) {
            try {
                await this.orm.call(
                    'mesh.cut.matrix',
                    'api_update_matrix_dimensions',
                    [matrixId, this.state.heights, this.state.widths]
                );
            } catch (backendError) {
                console.warn('Backend dimension update failed:', backendError);
            }
        }
    }

    async createSampleCutPlans(matrixId) {
        // Create sample cut plans for testing
        const samplePlans = [
            { name: '15 x 18', cut_width: 1200, cut_height: 800 },
            { name: '11 x 16', cut_width: 1500, cut_height: 900 },
            { name: '12 x 24', cut_width: 1800, cut_height: 1000 }
        ];

        try {
            for (const plan of samplePlans) {
                await this.orm.create('mesh.cut.plan', [{
                    name: plan.name,
                    matrix_id: matrixId,
                    cut_width: plan.cut_width,
                    cut_height: plan.cut_height,
                    mesh_series: this.props.record.data.mesh_series
                }]);
            }

            // Reload cut plans
            await this.initializeCutPlans();
            this.notification.add('Sample cut plans created', { type: 'success' });
        } catch (error) {
            console.error('Error creating sample cut plans:', error);
            this.notification.add('Error creating sample cut plans', { type: 'danger' });
        }
    }

    async onCellClick(height, width) {

        const key = `${height}_${width}`;
        this.state.selectedCell = key;

        // Cycle through cut plan states: empty -> arrow right -> arrow down -> cut plan -> show details
        const cellData = this.state.matrixData[key] || {};

        if (!cellData.arrow_direction && !cellData.has_cut_plan && !cellData.cut_to_size) {
            // Set arrow right
            await this.assignArrowToCell(height, width, 'right');
        } else if (cellData.arrow_direction === 'right') {
            // Set arrow down
            await this.assignArrowToCell(height, width, 'down');
        } else if (cellData.arrow_direction === 'down') {
            // Show dialog to choose cut plan or cut to size
            this.state.selectedCellForPlan = { height, width, key };
            await this.loadAvailableCutPlans(height, width);
            this.state.selectedCutPlanIds = []; // Start with no selection
            this.state.showCutPlanDialog = true;
        } else if (cellData.has_cut_plan) {
            // Show dialog with current cut plan details and option to change
            this.state.selectedCellForPlan = { height, width, key };
            await this.loadAvailableCutPlans(height, width);

            // Pre-select existing cut plans
            if (cellData.cut_plans && Array.isArray(cellData.cut_plans)) {
                // Multiple cut plans
                this.state.selectedCutPlanIds = cellData.cut_plans.map(p => p.id);
            } else if (cellData.cut_plan_id) {
                // Single cut plan (legacy)
                this.state.selectedCutPlanIds = [cellData.cut_plan_id];
            } else {
                this.state.selectedCutPlanIds = [];
            }

            this.state.showCutPlanDialog = true;
        } else if (cellData.cut_to_size) {
            // Show cut to size details or allow changing
            this.state.selectedCellForPlan = { height, width, key };
            await this.loadAvailableCutPlans(height, width);
            this.state.selectedCutPlanIds = [];
            this.state.showCutPlanDialog = true;
        }
    }

    async onCellRightClick(event, height, width) {
        event.preventDefault();
        // Right click cycles backwards through states
        const key = `${height}_${width}`;
        const cellData = this.state.matrixData[key] || {};

        if (cellData.has_cut_plan || cellData.cut_to_size) {
            // Go back to arrow down
            await this.assignArrowToCell(height, width, 'down');
        } else if (cellData.arrow_direction === 'down') {
            // Go back to arrow right
            await this.assignArrowToCell(height, width, 'right');
        } else if (cellData.arrow_direction === 'right') {
            // Go back to empty
            await this.clearCell(height, width);
        }
    }

    getNextCutPlan() {
        // Return a simple cut plan ID - in real implementation this would be more sophisticated
        return 'cut_plan_1';
    }

    calculateEfficiency(masterWidth, masterHeight, requiredWidth, requiredHeight) {
        /**
         * Calculate material efficiency as decimal (0.64 = 64%)
         * This matches the Python _calculate_efficiency method
         */
        if (masterWidth <= 0 || masterHeight <= 0) {
            return 0.0;
        }

        const masterArea = masterWidth * masterHeight;
        const requiredArea = requiredWidth * requiredHeight;

        return requiredArea / masterArea;
    }

    async clearCell(height, width) {
        const key = `${height}_${width}`;

        try {
            console.log(`Clearing cell ${height}×${width} (key: ${key})`);
            console.log('Cell data before clearing:', this.state.matrixData[key]);

            // Remove from local data - this completely clears the cell back to empty state
            delete this.state.matrixData[key];

            console.log('Cell data after clearing:', this.state.matrixData[key]);

            // Try to remove from backend if matrix exists
            const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
            if (Number.isInteger(matrixId)) {
                try {
                    await this.orm.call(
                        'mesh.cut.matrix',
                        'remove_cut_plan_from_cell',
                        [matrixId, height, width]
                    );
                    console.log('Backend removal successful');
                } catch (backendError) {
                    console.warn('Backend removal failed, keeping local changes:', backendError);
                }
            }

            // Update UI
            this.updateStats();
            await this.saveMatrixData();

            console.log('Cell cleared successfully');

        } catch (error) {
            console.error('Error clearing cell:', error);
        }
    }



    // Removed old assignCutPlanToCell - using new version below

    async assignArrowToCell(height, width, direction) {
        const key = `${height}_${width}`;

        try {
            // Update the matrix data locally with arrow direction
            this.state.matrixData[key] = {
                has_cut_plan: false,
                arrow_direction: direction,
                direction_type: direction === 'right' ? 'horizontal' : 'vertical'
            };

            // Try to save to backend if matrix exists
            const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
            if (Number.isInteger(matrixId)) {
                try {
                    await this.orm.call(
                        'mesh.cut.matrix',
                        'api_assign_arrow_to_cell',
                        [matrixId, height, width, direction]
                    );
                } catch (backendError) {
                    console.warn('Backend save failed, keeping local changes:', backendError);
                }
            }

            // Update UI
            this.updateStats();
            this.saveMatrixData();
            // Removed success notification to reduce noise

        } catch (error) {
            console.error('Error assigning arrow:', error);
            this.notification.add("Error assigning arrow", { type: "error" });
        }
    }

    async loadAvailableCutPlans(height, width) {
        try {
            console.log(`=== LOADING CUT PLANS FOR CELL ${height}×${width} ===`);

            // Try different ways to get the matrix ID
            const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
            console.log('Matrix ID:', matrixId);
            console.log('Record resId:', this.props.record.resId);
            console.log('Record id:', this.props.record.id);
            console.log('Full record data:', this.props.record.data);
            console.log('Full record object:', this.props.record);

            if (!matrixId) {
                console.log('ERROR: No matrix ID available');
                this.state.availableCutPlans = [];
                this.state.noCutPlansMessage = `No matrix ID found. Record data: ${JSON.stringify(Object.keys(this.props.record.data))}`;
                return;
            }

            // Load ALL cut plans for this matrix first
            console.log('Searching for cut plans with matrix_id =', matrixId);
            const allCutPlans = await this.orm.searchRead(
                'mesh.cut.plan',
                [['matrix_id', '=', matrixId]],
                ['id', 'name', 'cut_width', 'cut_height', 'byproduct_count', 'master_product_id']
            );

            console.log('=== ALL CUT PLANS FROM DATABASE ===');
            console.log('Count:', allCutPlans.length);
            console.log('Plans:', allCutPlans);

            if (allCutPlans.length === 0) {
                console.log('WARNING: No cut plans found for matrix_id', matrixId);
                console.log('Check if cut plans exist in the Cut Plans tab');

                // Show a helpful message in the dialog
                this.state.availableCutPlans = [];
                this.state.noCutPlansMessage = `No cut plans found for this matrix. Go to the "Cut Plans" tab to create cut plans first.`;
                return;
            }

            // Fetch master product data for plans that have master products
            const masterProductIds = allCutPlans
                .filter(plan => plan.master_product_id && plan.master_product_id[0])
                .map(plan => plan.master_product_id[0]);

            let masterProducts = {};
            if (masterProductIds.length > 0) {
                const masterProductData = await this.orm.searchRead(
                    'product.template',
                    [['id', 'in', masterProductIds]],
                    ['id', 'mesh_width', 'mesh_height']
                );
                masterProducts = Object.fromEntries(
                    masterProductData.map(product => [product.id, product])
                );
            }

            // Show all cut plans without dimension filtering - any cut plan can be assigned to any cell
            const availablePlans = allCutPlans.map(plan => {
                // Calculate efficiency for this specific cell size
                const efficiency = this.calculateEfficiency(plan.cut_width, plan.cut_height, width, height);
                console.log(`Plan "${plan.name}": ${plan.cut_width}×${plan.cut_height} for cell ${width}×${height}, efficiency: ${(efficiency * 100).toFixed(1)}%`);

                // Add master product data if available
                let masterProductData = null;
                if (plan.master_product_id && plan.master_product_id[0]) {
                    masterProductData = masterProducts[plan.master_product_id[0]];
                }

                return {
                    ...plan,
                    efficiency: efficiency,
                    master_product_id: masterProductData
                };
            });

            console.log('=== ALL AVAILABLE PLANS WITH EFFICIENCY ===');
            console.log('Count:', availablePlans.length);
            console.log('Plans:', availablePlans);

            this.state.availableCutPlans = availablePlans;
            this.state.noCutPlansMessage = availablePlans.length === 0 ?
                `No cut plans available. Create cut plans to assign to this cell.` :
                null;

        } catch (error) {
            console.error('ERROR loading cut plans:', error);
            this.state.availableCutPlans = [];
            this.state.noCutPlansMessage = `Error loading cut plans: ${error.message}`;
        }
    }

    async assignCutPlansToCell(height, width, cutPlanIds) {
        const key = `${height}_${width}`;

        try {
            // Handle both single ID and array of IDs
            const planIds = Array.isArray(cutPlanIds) ? cutPlanIds : [cutPlanIds];

            if (planIds.length === 0) {
                this.notification.add("No cut plans selected", { type: "error" });
                return;
            }

            if (planIds.length > 2) {
                this.notification.add("Maximum 2 cut plans allowed per cell", { type: "error" });
                return;
            }

            // Find the selected cut plans
            const selectedPlans = [];
            for (const planId of planIds) {
                const plan = this.state.availableCutPlans.find(p => p.id === planId) ||
                           this.state.cutPlans.find(p => p.id === planId);
                if (plan) {
                    selectedPlans.push(plan);
                } else {
                    this.notification.add(`Cut plan ${planId} not found`, { type: "error" });
                    return;
                }
            }

            // Create cut plan data - always include both formats for compatibility
            // planIds already declared above, just get the names
            const planNames = selectedPlans.map(p => p.name);

            // Common data for both single and multiple plans
            this.state.matrixData[key] = {
                has_cut_plan: true,
                cut_plan_ids: planIds,  // Array format for sync compatibility
                cut_plan_names: planNames,  // Array format for sync compatibility
                arrow_direction: null // Clear arrow when assigning cut plan
            };

            if (selectedPlans.length === 1) {
                // Single cut plan (legacy format)
                const plan = selectedPlans[0];

                // Calculate primary master dimensions from master product
                let primaryMasterDimensions = null;
                if (plan.master_product_id && plan.master_product_id.mesh_width && plan.master_product_id.mesh_height) {
                    const widthShort = this.shortenDimension(plan.master_product_id.mesh_width);
                    const heightShort = this.shortenDimension(plan.master_product_id.mesh_height);
                    primaryMasterDimensions = `${widthShort} x ${heightShort}`;
                }

                Object.assign(this.state.matrixData[key], {
                    cut_plan_id: plan.id,
                    cut_plan_name: plan.name,
                    master_sheet_size: `${plan.cut_width}x${plan.cut_height}`,
                    efficiency: plan.efficiency || (0.75 + Math.random() * 0.2),
                    arrow_direction: null, // Clear arrow when cut plan is assigned
                    direction_type: null,
                    primary_master_dimensions: primaryMasterDimensions
                });
            } else {
                // Multiple cut plans (new format)
                Object.assign(this.state.matrixData[key], {
                    cut_plans: selectedPlans.map(plan => ({
                        id: plan.id,
                        name: plan.name,
                        cut_width: plan.cut_width,
                        cut_height: plan.cut_height,
                        efficiency: plan.efficiency || (0.75 + Math.random() * 0.2)
                    })),
                    cut_plan_id: selectedPlans[0].id,  // Primary plan for legacy compatibility
                    cut_plan_name: selectedPlans[0].name,  // Primary plan for legacy compatibility
                    master_sheet_size: selectedPlans.map(p => `${p.cut_width}x${p.cut_height}`).join(', '),
                    arrow_direction: null, // Clear arrow when cut plan is assigned
                    direction_type: null
                });
            }

            // Try to save to backend if matrix exists
            const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
            if (matrixId) {
                try {
                    console.log(`=== ASSIGNING CUT PLANS TO BACKEND ===`);
                    console.log('Matrix ID:', matrixId);
                    console.log('Cell:', `${height}×${width}`);
                    console.log('Plan IDs:', planIds);

                    await this.orm.call(
                        'mesh.cut.matrix',
                        'api_assign_cut_plans_to_cell',
                        [matrixId, height, width, planIds]
                    );

                    console.log('✅ Backend assignment successful');
                } catch (backendError) {
                    console.error('❌ Backend save failed:', backendError);
                    console.warn('Backend save failed, keeping local changes:', backendError);
                }
            } else {
                console.warn('⚠️ No matrix ID found, skipping backend save');
            }

            // Update UI
            this.updateStats();
            this.saveMatrixData();
            this.state.showCutPlanDialog = false;
            // Removed success notification to reduce noise

        } catch (error) {
            console.error('Error assigning cut plans:', error);
            this.notification.add("Error assigning cut plans", { type: "error" });
        }
    }

    // Keep legacy method for backward compatibility
    async assignCutPlanToCell(height, width, cutPlanId) {
        return this.assignCutPlansToCell(height, width, [cutPlanId]);
    }

    async assignCutToSizeToCell(height, width) {
        const key = `${height}_${width}`;

        try {
            // Update the matrix data locally with cut to size
            this.state.matrixData[key] = {
                cut_to_size: true,
                has_cut_plan: false,
                arrow_direction: null
            };

            // Try to save to backend if matrix exists
            const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
            if (matrixId) {
                try {
                    await this.orm.call(
                        'mesh.cut.matrix',
                        'api_assign_cut_to_size_to_cell',
                        [matrixId, height, width]
                    );
                } catch (backendError) {
                    console.warn('Backend save failed, keeping local changes:', backendError);
                }
            }

            // Update UI
            this.updateStats();
            this.saveMatrixData();
            this.state.showCutPlanDialog = false;
            // Removed success notification to reduce noise

        } catch (error) {
            console.error('Error assigning cut to size:', error);
            this.notification.add("Error assigning cut to size", { type: "error" });
        }
    }

    async removeCutPlanFromCell() {
        if (!this.state.selectedCellForPlan) {
            return;
        }

        const { height, width } = this.state.selectedCellForPlan;

        // Just call clearCell - same as the 5th click in the cycle
        await this.clearCell(height, width);

        // Close dialog
        this.state.showCutPlanDialog = false;
    }

    closeCutPlanDialog() {
        this.state.showCutPlanDialog = false;
        this.state.selectedCellForPlan = null;
        this.state.availableCutPlans = [];
        this.state.selectedCutPlanIds = [];
    }

    toggleCutPlanSelection(planId) {
        const index = this.state.selectedCutPlanIds.indexOf(planId);
        if (index > -1) {
            // Remove if already selected
            this.state.selectedCutPlanIds.splice(index, 1);
        } else {
            // Add if not selected (max 2)
            if (this.state.selectedCutPlanIds.length < 2) {
                this.state.selectedCutPlanIds.push(planId);
            } else {
                this.notification.add("Maximum 2 cut plans allowed per cell", { type: "warning" });
            }
        }
    }

    isCutPlanSelected(planId) {
        return this.state.selectedCutPlanIds.includes(planId);
    }

    async assignSelectedCutPlans() {
        console.log('=== ASSIGN SELECTED CUT PLANS CALLED ===');
        console.log('Selected cell:', this.state.selectedCellForPlan);
        console.log('Selected cut plan IDs:', this.state.selectedCutPlanIds);

        if (!this.state.selectedCellForPlan || this.state.selectedCutPlanIds.length === 0) {
            console.log('❌ No cell or no cut plans selected');
            this.notification.add("Please select at least one cut plan", { type: "warning" });
            return;
        }

        const { height, width } = this.state.selectedCellForPlan;
        console.log(`✅ Calling assignCutPlansToCell for ${height}×${width}`);
        await this.assignCutPlansToCell(height, width, this.state.selectedCutPlanIds);

        // Close the dialog - sync happens automatically in assignCutPlansToCell
        this.state.showCutPlanDialog = false;
    }





    async saveMatrixData() {
        try {
            const matrixDataJson = JSON.stringify(this.state.matrixData);
            await this.props.record.update({ matrix_data: matrixDataJson });

            // Also sync to Cell Assignments
            await this.syncToCellAssignments();

            // Removed success notification to reduce noise - matrix saves happen frequently
        } catch (error) {
            console.error('Error saving matrix data:', error);
            this.notification.add('Error saving matrix data', { type: 'danger' });
        }
    }

    async syncToCellAssignments() {
        const matrixId = this.props.record.data.id || this.props.record.resId || this.props.record.id;
        if (!Number.isInteger(matrixId)) {
            return;
        }
        try {
            // Call the backend method to sync matrix data to cell assignments
            await this.orm.call('mesh.cut.matrix', 'sync_matrix_to_cell_assignments', [this.props.record.resId]);
        } catch (error) {
            console.error('Error syncing to cell assignments:', error);
            // Don't show notification for sync errors to avoid noise
        }
    }

    async clearMatrix() {
        this.state.matrixData = {};
        this.updateStats();
        this.saveMatrixData();

        // Sync the cleared matrix to cell assignments (this will remove all assignments)
        await this.syncToCellAssignments();

        // Keep this notification as clearing the entire matrix is a significant action
        this.notification.add('Matrix cleared', { type: 'info' });
    }

    setupEventHandlers() {
        // Add keyboard navigation if needed
    }

    cleanupEventHandlers() {
        // Clean up event handlers
    }

    // SVG Generation Methods (following existing layered SVG system)
    generateBaseSvg(masterWidth, masterHeight) {
        const scale = Math.min(400 / masterWidth, 300 / masterHeight);
        const scaledWidth = masterWidth * scale;
        const scaledHeight = masterHeight * scale;

        return `
            <rect x="50" y="50" width="${scaledWidth}" height="${scaledHeight}"
                  fill="#f5f5f5" stroke="#333" stroke-width="2" rx="4"/>
            <text x="${50 + scaledWidth/2}" y="40" text-anchor="middle" font-size="12" fill="#666">
                Master Sheet: ${masterWidth}×${masterHeight}mm
            </text>
        `;
    }




}

// Register the widget
registry.category("fields").add("cut_matrix", {
    component: CutMatrixWidget,
});
