/** @odoo-module **/

import { Component, onMounted, onWillUpdateProps, onWillUnmount, useState, useRef } from "@odoo/owl";
import { registry } from "@web/core/registry";

/**
 * Price Matrix Visual Editor Widget
 * Provides Excel-like editing experience for price matrix data
 */
export class PriceMatrixWidget extends Component {
    static template = "canbrax_configmatrix.InteractiveMatrixEditor";

    setup() {
        this.state = useState({
            heights: [],
            widths: [],
            matrixData: {},
            selectedCell: null,
            editingCell: null,
            editValue: '',
            stats: { total: 0, filled: 0, completion: 0 }
        });

        this.matrixRef = useRef("matrixGrid");
        this.lastKnownHeightRanges = "[]";
        this.lastKnownWidthRanges = "[]";
        this.lastKnownMatrixData = "{}";

        onMounted(() => {
            this.loadMatrixData();
            this.setupEventHandlers();
            this.setupResizeHandler();
            this.adjustContainerWidth();

            // Set up auto-refresh to catch data changes
            this.autoRefreshInterval = setInterval(() => {
                const currentHeightRanges = this.props.record?.data.height_ranges || "[]";
                const currentWidthRanges = this.props.record?.data.width_ranges || "[]";
                const currentMatrixData = this.props.record?.data.matrix_data || "{}";

                if (currentHeightRanges !== this.lastKnownHeightRanges ||
                    currentWidthRanges !== this.lastKnownWidthRanges ||
                    currentMatrixData !== this.lastKnownMatrixData) {
                    this.lastKnownHeightRanges = currentHeightRanges;
                    this.lastKnownWidthRanges = currentWidthRanges;
                    this.lastKnownMatrixData = currentMatrixData;
                    this.loadMatrixData();
                }
            }, 2000);
        });

        // Watch for changes in the field value
        onWillUpdateProps((nextProps) => {
            if (nextProps.record.data[this.props.name] !== this.props.record.data[this.props.name] ||
                nextProps.record.data.height_ranges !== this.props.record.data.height_ranges ||
                nextProps.record.data.width_ranges !== this.props.record.data.width_ranges) {
                // Update props reference and reload
                this.props = nextProps;
                this.loadMatrixData();
            }
        });

        onWillUnmount(() => {
            this.cleanupEventHandlers();
            this.cleanupResizeHandler();
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
            }
        });
    }

    loadMatrixData() {
        try {
            // Get data from the price matrix record
            const record = this.props.record;
            if (!record || !record.data) {
                this.state.heights = [];
                this.state.widths = [];
                this.state.matrixData = {};
                return;
            }

            // Handle Python-style single quotes by converting to valid JSON
            const heightRangesJson = (record.data.height_ranges || '[]').replace(/'/g, '"');
            const widthRangesJson = (record.data.width_ranges || '[]').replace(/'/g, '"');
            const matrixDataJson = (record.data.matrix_data || '{}').replace(/'/g, '"');

            // Parse height and width ranges
            const heightRanges = JSON.parse(heightRangesJson);
            const widthRanges = JSON.parse(widthRangesJson);
            const matrixData = JSON.parse(matrixDataJson);

            // Extract labels from ranges (support both old min/max format and new value format)
            this.state.heights = heightRanges.map(range => {
                if (range.value !== undefined) {
                    return range.label || range.value.toString();
                } else {
                    return range.label || range.max.toString();
                }
            });
            this.state.widths = widthRanges.map(range => {
                if (range.value !== undefined) {
                    return range.label || range.value.toString();
                } else {
                    return range.label || range.max.toString();
                }
            });
            this.state.matrixData = matrixData;

            this.calculateStats();
            this.renderMatrix();
        } catch (error) {
            this.state.heights = [];
            this.state.widths = [];
            this.state.matrixData = {};
        }
    }

    calculateStats() {
        const total = this.state.heights.length * this.state.widths.length;
        const filled = Object.keys(this.state.matrixData).filter(key => this.state.matrixData[key]).length;
        const completion = total > 0 ? Math.round((filled / total) * 100) : 0;

        this.state.stats = { total, filled, completion };
    }

    renderMatrix() {
        if (!this.matrixRef.el) return;

        const table = this.matrixRef.el;
        table.innerHTML = '';

        if (this.state.heights.length === 0 || this.state.widths.length === 0) {
            table.innerHTML = `
                <tr>
                    <td colspan="100%" class="text-center text-muted p-5">
                        <div class="matrix-loading">
                            <i class="fa fa-info-circle fa-2x mb-3" style="color: #6c757d;"></i>
                            <div>
                                <h5>No Matrix Data</h5>
                                <p class="mb-0">Configure height and width ranges in the Matrix Configuration tab to see the matrix</p>
                            </div>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        // Create header row
        const headerRow = document.createElement('tr');
        const cornerCell = document.createElement('th');
        cornerCell.className = 'matrix-corner-cell';
        cornerCell.textContent = 'H\\W';
        headerRow.appendChild(cornerCell);

        this.state.widths.forEach(width => {
            const th = document.createElement('th');
            th.className = 'matrix-header-cell';
            th.textContent = width;
            headerRow.appendChild(th);
        });

        table.appendChild(headerRow);

        // Create data rows
        this.state.heights.forEach((height, rowIndex) => {
            const row = document.createElement('tr');

            // Row header
            const rowHeader = document.createElement('th');
            rowHeader.className = 'matrix-row-header';
            rowHeader.textContent = height;
            row.appendChild(rowHeader);

            // Data cells
            this.state.widths.forEach((width, colIndex) => {
                const cell = document.createElement('td');
                const cellKey = `${height}_${width}`;
                const cellValue = this.state.matrixData[cellKey] || '';

                cell.className = 'matrix-data-cell';
                cell.dataset.height = height;
                cell.dataset.width = width;
                cell.dataset.key = cellKey;

                // Apply Excel-like styling
                if (cellValue) {
                    cell.textContent = this.formatCellValue(cellValue);
                    cell.classList.add('has-value');
                } else {
                    cell.textContent = '-';
                    cell.classList.add('empty-value');
                }

                // Hover effects are now handled by CSS

                // Add click handlers
                cell.addEventListener('click', () => this.onCellClick(height, width, cellKey));
                cell.addEventListener('dblclick', () => this.onCellDoubleClick(height, width, cellKey));

                row.appendChild(cell);
            });

            table.appendChild(row);
        });
    }

    formatCellValue(value) {
        if (typeof value === 'number') {
            return value.toFixed(4);
        }
        return value.toString();
    }

    onCellClick(height, width, cellKey) {
        // Clear previous selection
        this.clearSelection();

        // Set new selection
        this.state.selectedCell = { height, width, key: cellKey };

        // Update visual selection
        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        if (cell) {
            cell.classList.add('selected');
        }
    }

    onCellDoubleClick(height, width, cellKey) {
        this.startCellEdit(height, width, cellKey);
    }

    startCellEdit(height, width, cellKey) {
        this.state.editingCell = { height, width, key: cellKey };
        this.state.editValue = this.state.matrixData[cellKey] || '';

        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        if (cell) {
            cell.classList.add('editing');

            // Create input element
            const input = document.createElement('input');
            input.type = 'number';
            input.step = '0.01';
            input.className = 'matrix-cell-input';
            input.value = this.state.editValue;

            // Replace cell content with input
            cell.innerHTML = '';
            cell.appendChild(input);

            // Focus and select
            input.focus();
            input.select();

            // Handle input events
            input.addEventListener('blur', () => this.confirmCellEdit(cellKey));
            input.addEventListener('keydown', (e) => this.handleInputKeydown(e, cellKey));
        }
    }

    handleInputKeydown(event, cellKey) {
        switch(event.key) {
            case 'Enter':
                event.preventDefault();
                this.confirmCellEdit(cellKey);
                break;
            case 'Escape':
                event.preventDefault();
                this.cancelCellEdit(cellKey);
                break;
            case 'Tab':
                event.preventDefault();
                this.confirmCellEdit(cellKey);
                this.navigateToNextCell(cellKey, event.shiftKey ? -1 : 1);
                break;
        }
    }

    confirmCellEdit(cellKey) {
        const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
        const input = cell?.querySelector('.matrix-cell-input');

        if (input) {
            const newValue = parseFloat(input.value) || 0;

            // Update matrix data
            if (newValue === 0) {
                delete this.state.matrixData[cellKey];
            } else {
                this.state.matrixData[cellKey] = newValue;
            }

            // Update the record field
            this.updateRecordField();

            // Re-render the cell
            this.endCellEdit(cellKey);
            this.calculateStats();

            // No notification - silent update
        }
    }

    cancelCellEdit(cellKey) {
        this.endCellEdit(cellKey);
    }

    endCellEdit(cellKey) {
        this.state.editingCell = null;
        this.state.editValue = '';
        this.renderMatrix(); // Re-render to restore proper styling
    }

    updateRecordField() {
        // Update the matrix_data field in the record
        try {
            const jsonString = JSON.stringify(this.state.matrixData);
            this.props.record.update({ [this.props.name]: jsonString });
        } catch (error) {
            // Silent error handling - field update failed
        }
    }

    clearSelection() {
        this.matrixRef.el?.querySelectorAll('.matrix-data-cell.selected').forEach(cell => {
            cell.classList.remove('selected');
        });
        this.state.selectedCell = null;
        this.renderMatrix(); // Re-render to restore proper styling
    }

    setupEventHandlers() {
        // Add keyboard handlers for the matrix
        this.keyHandler = (event) => {
            if (!this.matrixRef.el?.contains(event.target)) return;

            // Handle matrix navigation
            if (this.state.selectedCell && !this.state.editingCell) {
                switch(event.key) {
                    case 'Enter':
                    case 'F2':
                        event.preventDefault();
                        const { height, width, key } = this.state.selectedCell;
                        this.startCellEdit(height, width, key);
                        break;
                    case 'Delete':
                    case 'Backspace':
                        event.preventDefault();
                        this.deleteSelectedCell();
                        break;
                }
            }
        };

        document.addEventListener('keydown', this.keyHandler);
    }

    cleanupEventHandlers() {
        if (this.keyHandler) {
            document.removeEventListener('keydown', this.keyHandler);
        }
    }

    deleteSelectedCell() {
        if (this.state.selectedCell) {
            const { key } = this.state.selectedCell;
            delete this.state.matrixData[key];
            this.updateRecordField();
            this.renderMatrix();
            this.calculateStats();
            // No notification - silent delete
        }
    }

    navigateToNextCell(currentKey, direction) {
        const [currentHeight, currentWidth] = currentKey.split('_');
        const heightIndex = this.state.heights.indexOf(currentHeight);
        const widthIndex = this.state.widths.indexOf(currentWidth);

        let newWidthIndex = widthIndex + direction;
        let newHeightIndex = heightIndex;

        // Handle wrapping
        if (newWidthIndex >= this.state.widths.length) {
            newWidthIndex = 0;
            newHeightIndex++;
        } else if (newWidthIndex < 0) {
            newWidthIndex = this.state.widths.length - 1;
            newHeightIndex--;
        }

        // Check bounds
        if (newHeightIndex >= 0 && newHeightIndex < this.state.heights.length) {
            const newHeight = this.state.heights[newHeightIndex];
            const newWidth = this.state.widths[newWidthIndex];
            const newKey = `${newHeight}_${newWidth}`;

            this.startCellEdit(newHeight, newWidth, newKey);
        }
    }

    fillSampleData() {
        const sampleData = {};
        const baseValue = 150;

        this.state.heights.forEach((height, i) => {
            this.state.widths.forEach((width, j) => {
                const key = `${height}_${width}`;
                const heightVal = parseInt(height);
                const widthVal = parseInt(width);
                const value = baseValue + (i * 25) + (j * 20) + (heightVal * widthVal * 0.0001);
                sampleData[key] = Math.round(value * 100) / 100;
            });
        });

        this.state.matrixData = sampleData;
        this.updateRecordField();
        this.renderMatrix();
        this.calculateStats();

        // No notification - silent sample data generation
    }

    clearAllData() {
        this.state.matrixData = {};
        this.updateRecordField();
        this.renderMatrix();
        this.calculateStats();

        // No notification - silent clear
    }

    adjustContainerWidth() {
        // Adjust container width based on available space
        setTimeout(() => {
            const container = this.matrixRef.el?.parentElement?.parentElement;
            const table = this.matrixRef.el;

            if (container && table) {
                // Calculate optimal container width based on available space
                const calculateOptimalWidth = () => {
                    // Find the form container or sheet element
                    const formContainer = container.closest('.o_form_sheet') ||
                        container.closest('.o_form_view') ||
                        container.closest('.o_form_editable');

                    if (formContainer) {
                        const formWidth = formContainer.clientWidth;
                        // Use 95% of form width, but not less than 600px
                        return Math.max(600, formWidth * 0.95);
                    }

                    // Fallback to viewport-based calculation
                    const parentContainer = container.parentElement;
                    const parentWidth = parentContainer ? parentContainer.clientWidth : 0;
                    return Math.min(parentWidth * 0.9, window.innerWidth * 0.9);
                };

                const tableWidth = table.scrollWidth;
                const maxAllowedWidth = calculateOptimalWidth();

                if (tableWidth > maxAllowedWidth) {
                    container.style.maxWidth = `${maxAllowedWidth}px`;
                } else {
                    // Reset to CSS value if table fits
                    container.style.maxWidth = '';
                }
            }
        }, 1000);
    }

    setupResizeHandler() {
        // Handle window resize to recalculate container width
        this.resizeHandler = () => {
            // Debounce resize events
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                this.adjustContainerWidth();
            }, 250);
        };

        window.addEventListener('resize', this.resizeHandler);
    }

    cleanupResizeHandler() {
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
    }

}

// Register the component for price matrix
registry.category("fields").add("price_matrix_visual", {
    component: PriceMatrixWidget,
});
