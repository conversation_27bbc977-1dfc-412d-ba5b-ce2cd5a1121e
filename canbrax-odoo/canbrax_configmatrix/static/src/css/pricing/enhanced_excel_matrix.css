/* Interactive Matrix Editor Styling */
.interactive-matrix-editor .matrix-toolbar {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 12px 16px;
        margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.interactive-matrix-editor .toolbar-actions .btn {
    margin-right: 8px;
    border: none;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.interactive-matrix-editor .toolbar-actions .btn-info {
    background-color: #17a2b8 !important;
    /* Solid teal */
    color: white !important;
}

.interactive-matrix-editor .toolbar-actions .btn-info:hover {
    background-color: #138496 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.interactive-matrix-editor .toolbar-actions .btn-warning {
    background-color: #ffc107 !important;
    /* Solid yellow */
    color: #212529 !important;
}

.interactive-matrix-editor .toolbar-actions .btn-warning:hover {
    background-color: #e0a800 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.interactive-matrix-editor .matrix-stats {
    color: #495057 !important;
    /* Darker text for better readability on light background */
}

.interactive-matrix-editor .matrix-stats .badge {
    background-color: #6c757d !important;
    color: white !important;
    font-size: 11px;
    padding: 4px 8px;
    margin-left: 6px;
    border-radius: 12px;
    font-weight: 500;
}

.interactive-matrix-editor .matrix-stats .badge-primary {
    background-color: #007bff !important;
}

.interactive-matrix-editor .matrix-stats .badge-success {
    background-color: #28a745 !important;
}

.interactive-matrix-editor .matrix-stats .badge-info {
    background-color: #17a2b8 !important;
}

/* Price Matrix Form Specific Styles */
.o_field_widget[data-field-name="matrix_data"] {
    max-width: 100%;
    overflow: hidden;
    width: 100%;
}

.o_field_widget[data-field-name="matrix_data"] .interactive-matrix-editor,
.o_field_widget[data-field-name="matrix_data"] .matrix-container {
    max-width: 100%;
    width: 100%;
}

.o_field_widget[data-field-name="matrix_data"] .matrix-scroll-wrapper {
    max-width: min(100%, 90vw);
    width: 100%;
    overflow: auto;
}

/* Interactive Matrix Editor Scrolling Styles */
.interactive-matrix-editor {
    max-width: 100%;
    overflow: hidden;
}

.interactive-matrix-editor .matrix-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 100%;
}

.interactive-matrix-editor .matrix-scroll-wrapper {
    position: relative;
    overflow: auto;
        max-height: 500px;
        max-width: 100%;
        width: 100%;
        min-width: 0;
        box-sizing: border-box;
        flex-shrink: 0;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e0 #f7fafc;
    }

.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 4px;
}

.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

.interactive-matrix-editor .matrix-table {
    margin: 0;
    font-size: 12px;
    border-collapse: separate;
        border-spacing: 0;
        min-width: max-content;
        width: max-content;
        table-layout: auto;
}

/* Sticky positioning for headers in interactive matrix */
.interactive-matrix-editor .matrix-corner-cell,
.interactive-matrix-editor .matrix-header-cell,
.interactive-matrix-editor .matrix-row-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    text-align: center !important;
    font-weight: 600 !important;
    border: 1px solid #dee2e6 !important;
    color: #495057 !important;
}

.interactive-matrix-editor .matrix-corner-cell {
    position: sticky !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 15 !important;
    min-width: 80px !important;
    font-weight: bold !important;
}

.interactive-matrix-editor .matrix-header-cell {
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    min-width: 90px !important;
    height: 30px !important;
}

.interactive-matrix-editor .matrix-row-header {
    position: sticky !important;
    left: 0 !important;
    z-index: 5 !important;
    border-right: 2px solid #adb5bd !important;
}

/* Data cell styling for interactive matrix */
.interactive-matrix-editor .matrix-data-cell {
    text-align: center;
    cursor: pointer;
    padding: 8px;
    min-width: 90px;
    border: 1px solid #d1d5db;
    transition: all 0.15s ease;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
}

.interactive-matrix-editor .matrix-data-cell:hover {
    background-color: #f8fafc !important;
    border-color: #3b82f6 !important;
    box-shadow: inset 0 0 0 1px #3b82f6 !important;
}

.interactive-matrix-editor .matrix-data-cell.selected {
    background-color: #dbeafe !important;
    border-color: #2563eb !important;
    box-shadow: 0 0 0 1px #2563eb !important;
}

.interactive-matrix-editor .matrix-data-cell.editing {
    padding: 0;
}

.interactive-matrix-editor .matrix-data-cell.has-value {
    font-weight: 500;
    color: #374151;
}

.interactive-matrix-editor .matrix-data-cell.empty-value {
    color: #9ca3af;
    font-style: italic;
}

/* Input styling for editing in interactive matrix */
.interactive-matrix-editor .matrix-cell-input {
    width: 100%;
    border: none;
    outline: none;
        text-align: center;
        background: #ffffff;
    font-weight: 500;
    color: #1f2937;
        font-size: 13px;
        padding: 8px;
        box-shadow: inset 0 0 0 2px #059669;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Help text styling for interactive matrix */
.interactive-matrix-editor .matrix-help {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 0.75rem;
    margin: 1rem -1rem -1rem -1rem;
}

/* Loading state for interactive matrix */
.interactive-matrix-editor .matrix-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
    flex-direction: column;
}

.interactive-matrix-editor .matrix-loading .spinner-border {
    width: 2rem;
    height: 2rem;
    margin-right: 0.5rem;
}

/* Responsive adjustments for interactive matrix */
@media (max-width: 768px) {
    .interactive-matrix-editor .matrix-scroll-wrapper {
        max-height: 300px;
    }
    .interactive-matrix-editor .matrix-data-cell {
                                    min-width: 70px;
                                    padding: 6px;
                                    font-size: 11px;
                                }

        .interactive-matrix-editor .matrix-corner-cell,
                                .interactive-matrix-editor .matrix-row-header {
                                    min-width: 60px;
                                    font-size: 11px;
                                }

                                .interactive-matrix-editor .matrix-header-cell {
                                    min-width: 70px;
                                    font-size: 11px;
                                }
}