/* Excel-style Cut Matrix Widget - v2.1 FORCE RELOAD */
.excel-matrix-editor {
    padding: 15px;
    background: #fff;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.matrix-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.toolbar-left {
    display: flex;
    gap: 8px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.matrix-stats {
    display: flex;
    gap: 6px;
}

.excel-matrix-container {
    border: 2px solid #c0c0c0;
    border-radius: 2px;
    background: #fff;
    overflow: auto;
    max-height: 70vh;
}

.excel-grid-wrapper {
    display: flex;
    flex-direction: column;
    min-width: fit-content;
}

/* Column Headers (A, B, C, etc.) */
.excel-column-headers {
    display: flex;
    background: #f0f0f0;
    border-bottom: 1px solid #c0c0c0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.excel-corner-cell {
    width: 60px;
    height: 40px;
    background: #e0e0e0;
    border-right: 1px solid #c0c0c0;
    border-bottom: 1px solid #c0c0c0;
}

.excel-column-header {
    width: 80px;
    height: 40px;
    background: #f0f0f0;
    border-right: 1px solid #c0c0c0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    color: #333;
}

.column-letter {
    font-size: 12px;
    font-weight: bold;
    color: #0066cc;
}

.column-value {
    font-size: 10px;
    color: #666;
}

/* Matrix Rows */
.excel-matrix-rows {
    display: flex;
    flex-direction: column;
}

.excel-matrix-row {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
}

.excel-row-header {
    width: 60px;
    height: 30px;
    background: #f0f0f0;
    border-right: 1px solid #c0c0c0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    color: #333;
    position: sticky;
    left: 0;
    z-index: 5;
}

.row-number {
    font-size: 12px;
    font-weight: bold;
    color: #0066cc;
}

.row-value {
    font-size: 10px;
    color: #666;
}

/* Matrix Cells - Excel Style */
.excel-matrix-cell {
    width: 80px;
    height: 30px;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    background: #fff;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.1s ease;
}

.excel-matrix-cell:hover {
    background: #e6f3ff;
    border-color: #0066cc;
    z-index: 2;
}

.excel-matrix-cell.selected {
    background: #cce7ff;
    border: 2px solid #0066cc;
    z-index: 3;
}

/* Cell Content Styling - FORCE OVERRIDE */
.excel-cell-content {
    font-size: 14px !important;
    font-weight: 700 !important;
    text-align: center !important;
    line-height: 1.1 !important;
    padding: 2px !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-direction: column !important;
    color: #000 !important;
    overflow: hidden !important;
    border-radius: 2px !important;
}

/* Cut Plan Reference - FORCE OVERRIDE */
.cut-plan-ref {
    font-size: 9px !important;
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 1px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
    display: block !important;
}

/* Cut Plan Arrows - FORCE OVERRIDE */
.cut-plan-arrow {
    font-size: 12px !important;
    font-weight: bold !important;
    color: #666 !important;
    margin: 0 1px !important;
    display: inline-block !important;
}

.arrow-right {
    color: #007bff !important;
}

.arrow-down {
    color: #28a745 !important;
}

/* Offcut Indicator - FORCE OVERRIDE */
.offcut-indicator {
    font-size: 8px !important;
    font-weight: 500 !important;
    color: #666 !important;
    margin-top: 1px !important;
    display: block !important;
}

/* Cut Plan Cell Styles - Match Excel Colors */
.excel-matrix-cell.has-cut-plan {
    background: #fff2cc !important;
    border-color: #d6b656 !important;
}

.excel-matrix-cell.has-cut-plan:hover {
    background: #ffeb9c !important;
}

.excel-matrix-cell.has-cut-plan .excel-cell-content {
    color: #7f6000 !important;
    font-weight: 600 !important;
}

/* Specific Cut Plan Colors - Clean and Readable */
.excel-matrix-cell.cut-plan-1, .matrix-cell.cut-plan-1 {
    background: #fff2cc !important;
    color: #333 !important;
    border: 1px solid #d6b656 !important;
} /* Light Yellow */

.excel-matrix-cell.cut-plan-2, .matrix-cell.cut-plan-2 {
    background: #d4edda !important;
    color: #333 !important;
    border: 1px solid #28a745 !important;
} /* Light Green */

.excel-matrix-cell.cut-plan-3, .matrix-cell.cut-plan-3 {
    background: #f8d7da !important;
    color: #333 !important;
    border: 1px solid #dc3545 !important;
} /* Light Red */

.excel-matrix-cell.cut-plan-4, .matrix-cell.cut-plan-4 {
    background: #d1ecf1 !important;
    color: #333 !important;
    border: 1px solid #17a2b8 !important;
} /* Light Blue */

.excel-matrix-cell.cut-plan-5, .matrix-cell.cut-plan-5 {
    background: #e2e3f3 !important;
    color: #333 !important;
    border: 1px solid #6f42c1 !important;
} /* Light Purple */

.excel-matrix-cell.cut-plan-6, .matrix-cell.cut-plan-6 {
    background: #ffeaa7 !important;
    color: #333 !important;
    border: 1px solid #fd7e14 !important;
} /* Light Orange */

/* Arrow Direction Indicators */
.matrix-cell.arrow-right, .excel-matrix-cell.arrow-right {
    background: #e3f2fd !important;
    color: #1976d2 !important;
    border: 2px solid #1976d2 !important;
    font-weight: bold !important;
}

.matrix-cell.arrow-down, .excel-matrix-cell.arrow-down {
    background: #e8f5e8 !important;
    color: #2e7d32 !important;
    border: 2px solid #2e7d32 !important;
    font-weight: bold !important;
}

/* New Cell Content Styling */
.cell-arrow {
    font-size: 18px;
    font-weight: bold;
    color: #000;
    line-height: 1;
    margin: 2px 0;
}

.cut-plan-info {
    font-size: 8px;
    font-weight: bold;
    color: #333;
    margin: 1px 0;
}

.efficiency {
    font-size: 8px;
    color: #666;
    margin: 1px 0;
}

.master-size {
    font-size: 7px;
    color: #888;
    margin: 1px 0;
}

/* Shortened Dimensions - Make them prominent */
.primary-dimensions {
    font-size: 13px !important;
    font-weight: bold !important;
    color: #fff !important;
    margin: 0 !important;
    background: #007bff !important;
    padding: 3px 6px !important;
    border-radius: 4px !important;
    text-align: center !important;
    text-shadow: none !important;
    box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3) !important;
    width: 100% !important;
    display: block !important;
}

.offcut-label {
    font-size: 8px;
    color: #999;
    font-style: italic;
}

/* Arrow Options in Modal */
.arrow-options {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.arrow-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    background: #f8f9fa;
}

.arrow-option:hover {
    border-color: #007bff;
    background: #e3f2fd;
    transform: translateY(-2px);
}

.arrow-icon {
    font-size: 24px;
    font-weight: bold;
    margin-right: 12px;
    color: #007bff;
}

.arrow-label {
    display: flex;
    flex-direction: column;
}

.arrow-label strong {
    color: #333;
    margin-bottom: 2px;
}

.arrow-label small {
    color: #666;
    font-size: 11px;
}

/* Arrow Styles */
.cut-plan-arrow {
    font-size: 12px;
    font-weight: bold;
    margin: 0 1px;
}

/* Suggested Master Size */
.suggested-master {
    font-size: 10px !important;
    color: #666 !important;
    text-align: center !important;
    line-height: 1.2 !important;
    padding: 1px !important;
    background-color: rgba(255, 255, 255, 0.8) !important;
    border-radius: 2px !important;
    display: block !important;
    margin: 1px 0 !important;
}

.arrow-right { color: #0066cc; }
.arrow-down { color: #009900; }
.arrow-up { color: #cc6600; }
.arrow-left { color: #cc0066; }

/* Cut Plan Reference */
.cut-plan-ref {
    font-size: 8px;
    font-weight: bold;
    color: #333;
    background: rgba(255, 255, 255, 0.8);
    padding: 0 2px;
    border-radius: 2px;
    margin: 1px 0;
}

/* Offcut Indicators */
.offcut-indicator {
    font-size: 7px;
    color: #666;
    font-style: italic;
}



/* Legend */
.matrix-legend {
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.matrix-legend h6 {
    color: #495057;
    margin-bottom: 12px;
    font-weight: 600;
}

.matrix-legend ul {
    margin-bottom: 0;
}

.matrix-legend li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.legend-color {
    display: inline-block;
    width: 20px;
    height: 16px;
    border-radius: 3px;
    margin-right: 8px;
    border: 1px solid #dee2e6;
}

/* Cut Plan Details Panel */
.cut-plan-details .card {
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.cut-plan-details .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 12px 16px;
}

.cut-plan-details .card-header h6 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.cut-plan-details .card-body {
    padding: 16px;
}

.cut-plan-details p {
    margin-bottom: 8px;
    font-size: 14px;
}

.cut-plan-details ol,
.cut-plan-details ul {
    margin-bottom: 12px;
    padding-left: 20px;
}

.cut-plan-details li {
    margin-bottom: 4px;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cut-matrix-editor {
        padding: 12px;
    }
    
    .matrix-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }
    
    .matrix-container {
        max-height: 400px;
    }
    
    .matrix-table {
        font-size: 12px;
    }
    
    .matrix-cell {
        min-height: 50px;
        padding: 6px;
    }
    
    .width-header {
        min-width: 100px;
    }
}

/* Loading and Animation States */
.matrix-cell.loading {
    opacity: 0.6;
    pointer-events: none;
}

.matrix-cell.selected {
    background-color: #fff3cd !important;
    border: 2px solid #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.25);
}

/* Button Styling */
.cut-matrix-editor .btn {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.cut-matrix-editor .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* SVG Visualization Styles */
.svg-visualization {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #dee2e6;
}

.svg-visualization svg {
    width: 100%;
    height: auto;
    max-height: 300px;
}

.svg-visualization text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Cut plan details responsive layout */
@media (max-width: 992px) {
    .cut-plan-details .row {
        flex-direction: column;
    }

    .cut-plan-details .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Cut Matrix Modal Dialog Styles - Scoped to avoid conflicts */
.cut-matrix-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
}

.cut-matrix-modal-dialog {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 700px;
    width: 95%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.cut-matrix-modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cut-matrix-modal-header h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.btn-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close:hover {
    color: #495057;
}

.cut-matrix-modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.cut-matrix-modal-footer {
    padding: 16px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cut-matrix-modal-footer .footer-center {
    flex: 1;
    text-align: center;
}

.cut-matrix-modal-footer .selection-info {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.cut-matrix-modal-footer .footer-right {
    display: flex;
    gap: 8px;
}

/* Current Assignment Styles */
.current-assignment .alert {
    margin-bottom: 0;
}

.current-plan-details {
    margin-top: 10px;
}

.current-plan-details > div {
    margin-bottom: 5px;
}

.current-plan-details .badge {
    font-size: 0.8em;
}

.footer-left {
    display: flex;
    gap: 10px;
}

.footer-right {
    display: flex;
    gap: 10px;
}

/* Cut Plan List Styles */
.cut-plan-list {
    max-height: 300px;
    overflow-y: auto;
}

.cut-plan-option {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.cut-plan-option:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.cut-plan-option.selected {
    border-color: #28a745;
    background-color: #e8f5e9;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.cut-plan-option:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cut-plan-option .plan-checkbox {
    flex-shrink: 0;
    margin-top: 2px;
}

.cut-plan-option .plan-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #28a745;
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.plan-name {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #495057;
}

.plan-name i {
    color: #6c757d;
}

.plan-details {
    color: #6c757d;
    font-size: 13px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.detail-row span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.detail-row i {
    width: 14px;
    color: #6c757d;
}

.efficiency-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.cell-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border-left: 4px solid #007bff;
}

.cell-info h6 {
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cell-info p {
    margin-bottom: 4px;
    font-size: 14px;
}

/* Cut Plan List Styles */
.cut-plan-list {
    max-height: 300px;
    overflow-y: auto;
}

.cut-plan-option {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
}

.cut-plan-option:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.cut-plan-option:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.plan-name {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #495057;
}

.plan-name i {
    color: #6c757d;
}

.plan-details {
    color: #6c757d;
    font-size: 13px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.detail-row span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.detail-row i {
    width: 14px;
    color: #6c757d;
}

.efficiency-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.cell-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border-left: 4px solid #007bff;
}

.cell-info h6 {
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cell-info p {
    margin-bottom: 4px;
    font-size: 14px;
}

/* Multiple Cut Plans in Cell */
.cut-plan-info.plan-1 {
    color: #007bff;
    font-weight: 600;
}

.cut-plan-info.plan-2 {
    color: #28a745;
    font-weight: 600;
}

.efficiency.plan-1 {
    color: #007bff;
    font-size: 9px;
}

.efficiency.plan-2 {
    color: #28a745;
    font-size: 9px;
}

/* Cut to Size Styling */
.cut-to-size {
    background-color: #fff3cd !important;
    border-color: #ffeaa7 !important;
    color: #856404 !important;
}

.cut-to-size-info {
    color: #856404;
    font-weight: 600;
    font-size: 10px;
}

.cut-to-size-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.cut-to-size-option {
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff8e1;
}

.cut-to-size-option:hover {
    background-color: #fff3cd;
    border-color: #ffb300;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
}

.cut-to-size-option .plan-description {
    font-size: 14px;
    color: #6c757d;
    margin-top: 4px;
}
