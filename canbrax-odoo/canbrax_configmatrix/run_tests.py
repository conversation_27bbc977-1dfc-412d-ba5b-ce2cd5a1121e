#!/usr/bin/env python3
"""
ConfigMatrix Test Runner

A simple script to run ConfigMatrix tests with various options.
This script provides an easy way to execute tests without remembering complex Odoo commands.

Usage:
    python run_tests.py                    # Run all tests
    python run_tests.py --unit            # Run unit tests only
    python run_tests.py --integration     # Run integration tests only
    python run_tests.py --performance     # Run performance tests only
    python run_tests.py --file test_file  # Run specific test file
    python run_tests.py --help            # Show help
"""

import argparse
import os
import subprocess
import sys
import time
from pathlib import Path

class ConfigMatrixTestRunner:
    """Test runner for ConfigMatrix module"""
    
    def __init__(self):
        self.odoo_path = self._find_odoo()
        self.test_db = "canbrax_test"
        self.module_path = "canbrax_configmatrix"
        self.test_results = []
        
    def _find_odoo(self):
        """Find Odoo executable"""
        # Use the known virtual environment and Odoo paths
        venv_python = "/home/<USER>/.local/share/virtualenvs/canbrax18/bin/python"
        odoo_bin = "/home/<USER>/odoo/itms/canbrax18/odoo/odoo-bin"
        
        # Check if virtual environment exists
        if os.path.exists(venv_python):
            return f"{venv_python} {odoo_bin}"
        
        # Fallback to common Odoo paths
        possible_paths = [
            "odoo",
            "python3 -m odoo",
            "/usr/bin/odoo",
            "/usr/local/bin/odoo",
            "./odoo-bin",
        ]
        
        for path in possible_paths:
            try:
                if path.startswith("python"):
                    # Test if python module is available
                    result = subprocess.run(
                        ["python3", "-c", "import odoo; print('odoo found')"],
                        capture_output=True, text=True
                    )
                    if result.returncode == 0:
                        return path
                else:
                    # Test if executable exists
                    result = subprocess.run(
                        [path, "--help"],
                        capture_output=True, text=True
                    )
                    if result.returncode == 0:
                        return path
            except (FileNotFoundError, subprocess.CalledProcessError):
                continue
        
        return "python3 -m odoo"  # Default fallback
    
    def _create_test_config(self):
        """Create test configuration file"""
        # Get the absolute path to the addons directory
        current_dir = os.getcwd()
        addons_path = os.path.join(current_dir, "..")
        
        config_content = f"""[options]
addons_path = {addons_path}
db_host = localhost
db_port = 5432
db_user = {os.getenv('DB_USER', 'postgres')}
db_password = {os.getenv('DB_PASSWORD', '')}
db_name = {self.test_db}
test_enable = True
test_tags = canbrax
log_level = test
"""
        
        config_file = "test.conf"
        with open(config_file, "w") as f:
            f.write(config_content)
        
        return config_file
    
    def _check_database(self):
        """Check if test database exists and is accessible"""
        try:
            # Try to connect to database
            result = subprocess.run(
                ["psql", "-h", "localhost", "-U", os.getenv('DB_USER', 'postgres'), 
                 "-d", self.test_db, "-c", "SELECT 1"],
                capture_output=True, text=True
            )
            return result.returncode == 0
        except FileNotFoundError:
            print("Warning: psql not found. Database check skipped.")
            return True
    
    def _create_database(self):
        """Create test database if it doesn't exist"""
        try:
            result = subprocess.run(
                ["createdb", "-h", "localhost", "-U", os.getenv('DB_USER', 'postgres'), self.test_db],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✓ Test database '{self.test_db}' created successfully")
                return True
            else:
                print(f"✗ Failed to create database: {result.stderr}")
                return False
        except FileNotFoundError:
            print("Warning: createdb not found. Please create database manually.")
            return False
    
    def _run_tests(self, test_type=None, test_file=None, verbose=False):
        """Run tests with specified options"""
        # Create test configuration
        config_file = self._create_test_config()
        
        # Get Odoo command
        odoo_cmd = self._find_odoo()
        
        # Build command based on Odoo command format
        if odoo_cmd.startswith("/home/<USER>/.local/share/virtualenvs/canbrax18/bin/python"):
            # Use virtual environment with odoo-bin
            cmd = [
                "/home/<USER>/.local/share/virtualenvs/canbrax18/bin/python",
                "/home/<USER>/odoo/itms/canbrax18/odoo/odoo-bin",
                "-c", config_file,
                "-d", self.test_db,
                "--test-enable",
                "--stop-after-init"
            ]
        else:
            # Use standard python -m odoo
            cmd = [
                "python3", "-m", "odoo",
                "-d", self.test_db,
                "--test-enable",
                "--stop-after-init",
                "--config", config_file
            ]
        
        # Add test tags based on type
        if test_type == "unit":
            cmd.extend(["--test-tags", "TestConfigMatrix"])
        elif test_type == "integration":
            cmd.extend(["--test-tags", "TestIntegration"])
        elif test_type == "performance":
            cmd.extend(["--test-tags", "TestPerformance"])
        elif test_file:
            cmd.extend(["--test-tags", test_file])
        else:
            cmd.extend(["--test-tags", "canbrax"])
        
        # Add verbose logging if requested
        if verbose:
            cmd.extend(["--log-level", "test"])
        
        print(f"Running tests with command: {' '.join(cmd)}")
        print(f"Test database: {self.test_db}")
        print(f"Module path: {self.module_path}")
        print("-" * 60)
        
        # Run tests
        start_time = time.time()
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            end_time = time.time()
            
            # Process results
            self._process_test_results(result, end_time - start_time)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"✗ Error running tests: {e}")
            return False
        finally:
            # Clean up config file
            if os.path.exists(config_file):
                os.remove(config_file)
    
    def _process_test_results(self, result, duration):
        """Process and display test results"""
        print(f"\nTest execution completed in {duration:.2f} seconds")
        print("=" * 60)
        
        if result.returncode == 0:
            print("✓ All tests passed successfully!")
        else:
            print("✗ Some tests failed!")
        
        # Display output
        if result.stdout:
            print("\nSTDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        # Parse test results if possible
        self._parse_test_output(result.stdout)
    
    def _parse_test_output(self, output):
        """Parse test output to extract results"""
        if not output:
            return
        
        lines = output.split('\n')
        test_count = 0
        passed = 0
        failed = 0
        
        for line in lines:
            if "test_" in line and "PASSED" in line:
                passed += 1
                test_count += 1
            elif "test_" in line and "FAILED" in line:
                failed += 1
                test_count += 1
            elif "test_" in line and "ERROR" in line:
                failed += 1
                test_count += 1
        
        if test_count > 0:
            print(f"\nTest Summary:")
            print(f"  Total tests: {test_count}")
            print(f"  Passed: {passed}")
            print(f"  Failed: {failed}")
            print(f"  Success rate: {(passed/test_count)*100:.1f}%")
    
    def run_all_tests(self, verbose=False):
        """Run all tests"""
        print("🚀 Running all ConfigMatrix tests...")
        return self._run_tests(verbose=verbose)
    
    def run_unit_tests(self, verbose=False):
        """Run unit tests only"""
        print("🧪 Running ConfigMatrix unit tests...")
        return self._run_tests(test_type="unit", verbose=verbose)
    
    def run_integration_tests(self, verbose=False):
        """Run integration tests only"""
        print("🔗 Running ConfigMatrix integration tests...")
        return self._run_tests(test_type="integration", verbose=verbose)
    
    def run_performance_tests(self, verbose=False):
        """Run performance tests only"""
        print("⚡ Running ConfigMatrix performance tests...")
        return self._run_tests(test_type="performance", verbose=verbose)
    
    def run_specific_file(self, test_file, verbose=False):
        """Run tests from specific file"""
        print(f"📁 Running tests from {test_file}...")
        return self._run_tests(test_file=test_file, verbose=verbose)
    
    def setup_test_environment(self):
        """Set up test environment"""
        print("🔧 Setting up test environment...")
        
        # Check database
        if not self._check_database():
            print(f"Creating test database '{self.test_db}'...")
            if not self._create_database():
                print("✗ Failed to set up test environment")
                return False
        
        print("✓ Test environment ready")
        return True
    
    def cleanup(self):
        """Clean up test environment"""
        print("🧹 Cleaning up test environment...")
        
        # Drop test database
        try:
            result = subprocess.run(
                ["dropdb", "-h", "localhost", "-U", os.getenv('DB_USER', 'postgres'), self.test_db],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✓ Test database '{self.test_db}' dropped")
            else:
                print(f"⚠ Warning: Could not drop test database: {result.stderr}")
        except FileNotFoundError:
            print("⚠ Warning: dropdb not found. Please clean up database manually.")
        
        print("✓ Cleanup completed")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="ConfigMatrix Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run all tests
  python run_tests.py --unit            # Run unit tests only
  python run_tests.py --integration     # Run integration tests only
  python run_tests.py --performance     # Run performance tests only
  python run_tests.py --file test_file  # Run specific test file
  python run_tests.py --verbose         # Run with verbose output
  python run_tests.py --setup           # Set up test environment only
  python run_tests.py --cleanup         # Clean up test environment only
        """
    )
    
    parser.add_argument(
        "--unit", 
        action="store_true", 
        help="Run unit tests only"
    )
    
    parser.add_argument(
        "--integration", 
        action="store_true", 
        help="Run integration tests only"
    )
    
    parser.add_argument(
        "--performance", 
        action="store_true", 
        help="Run performance tests only"
    )
    
    parser.add_argument(
        "--file", 
        type=str, 
        help="Run tests from specific file"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Run with verbose output"
    )
    
    parser.add_argument(
        "--setup", 
        action="store_true", 
        help="Set up test environment only"
    )
    
    parser.add_argument(
        "--cleanup", 
        action="store_true", 
        help="Clean up test environment only"
    )
    
    parser.add_argument(
        "--all", 
        action="store_true", 
        help="Run all tests (default)"
    )
    
    args = parser.parse_args()
    
    # Create test runner
    runner = ConfigMatrixTestRunner()
    
    try:
        # Handle setup/cleanup only
        if args.setup:
            success = runner.setup_test_environment()
            sys.exit(0 if success else 1)
        
        if args.cleanup:
            runner.cleanup()
            sys.exit(0)
        
        # Set up environment
        if not runner.setup_test_environment():
            print("✗ Failed to set up test environment")
            sys.exit(1)
        
        # Run tests based on arguments
        success = False
        
        if args.unit:
            success = runner.run_unit_tests(verbose=args.verbose)
        elif args.integration:
            success = runner.run_integration_tests(verbose=args.verbose)
        elif args.performance:
            success = runner.run_performance_tests(verbose=args.verbose)
        elif args.file:
            success = runner.run_specific_file(args.file, verbose=args.verbose)
        else:
            # Default: run all tests
            success = runner.run_all_tests(verbose=args.verbose)
        
        # Exit with appropriate code
        if success:
            print("\n🎉 All tests completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠ Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
    finally:
        # Clean up
        if not args.setup and not args.cleanup:
            runner.cleanup()

if __name__ == "__main__":
    main()
