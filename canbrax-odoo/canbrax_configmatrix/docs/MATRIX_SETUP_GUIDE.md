# Matrix Setup Guide: Width vs Height Clarification

## Understanding the Matrix Coordinate System

### Key Concepts:
- **WIDTH = HORIZONTAL axis (columns)** - Left to right
- **HEIGHT = VERTICAL axis (rows)** - Top to bottom
- **Matrix cell key format**: `"HEIGHT_WIDTH"` (e.g., `"2400_1200"`)

### Your Case: 1200x2400mm Mesh
- **Required dimensions**: 1200mm wide × 2400mm high
- **Matrix cell key**: `"2400_1200"` (height_width format)
- **Matrix coordinates**: Column for width=1200, Row for height=2400

## Matrix Configuration

### Default Configuration (BEFORE fix):
```
Heights: 600,700,800,900,1000,1100,1200,1300,1400,1500  (max: 1500)
Widths:  325,375,425,475,525,575,625,675,725,775,825,875,925,975  (max: 975)
```

**Problem**: Your dimensions (1200×2400) don't fit because:
- Width 1200 > max width 975 ❌
- Height 2400 > max height 1500 ❌

### Updated Configuration (AFTER fix):
```
Heights: 600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800,1900,2000,2100,2200,2300,2400,2500,2600
Widths:  325,375,425,475,525,575,625,675,725,775,825,875,925,975,1000,1100,1200,1300,1400,1500
```

**Solution**: Now includes:
- Width 1200 ✓
- Height 2400 ✓

## Setting Up Cut Plans

### Step 1: Create Matrix (Done automatically)
The BasiX matrix is created with ID `mesh_cut_matrix_basix`

### Step 2: Create Cut Plans
Two cut plans are created:
1. **1200×2400 Standard Cut** - For your exact master sheet size
2. **975×1500 Efficient Cut** - For smaller cuts

### Step 3: Assign Cut Plans to Matrix Cells
The matrix cell `"2400_1200"` should be linked to the 1200×2400 cut plan.

## Expected Results After Update

### When you hunt for 1200×2400mm mesh:

#### Matrix Lookup Process:
1. **Required**: 1200×2400mm (W×H)
2. **Matrix cell lookup**: `"2400_1200"`
3. **Cut plan found**: "BasiX 1200x2400 Standard Cut"
4. **Master sheet**: 1200×2400mm BasiX master sheet
5. **Matrix info populated**:
   - Matrix Cell Height: 2400
   - Matrix Cell Width: 1200
   - Matrix Cell Reference: Computed (e.g., "P1" for column P, row 1)
   - Cut Plan: "BasiX 1200x2400 Standard Cut"

#### Hunt Log Should Show:
```
[HUNT] Attempting matrix lookup for 1200x2400mm (W×H) in basix series
[HUNT] Matrix 'BasiX Series Cutting Matrix' configured with:
[HUNT]   Heights (vertical): 600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800,1900,2000,2100,2200,2300,2400...
[HUNT]   Widths (horizontal): 325,375,425,475,525,575,625,675,725,775,825,875,925,975,1000,1100,1200,1300,1400,1500
[HUNT]   Cut plans available: 2
[HUNT] ✓ Found matrix cut plan: BasiX 1200x2400 Standard Cut
[HUNT] Matrix cell: 1200x2400mm (W×H)
[HUNT] Master sheet: 1200 X 2400 Basix 0.71mm 316 Marine Grade Stainless Steel Mesheet Products
[HUNT] Efficiency: 100.0%
```

## Troubleshooting

### If Matrix Lookup Still Fails:
1. **Check matrix exists**: Go to Mesh Hunt > Mesh Cut Matrices > BasiX
2. **Verify dimensions**: Ensure height_values includes 2400 and width_values includes 1200
3. **Check cut plans**: Ensure cut plans exist and are linked to matrix
4. **Verify matrix data**: The matrix_data JSON should include `"2400_1200"` key

### Manual Matrix Cell Assignment:
If automatic assignment doesn't work, manually assign:
1. Open BasiX matrix
2. Go to Visual Matrix tab
3. Click on cell at Height=2400, Width=1200
4. Assign the "BasiX 1200x2400 Standard Cut" plan

## Visual Matrix Layout

```
        325  375  425  ...  975  1000 1100 1200 1300 1400 1500
   600   A1   B1   C1   ...  N1   O1   P1   Q1   R1   S1   T1
   700   A2   B2   C2   ...  N2   O2   P2   Q2   R2   S2   T2
   ...   ...  ...  ...  ...  ...  ...  ...  ...  ...  ...  ...
  2400   A21  B21  C21  ...  N21  O21  P21  Q21  R21  S21  T21
```

Your 1200×2400 mesh maps to cell **Q21** (Column Q = width 1200, Row 21 = height 2400)
