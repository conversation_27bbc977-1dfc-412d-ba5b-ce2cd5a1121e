# Mesh Panel Individual Hunting Implementation

## Overview
Complete implementation to calculate individual mesh panel dimensions based on door configuration and midrail position, with separate hunting for each panel to optimize material usage from different sources (unplanned offcuts, planned offcuts, master sheets).

## Problem Statement
Current mesh system treats all panels as identical, but midrail creates different sized panels. For example:
- Door: 1000mm high × 500mm wide with midrail at 600mm
- Results in: Panel 1 (600mm × 500mm) and Panel 2 (400mm × 500mm)
- Each panel should be hunted individually for optimal material usage

## Solution Architecture
1. **Panel Dimension Calculation**: Calculate exact dimensions for each panel
2. **Individual Hunting**: Hunt each panel separately through mesh sources
3. **Source Optimization**: Priority order: unplanned → planned → master
4. **Operation Tracking**: Create separate operations for each panel

---

## Implementation Tasks

### Task 1: Enhance Template Model
**File**: `canbrax_configmatrix/models/config_matrix_template.py`

```python
# Add these fields after existing mesh fields
mesh_panel_calculation_method = fields.Selection([
    ('simple_count', 'Simple Panel Count'),
    ('midrail_split', 'Midrail Split Calculation'),
    ('custom_formula', 'Custom Formula')
], string="Panel Calculation Method", default='midrail_split',
   help="Method to calculate mesh panel dimensions")

mesh_panel_count_formula = fields.Char(
    "Panel Count Formula", 
    default="1",
    help="Simple count formula when method is 'simple_count'"
)

mesh_panel_dimensions_formula = fields.Text(
    "Panel Dimensions Formula",
    help="Python code to return list of panel dimensions when method is 'custom_formula'.\n"
         "Return format: [{'width': 500, 'height': 600, 'name': 'Top Panel'}, {'width': 500, 'height': 400, 'name': 'Bottom Panel'}]\n"
         "Available variables: door_width, door_height, midrail_position, is_double_door, config_values"
)

hunt_each_panel_separately = fields.Boolean(
    "Hunt Each Panel Separately",
    default=True,
    help="When enabled, each mesh panel will be hunted individually, allowing different sources for each panel"
)

mesh_hunting_priority = fields.Selection([
    ('unplanned_planned_master', 'Unplanned → Planned → Master'),
    ('planned_unplanned_master', 'Planned → Unplanned → Master'),
    ('master_only', 'Master Sheets Only'),
    ('offcuts_only', 'Offcuts Only (Unplanned + Planned)')
], string="Mesh Hunting Priority", default='unplanned_planned_master',
   help="Priority order for hunting mesh panels")
```

### Task 2: Update Template Form View
**File**: `canbrax_configmatrix/views/config_matrix_template_views.xml`

```xml
<!-- Add after mesh_series field in the form view -->
<group string="Panel Calculation" col="2">
    <field name="mesh_panel_calculation_method" 
           invisible="mesh_required == False"/>
    <field name="hunt_each_panel_separately" 
           invisible="mesh_required == False"/>
    <field name="mesh_hunting_priority" 
           invisible="mesh_required == False or hunt_each_panel_separately == False"/>
</group>

<group string="Panel Formulas" col="1">
    <field name="mesh_panel_count_formula" 
           invisible="mesh_required == False or mesh_panel_calculation_method != 'simple_count'"
           placeholder="e.g., 2 if door_type == 'double' else 1"/>
    <field name="mesh_panel_dimensions_formula" 
           invisible="mesh_required == False or mesh_panel_calculation_method != 'custom_formula'"
           widget="ace" options="{'mode': 'python'}"
           placeholder="# Return list of panel dimensions&#10;panels = []&#10;if midrail_position > 0:&#10;    panels.append({'width': door_width, 'height': midrail_position, 'name': 'Top Panel'})&#10;    panels.append({'width': door_width, 'height': door_height - midrail_position, 'name': 'Bottom Panel'})&#10;return panels"/>
</group>
```

### Task 3: Enhance Mesh Cut Operation Model
**File**: `canbrax_configmatrix/models/mesh_cut_operation.py`

```python
# Add these fields to existing model
panel_number = fields.Integer("Panel Number", help="Sequential panel number within configuration")
panel_position = fields.Selection([
    ('full', 'Full Panel'),
    ('top', 'Top Panel'),
    ('bottom', 'Bottom Panel'),
    ('left', 'Left Panel'),
    ('right', 'Right Panel'),
    ('custom', 'Custom Position')
], string="Panel Position", help="Position of panel within door")
door_number = fields.Integer("Door Number", default=1, help="Door number for multi-door configurations")
hunting_priority = fields.Selection([
    ('unplanned_planned_master', 'Unplanned → Planned → Master'),
    ('planned_unplanned_master', 'Planned → Unplanned → Master'),
    ('master_only', 'Master Sheets Only'),
    ('offcuts_only', 'Offcuts Only')
], string="Hunting Priority", help="Priority order for this panel")
mesh_source_type = fields.Selection([
    ('unplanned_offcut', 'Unplanned Offcut'),
    ('planned_offcut', 'Planned Offcut'),
    ('master_sheet', 'Master Sheet'),
    ('not_found', 'Not Found')
], string="Mesh Source Type", readonly=True, help="Source type of selected mesh")
selected_mesh_id = fields.Many2one('product.product', string="Selected Mesh", readonly=True)
hunting_notes = fields.Text("Hunting Notes", readonly=True, help="Notes from hunting process")

def hunt_individual_panel(self, width, height, series, priority_order=None):
    """Hunt for mesh for individual panel with priority order"""
    self.ensure_one()
    
    if not priority_order:
        priority_order = self.hunting_priority or 'unplanned_planned_master'
    
    hunting_methods = self._get_hunting_methods(priority_order)
    hunting_log = []
    
    for method_name, method_func in hunting_methods:
        hunting_log.append(f"Trying {method_name}...")
        
        result = method_func(width, height, series)
        if result and result.get('product'):
            # Found mesh
            self.write({
                'selected_mesh_id': result['product'].id,
                'mesh_source_type': result.get('source_type', 'not_found'),
                'hunting_notes': '\n'.join(hunting_log + [f"✓ Found: {result.get('details', 'No details')}"])
            })
            return result
        else:
            hunting_log.append(f"  ✗ {method_name}: {result.get('reason', 'No suitable mesh found')}")
    
    # No mesh found
    self.write({
        'mesh_source_type': 'not_found',
        'hunting_notes': '\n'.join(hunting_log + ["✗ No suitable mesh found in any source"])
    })
    return None

def _get_hunting_methods(self, priority_order):
    """Get hunting methods in priority order"""
    methods = {
        'unplanned_offcuts': ('Unplanned Offcuts', self._hunt_unplanned_offcuts),
        'planned_offcuts': ('Planned Offcuts', self._hunt_planned_offcuts),
        'master_sheets': ('Master Sheets', self._hunt_master_sheets)
    }
    
    if priority_order == 'unplanned_planned_master':
        return [methods['unplanned_offcuts'], methods['planned_offcuts'], methods['master_sheets']]
    elif priority_order == 'planned_unplanned_master':
        return [methods['planned_offcuts'], methods['unplanned_offcuts'], methods['master_sheets']]
    elif priority_order == 'master_only':
        return [methods['master_sheets']]
    elif priority_order == 'offcuts_only':
        return [methods['unplanned_offcuts'], methods['planned_offcuts']]
    else:
        return [methods['unplanned_offcuts'], methods['planned_offcuts'], methods['master_sheets']]

def _hunt_unplanned_offcuts(self, width, height, series):
    """Hunt in unplanned offcuts"""
    # Search for unplanned offcuts that can fit the required dimensions
    domain = [
        ('mesh_series', '=', series),
        ('current_width', '>=', width),
        ('current_height', '>=', height),
        ('state', '=', 'available'),
        ('is_planned_offcut', '=', False),
        ('is_master_sheet', '=', False)
    ]
    
    offcuts = self.env['mesh.offcut'].search(domain, order='current_width asc, current_height asc', limit=1)
    
    if offcuts:
        offcut = offcuts[0]
        return {
            'product': offcut.product_id,
            'source_type': 'unplanned_offcut',
            'source_record': offcut,
            'details': f"Unplanned offcut {offcut.current_width}x{offcut.current_height}mm (ID: {offcut.id})",
            'efficiency': (width * height) / (offcut.current_width * offcut.current_height)
        }
    
    return {'reason': 'No suitable unplanned offcuts found'}

def _hunt_planned_offcuts(self, width, height, series):
    """Hunt in planned offcuts"""
    domain = [
        ('mesh_series', '=', series),
        ('current_width', '>=', width),
        ('current_height', '>=', height),
        ('state', '=', 'available'),
        ('is_planned_offcut', '=', True),
        ('is_master_sheet', '=', False)
    ]
    
    offcuts = self.env['mesh.offcut'].search(domain, order='current_width asc, current_height asc', limit=1)
    
    if offcuts:
        offcut = offcuts[0]
        return {
            'product': offcut.product_id,
            'source_type': 'planned_offcut',
            'source_record': offcut,
            'details': f"Planned offcut {offcut.current_width}x{offcut.current_height}mm (ID: {offcut.id})",
            'efficiency': (width * height) / (offcut.current_width * offcut.current_height)
        }
    
    return {'reason': 'No suitable planned offcuts found'}

def _hunt_master_sheets(self, width, height, series):
    """Hunt in master sheets"""
    # Find best master sheet from cut matrix
    matrix = self.env['mesh.cut.matrix'].search([('mesh_series', '=', series)], limit=1)
    
    if not matrix:
        return {'reason': 'No cut matrix found for series'}
    
    # Find best cut plan for these dimensions
    best_plan = matrix.find_best_cut_plan(width, height)
    
    if best_plan:
        master_product = self.env['product.product'].search([
            ('mesh_series', '=', series),
            ('is_master_sheet', '=', True),
            ('mesh_width', '=', best_plan['master_width']),
            ('mesh_height', '=', best_plan['master_height'])
        ], limit=1)
        
        if master_product:
            return {
                'product': master_product,
                'source_type': 'master_sheet',
                'source_record': best_plan,
                'details': f"Master sheet {best_plan['master_width']}x{best_plan['master_height']}mm (Plan: {best_plan.get('name', 'Unknown')})",
                'efficiency': best_plan.get('efficiency', 0.0)
            }
    
    return {'reason': 'No suitable master sheet cut plan found'}

@api.model
def create_operations_for_configuration(self, configuration_id):
    """Create mesh operations for a configuration with individual panel dimensions"""
    config = self.env['config.matrix.configuration'].browse(configuration_id)
    if not config.exists() or not config.template_id.mesh_required:
        return []
    
    # Get panel dimensions
    config_values = config.get_configuration_values()
    panel_dimensions = config._calculate_mesh_panel_dimensions(config_values)
    
    if not panel_dimensions:
        return []
    
    mesh_series = config_values.get('mesh_series', config.template_id.mesh_series)
    hunting_priority = config.template_id.mesh_hunting_priority
    operations = []
    
    # Create operation for each panel
    for i, panel in enumerate(panel_dimensions):
        operation = self.create({
            'name': panel['name'],
            'required_width': panel['width'],
            'required_height': panel['height'],
            'required_qty': 1.0,
            'mesh_series': mesh_series,
            'state': 'draft',
            'config_id': config.id,
            'panel_number': i + 1,
            'panel_position': panel.get('panel_position', 'full'),
            'door_number': panel.get('door_number', 1),
            'hunting_priority': hunting_priority,
        })
        
        # Hunt for mesh if enabled
        if config.template_id.hunt_each_panel_separately:
            operation.hunt_individual_panel(panel['width'], panel['height'], mesh_series, hunting_priority)
        
        operations.append(operation)
    
    return operations
```

### Task 4: Add Panel Dimension Calculation to Configuration Model
**File**: `canbrax_configmatrix/models/config_matrix_configuration.py`

```python
def _calculate_mesh_panel_dimensions(self, config_values):
    """Calculate dimensions for each mesh panel based on configuration"""
    if not self.template_id.mesh_required:
        return []
    
    method = self.template_id.mesh_panel_calculation_method
    
    if method == 'midrail_split':
        return self._calculate_midrail_split_panels(config_values)
    elif method == 'custom_formula':
        return self._calculate_custom_formula_panels(config_values)
    else:
        return self._calculate_simple_count_panels(config_values)

def _calculate_midrail_split_panels(self, config_values):
    """Calculate panel dimensions for door/midrail combinations"""
    panels = []
    
    # Get door dimensions
    door_width = config_values.get('_CALCULATED_largest_door_width', 0)
    door_height = config_values.get('_CALCULATED_largest_door_height', 0)
    
    if not door_width or not door_height:
        return panels
    
    # Determine if double door
    is_double_door = self._is_double_door(config_values)
    door_count = 2 if is_double_door else 1
    
    # Check for midrail
    has_midrail = (config_values.get('midrail_selected', False) or 
                   config_values.get('has_midrail', False) or
                   config_values.get('midrail_position', 0) > 0)
    
    if has_midrail:
        # Get midrail position
        midrail_position = self._get_midrail_position(config_values, door_height)
        
        # Calculate panel heights
        top_height = midrail_position
        bottom_height = door_height - midrail_position
        
        # Create panels for each door
        for door_num in range(door_count):
            door_label = f"Door {door_num + 1}" if is_double_door else "Door"
            
            # Top panel
            panels.append({
                'width': door_width,
                'height': top_height,
                'door_number': door_num + 1,
                'panel_position': 'top',
                'name': f'{door_label} Top Panel ({door_width}x{int(top_height)}mm)'
            })
            
            # Bottom panel
            panels.append({
                'width': door_width,
                'height': bottom_height,
                'door_number': door_num + 1,
                'panel_position': 'bottom',
                'name': f'{door_label} Bottom Panel ({door_width}x{int(bottom_height)}mm)'
            })
    else:
        # No midrail - single panel per door
        for door_num in range(door_count):
            door_label = f"Door {door_num + 1}" if is_double_door else "Door"
            
            panels.append({
                'width': door_width,
                'height': door_height,
                'door_number': door_num + 1,
                'panel_position': 'full',
                'name': f'{door_label} Full Panel ({door_width}x{door_height}mm)'
            })
    
    return panels

def _get_midrail_position(self, config_values, door_height):
    """Get midrail position from configuration"""
    # Try explicit midrail position first
    midrail_position = config_values.get('midrail_position', 0)
    if midrail_position > 0:
        return midrail_position
    
    # Check for calculated midrail height
    calc_midrail = config_values.get('_CALCULATED_midrail_height', 0)
    if calc_midrail > 0:
        return calc_midrail
    
    # Check for even split
    is_even_split = config_values.get('_CALCULATED_is_even_split', False)
    if is_even_split:
        return door_height / 2
    
    # Default to 60% of door height (common midrail position)
    return door_height * 0.6

def _is_double_door(self, config_values):
    """Check if this is a double door configuration"""
    # Check door type field
    door_type = config_values.get('door_type', '').lower()
    if 'double' in door_type:
        return True
    
    # Check template name
    if 'double' in (self.template_id.technical_name or '').lower():
        return True
    
    # Check other possible fields
    door_config = config_values.get('door_configuration', '').lower()
    if 'double' in door_config:
        return True
    
    # Check template name patterns
    template_name = (self.template_id.name or '').lower()
    if 'double' in template_name:
        return True
    
    return False

def _calculate_custom_formula_panels(self, config_values):
    """Calculate panel dimensions using custom formula"""
    if not self.template_id.mesh_panel_dimensions_formula:
        return self._calculate_simple_count_panels(config_values)
    
    try:
        # Create evaluation context
        context = {
            'door_width': config_values.get('_CALCULATED_largest_door_width', 0),
            'door_height': config_values.get('_CALCULATED_largest_door_height', 0),
            'midrail_position': self._get_midrail_position(config_values, config_values.get('_CALCULATED_largest_door_height', 0)),
            'midrail_selected': config_values.get('midrail_selected', False),
            'is_double_door': self._is_double_door(config_values),
            'is_even_split': config_values.get('_CALCULATED_is_even_split', False),
            'config_values': config_values,
        }
        
        # Execute formula
        result = eval(self.template_id.mesh_panel_dimensions_formula, {"__builtins__": {}}, context)
        
        # Validate and format result
        if isinstance(result, list):
            for i, panel in enumerate(result):
                if not isinstance(panel, dict):
                    continue
                if 'name' not in panel:
                    panel['name'] = f'Panel {i + 1}'
                if 'door_number' not in panel:
                    panel['door_number'] = 1
                if 'panel_position' not in panel:
                    panel['panel_position'] = 'custom'
                # Ensure dimensions are present
                if 'width' not in panel or 'height' not in panel:
                    continue
            return [p for p in result if isinstance(p, dict) and 'width' in p and 'height' in p]
        
    except Exception as e:
        _logger.warning(f"Error in mesh panel dimensions formula: {e}")
    
    return self._calculate_simple_count_panels(config_values)

def _calculate_simple_count_panels(self, config_values):
    """Simple panel count calculation (fallback method)"""
    door_width = config_values.get('_CALCULATED_largest_door_width', 0)
    door_height = config_values.get('_CALCULATED_largest_door_height', 0)
    
    if not door_width or not door_height:
        return []
    
    try:
        panel_count = eval(self.template_id.mesh_panel_count_formula or "1", {"__builtins__": {}}, config_values)
        panel_count = max(1, int(panel_count))
    except:
        panel_count = 1
    
    panels = []
    for i in range(panel_count):
        panels.append({
            'width': door_width,
            'height': door_height,
            'door_number': 1,
            'panel_position': 'full',
            'name': f'Panel {i + 1} ({door_width}x{door_height}mm)'
        })
    
    return panels

def _handle_mesh_operations(self, bom, config_values):
    """Handle mesh cut operations with individual panel hunting"""
    mesh_results = []
    
    if not self.template_id.mesh_required:
        return mesh_results
    
    # Calculate panel dimensions
    panel_dimensions = self._calculate_mesh_panel_dimensions(config_values)
    
    if not panel_dimensions:
        return mesh_results
    
    mesh_series = config_values.get('mesh_series', self.template_id.mesh_series)
    hunting_priority = self.template_id.mesh_hunting_priority
    
    # Create and hunt for each panel individually
    for i, panel in enumerate(panel_dimensions):
        panel_result = self._create_and_hunt_mesh_panel(
            i + 1, panel, mesh_series, hunting_priority, bom, config_values
        )
        if panel_result:
            mesh_results.append(panel_result)
    
    return mesh_results

def _create_and_hunt_mesh_panel(self, panel_num, panel_info, series, hunting_priority, bom, config_values):
    """Create mesh operation and hunt for individual panel"""
    width = panel_info['width']
    height = panel_info['height']
    panel_name = panel_info['name']
    
    # Create individual mesh operation
    mesh_operation = self.env['mesh.cut.operation'].create({
        'name': panel_name,
        'required_width': width,
        'required_height': height,
        'required_qty': 1.0,
        'mesh_series': series,
        'state': 'draft',
        'config_id': self.id,
        'panel_number': panel_num,
        'panel_position': panel_info.get('panel_position', 'full'),
        'door_number': panel_info.get('door_number', 1),
        'hunting_priority': hunting_priority,
    })
    
    # Hunt for mesh if individual hunting is enabled
    mesh_result = None
    if self.template_id.hunt_each_panel_separately:
        mesh_result = mesh_operation.hunt_individual_panel(width, height, series, hunting_priority)
    
    # Add to BOM if mesh was found
    if mesh_result and mesh_result.get('product'):
        mesh_product = mesh_result['product']
        
        # Create BOM line
        bom_line_vals = {
            'product_id': mesh_product.id,
            'product_qty': 1.0,
            'product_uom_id': mesh_product.uom_id.id,
            'bom_id': bom.id,
        }
        
        # Add operation reference if available
        if hasattr(self.env['mrp.bom.line'], 'operation_id'):
            bom_line_vals['operation_id'] = mesh_operation.id
        
        self.env['mrp.bom.line'].create(bom_line_vals)
        
        return {
            'operation': mesh_operation,
            'product': mesh_product,
            'source_type': mesh_result.get('source_type'),
            'panel_info': panel_info,
            'hunting_result': mesh_result,
        }
    
    # Return operation even if no mesh found (for tracking)
    return {
        'operation': mesh_operation,
        'product': None,
        'source_type': 'not_found',
        'panel_info': panel_info,
        'hunting_result': None,
    }

def get_configuration_values(self):
    """Get configuration values as dictionary"""
    if not self.config_data:
        return {}
    
    try:
        return json.loads(self.config_data)
    except:
        return {}
```

### Task 5: Add Mesh Offcut Model (if not exists)
**File**: `canbrax_configmatrix/models/mesh_offcut.py`

```python
from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class MeshOffcut(models.Model):
    _name = 'mesh.offcut'
    _description = 'Mesh Offcut Tracking'
    _order = 'create_date desc'

    name = fields.Char("Offcut Name", required=True)
    product_id = fields.Many2one('product.product', string="Mesh Product", required=True)
    mesh_series = fields.Char("Mesh Series", required=True)
    
    # Original dimensions
    original_width = fields.Float("Original Width (mm)", required=True)
    original_height = fields.Float("Original Height (mm)", required=True)
    
    # Current dimensions
    current_width = fields.Float("Current Width (mm)", required=True)
    current_height = fields.Float("Current Height (mm)", required=True)
    
    # Offcut type
    is_planned_offcut = fields.Boolean("Is Planned Offcut", default=False)
    is_master_sheet = fields.Boolean("Is Master Sheet", default=False)
    
    # Status
    state = fields.Selection([
        ('available', 'Available'),
        ('reserved', 'Reserved'),
        ('used', 'Used'),
        ('scrapped', 'Scrapped')
    ], string="State", default='available')
    
    # Tracking
    location_id = fields.Many2one('stock.location', string="Location")
    notes = fields.Text("Notes")
    
    # Usage tracking
    used_in_operation_ids = fields.One2many('mesh.cut.operation', 'source_offcut_id', string="Used In Operations")
    
    @api.model
    def find_suitable_offcuts(self, required_width, required_height, mesh_series, offcut_type='any'):
        """Find suitable offcuts for given dimensions"""
        domain = [
            ('mesh_series', '=', mesh_series),
            ('current_width', '>=', required_width),
            ('current_height', '>=', required_height),
            ('state', '=', 'available')
        ]
        
        if offcut_type == 'planned':
            domain.append(('is_planned_offcut', '=', True))
        elif offcut_type == 'unplanned':
            domain.append(('is_planned_offcut', '=', False))
        
        return self.search(domain, order='current_width asc, current_height asc')
    
    def reserve_for_operation(self, operation_id):
        """Reserve this offcut for a specific operation"""
        self.ensure_one()
        if self.state == 'available':
            self.write({
                'state': 'reserved',
                'notes': f"{self.notes or ''}\nReserved for operation {operation_id}".strip()
            })
            return True
        return False
    
    def mark_as_used(self, operation_id):
        """Mark offcut as used"""
        self.ensure_one()
        self.write({
            'state': 'used',
            'notes': f"{self.notes or ''}\nUsed in operation {operation_id}".strip()
        })
```

### Task 6: Update Mesh Cut Operation View
**File**: `canbrax_configmatrix/views/mesh_cut_operation_views.xml`

```xml
<record id="view_mesh_cut_operation_form_enhanced" model="ir.ui.view">
    <field name="name">mesh.cut.operation.form.enhanced</field>
    <field name="model">mesh.cut.operation</field>
    <field name="inherit_id" ref="existing_mesh_cut_operation_form_view_id"/>
    <field name="arch" type="xml">
        <field name="required_qty" position="after">
            <field name="panel_number"/>
            <field name="panel_position"/>
            <field name="door_number"/>
        </field>
        
        <field name="mesh_series" position="after">
            <field name="hunting_priority"/>
            <field name="mesh_source_type"/>
            <field name="selected_mesh_id"/>
        </field>
        
        <field name="notes" position="after">
            <field name="hunting_notes" widget="text"/>
        </field>
    </field>
</record>

<record id="view_mesh_cut_operation_list_enhanced" model="ir.ui.view">
    <field name="name">mesh.cut.operation.list.enhanced</field>
    <field name="model">mesh.cut.operation</field>
    <field name="inherit_id" ref="existing_mesh_cut_operation_list_view_id"/>
    <field name="arch" type="xml">
        <field name="name" position="after">
            <field name="panel_number"/>
            <field name="panel_position"/>
            <field name="door_number"/>
            <field name="mesh_source_type"/>
        </field>
    </field>
</record>
```

### Task 7: Add Manufacturing Order Integration
**File**: `canbrax_configmatrix/models/mrp_production.py`

```python
def _create_mesh_cut_operations(self):
    """Create mesh cut operations for manufacturing order with individual panel dimensions"""
    self.ensure_one()
    
    # Skip if not configured or no mesh required
    if not self.config_id or not self.config_id.template_id.mesh_required:
        return False
    
    # Get configuration values
    config_values = self.config_id.get_configuration_values()
    
    # Calculate panel dimensions
    panel_dimensions = self.config_id._calculate_mesh_panel_dimensions(config_values)
    
    if not panel_dimensions:
        return False
    
    mesh_series = config_values.get('mesh_series', self.config_id.template_id.mesh_series)
    hunting_priority = self.config_id.template_id.mesh_hunting_priority
    
    # Create operations for each panel
    operations_created = 0
    for i, panel in enumerate(panel_dimensions):
        operation = self.env['mesh.cut.operation'].create({
            'name': f"MO-{self.name}: {panel['name']}",
            'required_width': panel['width'],
            'required_height': panel['height'],
            'required_qty': 1.0,
            'mesh_series': mesh_series,
            'state': 'draft',
            'production_id': self.id,
            'config_id': self.config_id.id,
            'panel_number': i + 1,
            'panel_position': panel.get('panel_position', 'full'),
            'door_number': panel.get('door_number', 1),
            'hunting_priority': hunting_priority,
        })
        
        # Hunt for mesh if individual hunting is enabled
        if self.config_id.template_id.hunt_each_panel_separately:
            operation.hunt_individual_panel(panel['width'], panel['height'], mesh_series, hunting_priority)
        
        operations_created += 1
    
    return operations_created > 0

def action_view_mesh_operations(self):
    """View mesh operations for this manufacturing order"""
    self.ensure_one()
    
    operations = self.env['mesh.cut.operation'].search([('production_id', '=', self.id)])
    
    return {
        'type': 'ir.actions.act_window',
        'name': f'Mesh Operations - {self.name}',
        'res_model': 'mesh.cut.operation',
        'view_mode': 'list,form',
        'domain': [('id', 'in', operations.ids)],
        'context': {'default_production_id': self.id},
    }
```

### Task 8: Add Configuration Action for Mesh Operations
**File**: `canbrax_configmatrix/models/config_matrix_configuration.py`

```python
def action_create_mesh_operations(self):
    """Create mesh operations for this configuration"""
    self.ensure_one()
    
    if not self.template_id.mesh_required:
        raise UserError("This configuration template does not require mesh.")
    
    # Create operations
    operations = self.env['mesh.cut.operation'].create_operations_for_configuration(self.id)
    
    if not operations:
        raise UserError("No mesh operations could be created. Check configuration values.")
    
    return {
        'type': 'ir.actions.act_window',
        'name': f'Mesh Operations - {self.name}',
        'res_model': 'mesh.cut.operation',
        'view_mode': 'list,form',
        'domain': [('id', 'in', [op.id for op in operations])],
        'context': {'default_config_id': self.id},
    }

def action_view_mesh_operations(self):
    """View existing mesh operations for this configuration"""
    self.ensure_one()
    
    operations = self.env['mesh.cut.operation'].search([('config_id', '=', self.id)])
    
    return {
        'type': 'ir.actions.act_window',
        'name': f'Mesh Operations - {self.name}',
        'res_model': 'mesh.cut.operation',
        'view_mode': 'list,form',
        'domain': [('id', 'in', operations.ids)],
        'context': {'default_config_id': self.id},
    }
```

### Task 9: Add Configuration Form Buttons
**File**: `canbrax_configmatrix/views/config_matrix_configuration_views.xml`

```xml
<record id="view_config_matrix_configuration_form_mesh_enhanced" model="ir.ui.view">
    <field name="name">config.matrix.configuration.form.mesh.enhanced</field>
    <field name="model">config.matrix.configuration</field>
    <field name="inherit_id" ref="existing_config_matrix_configuration_form_view_id"/>
    <field name="arch" type="xml">
        <header position="inside">
            <button name="action_create_mesh_operations" 
                    type="object" 
                    string="Create Mesh Operations"
                    class="btn-primary"
                    invisible="template_id.mesh_required == False"/>
            <button name="action_view_mesh_operations" 
                    type="object" 
                    string="View Mesh Operations"
                    class="btn-secondary"
                    invisible="template_id.mesh_required == False"/>
        </header>
    </field>
</record>
```

---

## Configuration Examples

### Example 1: Single Door with Midrail
**Configuration:**
- Door: 1000mm high × 500mm wide
- Midrail at 600mm from bottom
- Template: `mesh_panel_calculation_method = 'midrail_split'`

**Result:** 2 panels
- Panel 1: 600mm × 500mm (top) → Hunt: Unplanned → Planned → Master
- Panel 2: 400mm × 500mm (bottom) → Hunt: Unplanned → Planned → Master

**Possible Outcome:**
- Panel 1: Found in unplanned offcut (650mm × 520mm)
- Panel 2: Found in master sheet (requires cutting from 1200mm × 800mm)

### Example 2: Double Door with Midrail
**Configuration:**
- Door: 2100mm high × 900mm wide per door
- Midrail at 1200mm from bottom
- Template: `mesh_panel_calculation_method = 'midrail_split'`

**Result:** 4 panels
- Panel 1: 1200mm × 900mm (Door 1 top)
- Panel 2: 900mm × 900mm (Door 1 bottom)
- Panel 3: 1200mm × 900mm (Door 2 top)
- Panel 4: 900mm × 900mm (Door 2 bottom)

**Possible Outcome:**
- Panel 1: Master sheet (1500mm × 1000mm)
- Panel 2: Planned offcut (950mm × 950mm)
- Panel 3: Master sheet (1500mm × 1000mm)
- Panel 4: Unplanned offcut (1000mm × 1000mm)

### Example 3: Custom Formula
**Template Configuration:**
```python
# Custom formula for special door type
panels = []
if config_values.get('door_type') == 'triple_panel':
    # Three equal panels
    panel_height = door_height / 3
    for i in range(3):
        panels.append({
            'width': door_width,
            'height': panel_height,
            'name': f'Panel {i+1} ({door_width}x{int(panel_height)}mm)',
            'panel_position': f'section_{i+1}',
            'door_number': 1
        })
return panels
```

---

## Testing Scenarios

### Test Case 1: Single Door, No Midrail
**Setup:**
- Template: `mesh_panel_calculation_method = 'midrail_split'`
- Config: `door_type="single"`, `midrail_selected=False`
- Dimensions: 2000mm × 800mm

**Expected:**
- 1 panel: 2000mm × 800mm (full)
- 1 mesh operation created
- Individual hunting performed

### Test Case 2: Single Door, With Midrail
**Setup:**
- Template: `mesh_panel_calculation_method = 'midrail_split'`
- Config: `door_type="single"`, `midrail_selected=True`, `midrail_position=1200`
- Dimensions: 2000mm × 800mm

**Expected:**
- 2 panels: 1200mm × 800mm (top), 800mm × 800mm (bottom)
- 2 mesh operations created
- Individual hunting for each panel

### Test Case 3: Double Door, No Midrail
**Setup:**
- Template: `mesh_panel_calculation_method = 'midrail_split'`
- Config: `door_type="double"`, `midrail_selected=False`
- Dimensions: 2000mm × 800mm per door

**Expected:**
- 2 panels: 2000mm × 800mm (Door 1), 2000mm × 800mm (Door 2)
- 2 mesh operations created
- Individual hunting for each panel

### Test Case 4: Double Door, With Midrail
**Setup:**
- Template: `mesh_panel_calculation_method = 'midrail_split'`
- Config: `door_type="double"`, `midrail_selected=True`, `midrail_position=1200`
- Dimensions: 2000mm × 800mm per door

**Expected:**
- 4 panels: 1200×800 (D1 top), 800×800 (D1 bottom), 1200×800 (D2 top), 800×800 (D2 bottom)
- 4 mesh operations created
- Individual hunting for each panel

### Test Case 5: Hunting Priority Testing
**Setup:**
- Template: `mesh_hunting_priority = 'planned_unplanned_master'`
- Various panel sizes

**Expected:**
- Each panel hunts in order: Planned Offcuts → Unplanned Offcuts → Master Sheets
- Hunting notes show attempted sources
- Different panels may come from different sources

---

## Benefits

### 1. Optimal Material Usage
- Each panel hunted individually
- Best source selected for each panel
- Minimizes waste through offcut utilization

### 2. Accurate Dimensions
- Exact panel sizes calculated based on midrail position
- No more oversized panels for split configurations
- Proper material requirements

### 3. Flexible Configuration
- Supports all door/midrail combinations
- Custom formulas for special cases
- Template-based configuration

### 4. Complete Tracking
- Each panel tracked with position and door number
- Hunting results recorded
- Source type documented

### 5. Manufacturing Integration
- Operations created for manufacturing orders
- Clear instructions for each panel
- Proper material allocation

---

## Files to Create/Modify

### New Files
- `canbrax_configmatrix/models/mesh_offcut.py`
- `canbrax_configmatrix/views/mesh_offcut_views.xml`
- `canbrax_configmatrix/security/ir.model.access.csv` (add mesh.offcut access)

### Modified Files
- `canbrax_configmatrix/models/config_matrix_template.py`
- `canbrax_configmatrix/models/config_matrix_configuration.py`
- `canbrax_configmatrix/models/mesh_cut_operation.py`
- `canbrax_configmatrix/models/mrp_production.py`
- `canbrax_configmatrix/views/config_matrix_template_views.xml`
- `canbrax_configmatrix/views/config_matrix_configuration_views.xml`
- `canbrax_configmatrix/views/mesh_cut_operation_views.xml`
- `canbrax_configmatrix/__manifest__.py` (add new dependencies if needed)

---

## Implementation Priority

### Phase 1: Core Functionality
1. Add template fields and calculation methods
2. Implement panel dimension calculation
3. Basic individual hunting

### Phase 2: Enhanced Hunting
1. Add mesh offcut model
2. Implement priority-based hunting
3. Add hunting result tracking

### Phase 3: Integration
1. Manufacturing order integration
2. BOM generation updates
3. UI enhancements

### Phase 4: Testing & Optimization
1. Comprehensive testing scenarios
2. Performance optimization
3. Documentation and training

This implementation provides a complete solution for individual mesh panel hunting with optimal material usage and accurate dimension calculation.