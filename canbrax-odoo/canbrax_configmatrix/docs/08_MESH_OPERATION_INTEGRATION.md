# Mesh System Consolidation Task

## Overview
Consolidate the duplicate mesh management system into the existing component/operation mapping framework to eliminate code duplication and leverage the proven mapping system.

## Current Problem
- **Existing System**: Robust component/operation mapping with decision tree questions
- **Duplicate System**: Separate mesh-specific system with `requires_mesh`, mesh hunt, and mesh operations
- **Issue**: Two systems doing similar work, mesh system can't handle complex scenarios (2 vs 4 panels based on midrail)

## Solution: Unified Mapping System
Use hidden questions in decision trees (leveraging existing use-case visibility) to drive mesh requirements through existing component/operation mappings.

---

## Phase 1: Create Hidden Mesh Questions Using Existing Use-Case System

### Task 1.1: Create Hidden Mesh Questions
**File**: `canbrax_configmatrix/data/mesh_hidden_questions.xml`

Create hidden questions using existing visibility system - set all use cases to invisible:

```xml
<!-- Hidden section for mesh calculations -->
<record id="section_mesh_calculations" model="config.matrix.section">
    <field name="name">Mesh Calculations</field>
    <field name="description">Hidden calculations for mesh requirements</field>
    <field name="sequence">999</field>
    <field name="template_id" ref="template_double_hinged_bx"/>
</record>

<!-- Mesh Required (always true for this template) -->
<record id="field_mesh_required_dbl_bx" model="config.matrix.field">
    <field name="name">Mesh Required</field>
    <field name="technical_name">mesh_required</field>
    <field name="field_type">boolean</field>
    <field name="section_id" ref="section_mesh_calculations"/>
    <field name="sequence">10</field>
    
    <!-- Hide from all use cases -->
    <field name="check_measure_visible">False</field>
    <field name="sales_visible">False</field>
    <field name="online_visible">False</field>
    
    <!-- Set default value for all use cases -->
    <field name="check_measure_default_value">true</field>
    <field name="sales_default_value">true</field>
    <field name="online_default_value">true</field>
</record>

<!-- Mesh Panel Count (2 or 4 based on midrail) -->
<record id="field_mesh_panel_count_dbl_bx" model="config.matrix.field">
    <field name="name">Mesh Panel Count</field>
    <field name="technical_name">mesh_panel_count</field>
    <field name="field_type">number</field>
    <field name="section_id" ref="section_mesh_calculations"/>
    <field name="sequence">20</field>
    
    <!-- Hide from all use cases -->
    <field name="check_measure_visible">False</field>
    <field name="sales_visible">False</field>
    <field name="online_visible">False</field>
    
    <!-- Use dynamic default to calculate based on midrail -->
    <field name="check_measure_use_dynamic_default">True</field>
    <field name="check_measure_dynamic_default_template">midrail_selected ? 4 : 2</field>
    <field name="sales_use_dynamic_default">True</field>
    <field name="sales_dynamic_default_template">midrail_selected ? 4 : 2</field>
    <field name="online_use_dynamic_default">True</field>
    <field name="online_dynamic_default_template">midrail_selected ? 4 : 2</field>
</record>

<!-- Mesh Series (default for this template) -->
<record id="field_mesh_series_dbl_bx" model="config.matrix.field">
    <field name="name">Mesh Series</field>
    <field name="technical_name">mesh_series</field>
    <field name="field_type">selection</field>
    <field name="section_id" ref="section_mesh_calculations"/>
    <field name="sequence">30</field>
    
    <!-- Hide from all use cases -->
    <field name="check_measure_visible">False</field>
    <field name="sales_visible">False</field>
    <field name="online_visible">False</field>
    
    <!-- Set default mesh series for all use cases -->
    <field name="check_measure_default_value">saltwaterseries</field>
    <field name="sales_default_value">saltwaterseries</field>
    <field name="online_default_value">saltwaterseries</field>
</record>

<!-- Create mesh series options -->
<record id="option_mesh_series_saltwater" model="config.matrix.field.option">
    <field name="field_id" ref="field_mesh_series_dbl_bx"/>
    <field name="name">Salt Water Series</field>
    <field name="value">saltwaterseries</field>
    <field name="sequence">10</field>
</record>
```

### Task 1.2: Update Dynamic Default Processing
**File**: `canbrax_configmatrix/models/config_matrix_field.py`

Ensure dynamic defaults can reference other field values:

```python
def evaluate_dynamic_default(self, template_values, use_case='check_measure'):
    """Evaluate dynamic default template with current configuration values"""
    if use_case == 'check_measure' and self.check_measure_use_dynamic_default:
        template = self.check_measure_dynamic_default_template
    elif use_case == 'sales' and self.sales_use_dynamic_default:
        template = self.sales_dynamic_default_template
    elif use_case == 'online' and self.online_use_dynamic_default:
        template = self.online_dynamic_default_template
    else:
        return None
        
    if not template:
        return None
        
    try:
        # Create evaluation context with current field values
        context = dict(template_values)
        
        # Add helper functions
        context.update({
            'max': max,
            'min': min,
            'int': int,
            'float': float,
            'bool': bool,
        })
        
        # Convert JS-like ternary to Python
        # midrail_selected ? 4 : 2  ->  4 if midrail_selected else 2
        python_expr = self._convert_ternary_to_python(template)
        
        # Safely evaluate
        result = eval(python_expr, {"__builtins__": {}}, context)
        return result
        
    except Exception as e:
        _logger.warning(f"Dynamic default evaluation failed for {self.technical_name}: {e}")
        return None

def _convert_ternary_to_python(self, js_expr):
    """Convert JavaScript ternary operator to Python conditional"""
    import re
    
    # Pattern: condition ? value1 : value2
    ternary_pattern = r'(\w+(?:\s*[<>=!]+\s*\w+)*)\s*\?\s*([^:]+)\s*:\s*(.+)'
    match = re.match(ternary_pattern, js_expr.strip())
    
    if match:
        condition, true_val, false_val = match.groups()
        return f"{true_val.strip()} if {condition.strip()} else {false_val.strip()}"
    
    return js_expr  # Return as-is if no ternary found
```

---

## Phase 2: Enhance Component/Operation Mappings

### Task 2.1: Add Mesh-Specific Fields to Component Mapping
**File**: `canbrax_configmatrix/models/config_matrix_component_mapping.py`

```python
# Add to existing ConfigMatrixComponentMapping model
is_mesh_component = fields.Boolean('Is Mesh Component', default=False,
                                  help='This component uses mesh hunting logic')
mesh_hunt_enabled = fields.Boolean('Enable Mesh Hunt', default=False,
                                  help='Use mesh hunting to find optimal component')
mesh_series_field = fields.Char('Mesh Series Field', default='mesh_series',
                               help='Technical name of field containing mesh series')

def get_component_for_configuration(self, config_values):
    """Override to handle mesh hunting"""
    if self.is_mesh_component and self.mesh_hunt_enabled:
        return self._get_mesh_component(config_values)
    return super().get_component_for_configuration(config_values)

def _get_mesh_component(self, config_values):
    """Get mesh component using hunt process"""
    # Extract mesh requirements from config values
    mesh_width = config_values.get('_CALCULATED_largest_door_width', 0)
    mesh_height = config_values.get('_CALCULATED_largest_door_height', 0)
    mesh_series = config_values.get(self.mesh_series_field, 'saltwaterseries')
    
    # Use existing mesh hunt logic
    mesh_operation = self.env['mesh.cut.operation']
    best_mesh = mesh_operation.find_best_mesh(mesh_width, mesh_height, mesh_series)
    
    if best_mesh and best_mesh.get('product_id'):
        return self.env['product.product'].browse(best_mesh['product_id'])
    
    return self.product_id  # Fallback to mapped product
```

### Task 2.2: Add Mesh-Specific Fields to Operation Mapping
**File**: `canbrax_configmatrix/models/config_matrix_operation_mapping.py`

```python
# Add to existing ConfigMatrixOperationMapping model
is_mesh_operation = fields.Boolean('Is Mesh Operation', default=False)
mesh_operation_type = fields.Selection([
    ('standard', 'Standard Cut'),
    ('precision', 'Precision Cut'),
], string='Mesh Operation Type', default='standard')

def create_operations_for_configuration(self, config_values, quantity=None):
    """Override to handle mesh operations"""
    if self.is_mesh_operation:
        return self._create_mesh_operations(config_values, quantity)
    return super().create_operations_for_configuration(config_values, quantity)

def _create_mesh_operations(self, config_values, quantity=None):
    """Create multiple mesh cut operations based on quantity"""
    if quantity is None:
        quantity = int(config_values.get('mesh_panel_count', 1))
        
    operations = []
    mesh_width = config_values.get('_CALCULATED_largest_door_width', 0)
    mesh_height = config_values.get('_CALCULATED_largest_door_height', 0)
    mesh_series = config_values.get('mesh_series', 'saltwaterseries')
    
    for i in range(quantity):
        operation = self.env['mesh.cut.operation'].create({
            'name': f'Mesh Cut {i+1}/{quantity} - {mesh_width}x{mesh_height}mm',
            'required_width': mesh_width,
            'required_height': mesh_height,
            'required_qty': 1.0,
            'mesh_series': mesh_series,
            'state': 'draft',
        })
        operations.append(operation)
        
    return operations
```

### Task 2.3: Update Form Views for New Fields
**File**: `canbrax_configmatrix/views/config_matrix_component_mapping_views.xml`

```xml
<!-- Add to component mapping form -->
<group string="Mesh Settings" invisible="not is_mesh_component">
    <field name="is_mesh_component"/>
    <field name="mesh_hunt_enabled" invisible="not is_mesh_component"/>
    <field name="mesh_series_field" invisible="not is_mesh_component"/>
</group>
```

**File**: `canbrax_configmatrix/views/config_matrix_operation_mapping_views.xml`

```xml
<!-- Add to operation mapping form -->
<group string="Mesh Operation Settings" invisible="not is_mesh_operation">
    <field name="is_mesh_operation"/>
    <field name="mesh_operation_type" invisible="not is_mesh_operation"/>
</group>
```

---

## Phase 3: Create Component/Operation Mappings for Templates

### Task 3.1: Double Hinged BX Component Mappings
**File**: `canbrax_configmatrix/data/mesh_component_mappings.xml`

```xml
<!-- Mesh Component Mapping for Double Hinged BX -->
<record id="component_mesh_dbl_bx" model="config.matrix.component.mapping">
    <field name="name">Mesh Panels</field>
    <field name="template_id" ref="template_double_hinged_bx"/>
    <field name="condition">mesh_required == true</field>
    <field name="product_id" ref="product_mesh_generic"/>
    <field name="quantity_formula">mesh_panel_count</field>
    <field name="is_mesh_component">True</field>
    <field name="mesh_hunt_enabled">True</field>
    <field name="mesh_series_field">mesh_series</field>
    <field name="sequence">100</field>
</record>
```

### Task 3.2: Double Hinged BX Operation Mappings
**File**: `canbrax_configmatrix/data/mesh_operation_mappings.xml`

```xml
<!-- Mesh Cut Operation Mapping for Double Hinged BX -->
<record id="operation_mesh_cut_dbl_bx" model="config.matrix.operation.mapping">
    <field name="name">Mesh Cutting Operations</field>
    <field name="template_id" ref="template_double_hinged_bx"/>
    <field name="condition">mesh_required == true</field>
    <field name="operation_template_id" ref="operation_template_mesh_cut"/>
    <field name="quantity_formula">mesh_panel_count</field>
    <field name="is_mesh_operation">True</field>
    <field name="mesh_operation_type">standard</field>
    <field name="sequence">100</field>
</record>
```

---

## Phase 4: Update Configuration Processing

### Task 4.1: Update Configuration Generation
**File**: `canbrax_configmatrix/models/config_matrix_configuration.py`

Ensure hidden fields are processed during configuration:

```python
def _process_hidden_fields(self, config_values, use_case='check_measure'):
    """Process hidden fields with dynamic defaults"""
    template = self.template_id
    hidden_fields = template.field_ids.filtered(
        lambda f: not f.check_measure_visible and not f.sales_visible and not f.online_visible
    )
    
    for field in hidden_fields:
        if field.technical_name not in config_values:
            # Calculate dynamic default if available
            default_value = field.evaluate_dynamic_default(config_values, use_case)
            if default_value is not None:
                config_values[field.technical_name] = default_value
            else:
                # Use static default
                if use_case == 'check_measure':
                    config_values[field.technical_name] = field.check_measure_default_value
                elif use_case == 'sales':
                    config_values[field.technical_name] = field.sales_default_value
                elif use_case == 'online':
                    config_values[field.technical_name] = field.online_default_value
    
    return config_values
```

### Task 4.2: Update Frontend Processing
**File**: `canbrax_configmatrix/static/src/js/configurator.js`

Process hidden fields in the frontend:

```javascript
// Add to configurator initialization
async loadTemplate(templateId, configId = null) {
    // ... existing code ...
    
    // Process hidden fields
    this.processHiddenFields();
    
    // ... rest of existing code ...
}

processHiddenFields() {
    // Find hidden fields (not visible in any use case)
    const hiddenFields = this.state.sections
        .flatMap(section => section.fields)
        .filter(field => 
            !field.check_measure_visible && 
            !field.sales_visible && 
            !field.online_visible
        );
    
    // Set default values for hidden fields
    hiddenFields.forEach(field => {
        if (!this.state.values[field.technical_name]) {
            const defaultValue = this.getFieldDefaultValue(field);
            if (defaultValue !== null) {
                this.state.values[field.technical_name] = defaultValue;
            }
        }
    });
    
    // Trigger recalculation of dynamic fields
    this.recalculateDynamicFields();
}

recalculateDynamicFields() {
    // Find fields with dynamic defaults
    const dynamicFields = this.state.sections
        .flatMap(section => section.fields)
        .filter(field => field.use_dynamic_default);
    
    dynamicFields.forEach(field => {
        const newValue = this.evaluateDynamicDefault(field);
        if (newValue !== null && newValue !== this.state.values[field.technical_name]) {
            this.state.values[field.technical_name] = newValue;
        }
    });
}
```

---

## Phase 5: Create Template Variations

### Task 5.1: Single Panel Templates
**File**: `canbrax_configmatrix/data/single_panel_mesh_questions.xml`

For templates that need only 1 mesh panel:

```xml
<record id="field_mesh_panel_count_single" model="config.matrix.field">
    <field name="name">Mesh Panel Count</field>
    <field name="technical_name">mesh_panel_count</field>
    <field name="field_type">number</field>
    <field name="section_id" ref="section_mesh_calculations"/>
    
    <!-- Hide from all use cases -->
    <field name="check_measure_visible">False</field>
    <field name="sales_visible">False</field>
    <field name="online_visible">False</field>
    
    <!-- Always 1 panel for single templates -->
    <field name="check_measure_default_value">1</field>
    <field name="sales_default_value">1</field>
    <field name="online_default_value">1</field>
</record>
```

### Task 5.2: No-Mesh Templates
For templates that don't need mesh, simply don't add the mesh questions or set `mesh_required` to false.

---

## Phase 6: Migration and Testing

### Task 6.1: Data Migration Script
**File**: `canbrax_configmatrix/migrations/migrate_mesh_system.py`

```python
def migrate_existing_mesh_configurations():
    """Migrate existing mesh configurations to new system"""
    # Find templates with mesh requirements
    templates_with_mesh = env['config.matrix.template'].search([
        ('mesh_required', '=', True)
    ])
    
    for template in templates_with_mesh:
        # Create hidden mesh questions for this template
        create_mesh_questions_for_template(template)
        
        # Create component/operation mappings
        create_mesh_mappings_for_template(template)
        
        # Update existing configurations
        migrate_template_configurations(template)
```

### Task 6.2: Testing Scenarios

**Test Case 1: Double Hinged BX (No Midrail)**
- User answers questions normally
- Hidden fields: `mesh_required=true`, `mesh_panel_count=2`
- Expected: 2 mesh components, 2 cut operations

**Test Case 2: Double Hinged BX (With Midrail)**
- User selects midrail option
- Hidden fields: `mesh_required=true`, `mesh_panel_count=4` (dynamic)
- Expected: 4 mesh components, 4 cut operations

**Test Case 3: Template Without Mesh**
- No mesh questions added to template
- Expected: No mesh components/operations

---

## Benefits
1. **Leverages Existing System**: Uses proven use-case visibility and dynamic defaults
2. **No New Models**: Reuses existing field, component, and operation mapping models
3. **Template-Specific**: Each template defines its own mesh requirements
4. **Dynamic Quantities**: Handles complex scenarios (2 vs 4 panels based on midrail)
5. **Backward Compatible**: Existing configurations continue to work
6. **Maintainable**: Single system for all component/operation logic

## Files to Create/Modify
- `canbrax_configmatrix/models/config_matrix_field.py` (enhance dynamic defaults)
- `canbrax_configmatrix/models/config_matrix_component_mapping.py` (add mesh fields)
- `canbrax_configmatrix/models/config_matrix_operation_mapping.py` (add mesh fields)
- `canbrax_configmatrix/models/config_matrix_configuration.py` (process hidden fields)
- `canbrax_configmatrix/static/src/js/configurator.js` (handle hidden fields)
- `canbrax_configmatrix/data/mesh_hidden_questions.xml` (new)
- `canbrax_configmatrix/data/mesh_component_mappings.xml` (new)
- `canbrax_configmatrix/data/mesh_operation_mappings.xml` (new)
- `canbrax_configmatrix/views/config_matrix_component_mapping_views.xml` (update)
- `canbrax_configmatrix/views/config_matrix_operation_mapping_views.xml` (update)