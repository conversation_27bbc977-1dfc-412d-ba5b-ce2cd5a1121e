# Special Condition Evaluation for Price Matrices

## Overview

The ConfigMatrix system now supports dynamic price matrix selection based on special conditions. This feature allows the system to automatically choose the most appropriate price matrix based on configuration values, specifically for `mulion_mohair_price_grid_id` cases.

## How It Works

### 1. Special Condition Storage

Special conditions are stored in the `special_conditions` field of price matrices as **JavaScript formula strings**, not as JSON data. The field contains a single JavaScript formula that evaluates to true/false.

Example:
```python
# Single JavaScript formula stored as string
special_conditions = "door_height <= 2000 and door_width <= 1200 and has_midrail === true"
```

### 2. Evaluation Logic

The system follows this evaluation hierarchy:

1. **Check Dimension-Specific Conditions**: Use `get_special_conditions(height, width)` method
2. **If Conditions Found**: Evaluate those conditions using `calc_field_model._convert_js_to_python`
3. **If No Conditions**: Check the general `special_conditions` field
4. **If No Special Conditions**: Matrix is not valid
5. **Select Matrix**: Choose the first matrix where all conditions evaluate to `true`
6. **Replace Reference**: Replace the `mulion_mohair_price_grid_id` with the selected matrix

**EVALUATION LOGIC**: The evaluation now follows a specific hierarchy:
- **If `get_special_conditions(height, width)` returns conditions**: Then use those conditions for evaluation
- **If no conditions from `get_special_conditions`**: Then check the `special_conditions` field
- **If no `special_conditions` field**: Then the matrix is not valid for special condition evaluation

### 3. Field Name Resolution

The `evaluate_special_conditions` method now accepts field name parameters for proper dimension extraction:

```python
def evaluate_special_conditions(self, configuration_values, door_height_field_name=None, door_width_field_name=None):
    """Evaluate special conditions with field name resolution"""
    # Use provided field names or fall back to common field names
    height = None
    width = None
    
    if door_height_field_name and door_height_field_name in configuration_values:
        height = configuration_values[door_height_field_name]
    elif 'door_height' in configuration_values:
        height = configuration_values['door_height']
    elif 'height' in configuration_values:
        height = configuration_values['height']
        
    if door_width_field_name and door_width_field_name in configuration_values:
        width = configuration_values[door_width_field_name]
    elif 'door_width' in configuration_values:
        width = configuration_values['door_width']
    elif 'width' in configuration_values:
        width = configuration_values['width']
```

**Field Name Priority**:
1. **Provided field names**: Use `door_height_field_name` and `door_width_field_name` if available
2. **Standard names**: Fall back to `door_height` and `door_width` if available
3. **Generic names**: Fall back to `height` and `width` if available
4. **Not found**: Return `False` if no height/width values found

### 4. Formula Conversion

The system uses the existing `calc_field_model._convert_js_to_python()` method to convert JavaScript syntax to Python:

- `===` → `==` (strict equality)
- `&&` → `and` (logical AND)
- `||` → `or` (logical OR)
- `true` → `True` (boolean values)
- `false` → `False` (boolean values)

## Matrix Validity Requirements

### 1. Validation Logic

The system now enforces strict validation rules for price matrices:

```python
# EVALUATION LOGIC: If get_special_conditions returns conditions, use those
if conditions:
    # We have dimension-specific conditions from get_special_conditions
    # Evaluate each condition - if any fail, return False
    for condition in conditions:
        # Convert and evaluate condition
        # Return False if any condition fails
    return True  # All conditions passed
else:
    # No dimension-specific conditions from get_special_conditions
    # Check if we have general special_conditions field
    if not self.special_conditions:
        # No special_conditions field - matrix is not valid
        return False
    
    # Check if special_conditions field contains a valid JavaScript formula
    if not self.special_conditions.strip():
        # special_conditions field is empty - matrix is not valid
        return False
    
    # We have general special_conditions - evaluate it
    # Return True/False based on evaluation result
```

### 2. Matrix Validity States

| State | `get_special_conditions()` | `special_conditions` | Validity | Description |
|-------|---------------------------|---------------------|----------|-------------|
| **Valid** | Returns conditions | Any value | ✅ Valid | Matrix has dimension-specific conditions |
| **Valid** | No conditions | Has JavaScript formula | ✅ Valid | Matrix has general conditions only |
| **Invalid** | No conditions | Empty/None | ❌ Invalid | Matrix has no conditions at all |

### 3. Why This Validation?

This validation ensures:
- **Data Integrity**: Matrices must have conditions to be considered valid
- **Consistent Behavior**: All matrices follow the same evaluation pattern
- **Error Prevention**: Prevents matrices from being selected without proper condition evaluation
- **Quality Control**: Ensures only properly configured matrices are used for pricing

## Implementation Details

### 1. Core Method

```python
def evaluate_special_conditions(self, configuration_values, door_height_field_name=None, door_width_field_name=None):
    """Evaluate special conditions with field name resolution"""
    # First try to use existing get_special_conditions(height, width) method
    # If dimension-specific conditions exist, use those for evaluation
    # If no dimension-specific conditions, fall back to general special_conditions field
    # Convert JavaScript formulas to Python using calc_field_model._convert_js_to_python
    # Use safe_eval to evaluate conditions
    # Return True if all conditions pass, False otherwise
```

### 2. Integration with get_configuration_price

The method is integrated into the `get_configuration_price` method specifically for `mulion_mohair_price_grid_id` cases:

```python
def get_configuration_price(self, configuration_values):
    # ... existing logic ...
    
    # Special handling for mulion_mohair_price_grid_id
    if self.mulion_mohair_price_grid_id:
        # Get all sale price matrices
        sale_matrices = self.env['config.matrix.price.matrix'].search([
            ('is_sale_price_matrix', '=', True),
            ('active', '=', True)
        ])
        
        # Evaluate special conditions for each matrix with field names
        valid_matrices = []
        for matrix in sale_matrices:
            if matrix.evaluate_special_conditions(
                configuration_values,
                door_height_field_name=self.door_height_field_id.name if self.door_height_field_id else None,
                door_width_field_name=self.door_width_field_id.name if self.door_width_field_id else None
            ):
                valid_matrices.append(matrix)
        
        # Use the first valid matrix
        if valid_matrices:
            selected_matrix = valid_matrices[0]
            # ... use selected_matrix for pricing ...
        else:
            # No sale price matrix met special conditions, check the original mulion_mohair_price_grid_id
            original_matrix = self.mulion_mohair_price_grid_id
            if original_matrix:
                if original_matrix.special_conditions and original_matrix.special_conditions.strip():
                    # Evaluate the original matrix's special conditions
                    if original_matrix.evaluate_special_conditions(configuration_values, ...):
                        # Original matrix conditions pass, use it for pricing
                        pass
                    else:
                        # Original matrix conditions failed, remove from pricing
                        price_matrix_configs = [config for config in price_matrix_configs if config['type'] != 'mulion_mohair']
                else:
                    # Original matrix has no special_conditions, use it for pricing
                    pass
```

### 3. Matrix Selection Logic

The system follows this matrix selection hierarchy for `mulion_mohair_price_grid_id` cases:

1. **Primary Selection**: Evaluate all sale price matrices with `is_sale_price_matrix=True`
2. **If Matrix Selected**: Use the first matrix that meets special conditions
3. **If No Matrix Selected**: Check the original `mulion_mohair_price_grid_id`:
   - **Has Special Conditions**: Evaluate them
     - **Conditions Pass**: Use original matrix for pricing
     - **Conditions Fail**: No matrix pricing (remove from configs)
   - **No Special Conditions**: Use original matrix for pricing (default behavior)

This ensures that:
- **Dynamic Selection**: System can automatically choose the best matrix based on conditions
- **Fallback Logic**: Original matrix is used when appropriate
- **Condition Enforcement**: Matrices with failed conditions are not used for pricing
- **Backward Compatibility**: Templates without special conditions continue to work normally

## Usage Examples

### 1. Basic Special Condition

```python
# Matrix with general special condition
special_conditions = "door_height <= 2000 and door_width <= 1200"

# This matrix will only be valid for doors up to 2000mm height and 1200mm width
```

### 2. Complex Special Condition

```python
# Matrix with complex condition
special_conditions = "door_type === 'sliding' and has_midrail === true and door_height >= 1500"

# This matrix will only be valid for sliding doors with midrails and height >= 1500mm
```

### 3. Dimension-Specific Conditions

```python
# Matrix with dimension-specific conditions stored in matrix data
# get_special_conditions(1800, 1200) returns: ["has_midrail === true", "door_type === 'sliding'"]
# These conditions will be evaluated against configuration values
```

### 4. Custom Field Names

```python
# Template with custom field names
template.door_height_field_id.name = "custom_height_field"
template.door_width_field_id.name = "custom_width_field"

# Configuration values with custom field names
configuration_values = {
    'custom_height_field': 1500,
    'custom_width_field': 1200,
    'has_midrail': True,
    'door_type': 'sliding'
}

# evaluate_special_conditions will use the custom field names for dimension extraction
```

## Testing and Debugging

### 1. Test Method

Use the `test_special_conditions()` method to debug condition evaluation:

```python
# Test a matrix with specific configuration values and field names
test_result = price_matrix.test_special_conditions(
    configuration_values={
        'custom_height_field': 1500,
        'custom_width_field': 1200,
        'has_midrail': True,
        'door_type': 'sliding'
    },
    door_height_field_name="custom_height_field",
    door_width_field_name="custom_width_field"
)

print(f"Evaluation result: {test_result['evaluation_result']}")
print(f"Field names used: {test_result['field_names_used']}")
print(f"Details: {test_result['evaluation_details']}")
```

### 2. Common Issues

1. **Missing Height/Width**: Ensure the specified field names exist in configuration values
2. **Field Name Mismatch**: Check if field names in template match configuration value keys
3. **Formula Syntax Errors**: Check JavaScript formula syntax in `special_conditions` field
4. **Evaluation Errors**: Check configuration values and formula syntax
5. **No Conditions Found**: Matrix has neither dimension-specific nor general conditions

### Debug Steps

1. Check if matrix has `special_conditions` field populated
2. Verify JavaScript formula syntax
3. Check logs for evaluation errors and warnings
4. Verify field names are correctly passed from template
5. **Check Matrix Validity**: Ensure matrices have proper conditions defined
6. **Validate Field Content**: Ensure `special_conditions` field is not empty or whitespace-only
7. **Verify Field Names**: Ensure template field names match configuration value keys

## Future Enhancements

1. **Condition Templates**: Pre-built condition templates for common scenarios
2. **Visual Condition Builder**: GUI for building complex conditions
3. **Condition Validation**: Real-time validation of condition syntax
4. **Performance Optimization**: Caching of evaluated conditions
5. **Advanced Operators**: Support for more complex mathematical operations
6. **Field Name Validation**: Automatic validation of field name existence

## Summary

The special condition evaluation system provides:

- **Dynamic Matrix Selection**: Automatic selection based on configuration values
- **Flexible Conditions**: Support for both dimension-specific and general conditions
- **JavaScript Integration**: Familiar syntax for frontend developers
- **Robust Validation**: Ensures only valid matrices are used
- **Comprehensive Testing**: Built-in testing and debugging capabilities
- **Field Name Resolution**: Proper dimension extraction using template field names
- **Backward Compatibility**: Falls back to common field names if not specified

This system enables the ConfigMatrix to automatically select the most appropriate price matrix based on complex business rules, improving accuracy and reducing manual intervention in the pricing process. The new field name functionality ensures that dimensions are correctly extracted from configuration values using the template's specific field definitions.
