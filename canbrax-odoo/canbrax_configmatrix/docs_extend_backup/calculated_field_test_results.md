# Calculated Fields Management System

## 🎉 **NEW: Calculated Fields Management Module**

We've implemented a comprehensive calculated fields management system that replaces hardcoded JavaScript calculations with a flexible, admin-manageable system.

### ✅ **What's Been Implemented**

#### 1. **Calculated Fields Model** (`config.matrix.calculated.field`)
- **Database storage** for calculated field definitions
- **Sequence-based calculation** order
- **Category organization** (input, basic, threshold, range, condition, composite, kit, final)
- **Template-specific or global** field scope
- **Data type specification** (number, boolean, string)
- **Dependency tracking** for complex calculations

#### 2. **Admin Interface**
- **List view** with drag-and-drop sequencing
- **Form view** with JavaScript formula editor
- **Testing functionality** with JSON test values
- **Import/Export** from JSON definitions
- **Search and filtering** by category, template, data type

#### 3. **JavaScript Integration**
- **Dynamic field calculation** engine in configurator
- **Fallback system** for backward compatibility
- **Real-time evaluation** of formulas
- **Error handling** and logging

#### 4. **Import System**
- **JSON import wizard** for bulk field creation
- **Sample data file** with 40+ pre-defined calculated fields
- **Automatic categorization** based on field names
- **Update existing fields** or create new ones

### 🔧 **How It Works**

#### Field Definition Structure:
```json
{
  "_CALCULATED_smallest_door_height": {
    "description": "Minimum height of the door leaves.",
    "formula": "_CALCULATED_height_calculation_method === 'manual' ? Math.min(_CALCULATED_manual_left_height, _CALCULATED_manual_right_height) : ..."
  }
}
```

#### Calculation Process:
1. **Load field definitions** from database (ordered by sequence)
2. **Create evaluation context** with input field values + math functions
3. **Calculate fields sequentially**, adding results to context
4. **Handle dependencies** automatically through sequencing
5. **Return calculated values** for use in visibility conditions

### Additional Calculated Fields Added

- `_CALCULATED_height_calculation_method`: Shows which mode was used ('manual', 'even', 'uneven', or 'none')
- `_CALCULATED_deduction_assistance`: Shows the deduction assistance setting
- `_CALCULATED_door_split_type`: Shows the door split type
- `_CALCULATED_halfway_point`: `smallest_door_height / 2`
- `_CALCULATED_halfway_plus_32`: `(smallest_door_height / 2) + 32`
- `_CALCULATED_halfway_plus_79`: `(smallest_door_height / 2) + 79`
- `_CALCULATED_height_minus_740`: `smallest_door_height - 740`
- `_CALCULATED_height_minus_1137`: `smallest_door_height - 1137`
- `_CALCULATED_height_minus_1169`: `smallest_door_height - 1169`

### Lock Height Calculations

When a lock height is provided (e.g., `bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out`), additional calculated fields are created:
- `_CALCULATED_unified_condition_result`: Boolean result of the unified lock visibility condition
- `_CALCULATED_unified_condition_breakdown`: Detailed breakdown of the condition evaluation

## Test Expression

**Field:** Whitco Tasman ESCAPE - Lock Type (Top of Cut Out)
**Technical Name:** `bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out`
**Option to Test:** "3pt" (value: "3pt")

**Test Expression:**
```
(bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out < 1050) or (bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out > (_CALCULATED_smallest_door_height - 740))
```

**Simplified Test Expression (for initial testing):**
```
_CALCULATED_smallest_door_height > 1500
```
This simpler expression will show the option only when the calculated smallest door height is greater than 1500mm, making it easy to test the calculated field functionality.

**Expected Behavior:**
- The "3pt" option should be visible when:
  - Lock height is less than 1050mm, OR
  - Lock height is greater than (smallest door height - 740mm)

## Testing Steps

1. **Access Configurator:** Navigate to Configuration Matrix → Templates → Double Hinged Door BX
2. **Enable Debug Mode:** Add `?debug=1` to URL
3. **Configure Test Scenario:**
   - Set deduction assistance to test different modes
   - Enter door height values
   - Select Whitco Tasman ESCAPE as lock brand
   - Select "Top of Cut Out" as lock height location
   - Enter a lock height value
4. **Check Debug Panel:** Look for `_CALCULATED_smallest_door_height` in the field values
5. **Test Visibility:** Verify the "3pt" option appears/disappears based on the expression

## Test Results

### Test 1: Manual Mode
- **Setup:** Deduction assistance = 'no', Left door = 2100mm, Right door = 2000mm
- **Expected `_CALCULATED_smallest_door_height`:** 2000
- **Lock height test:** 1200mm
- **Expected visibility:** Visible (1200 > 2000-740 = 1260 is false, but 1200 < 1050 is false, so overall false - needs refinement)

### Test 2: Even Split Mode  
- **Setup:** Deduction assistance = 'yes', Door split = 'even', Each door = 2100mm
- **Expected `_CALCULATED_smallest_door_height`:** 2100
- **Lock height test:** 900mm
- **Expected visibility:** Visible (900 < 1050 = true)

### Test 3: Uneven Split Mode
- **Setup:** Deduction assistance = 'yes', Door split = 'uneven', Left = 2200mm, Right = 2000mm  
- **Expected `_CALCULATED_smallest_door_height`:** 2000
- **Lock height test:** 1400mm
- **Expected visibility:** Visible (1400 > 2000-740 = 1260 = true)

## Admin Interface Testing Steps

### Adding a Test Visibility Condition

1. **Navigate to Field Options:**
   - Configuration Matrix → Templates → Double Hinged Door BX
   - Go to Fields tab
   - Find "Whitco Tasman ESCAPE - Lock Type (Top of Cut Out)"
   - Click on the field to open it

2. **Edit an Option:**
   - Go to the Options tab
   - Find the "3pt" option
   - Click to edit it

3. **Add Visibility Condition:**
   - In the Visibility tab, click "Add a line"
   - Set Condition Type to "Custom Expression"
   - Enter the test expression: `_CALCULATED_smallest_door_height > 1500`
   - Save the condition

4. **Test in Configurator:**
   - Open the configurator
   - Configure door heights above and below 1500mm
   - Verify the "3pt" option appears/disappears accordingly

### Debugging Tips

- Use the browser's developer console to see JavaScript errors
- Check the "Show Field Values" debug panel for calculated field values
- Use the "Show Conditions" debug panel to see condition evaluations
- Start with simple expressions before testing complex ones

### 📁 **Files Created/Modified**

#### New Files:
- `models/config_matrix_calculated_field.py` - Main model for calculated fields
- `views/config_matrix_calculated_field_views.xml` - Admin interface views
- `wizards/config_matrix_calculated_field_import_wizard.py` - JSON import wizard
- `data/sample_calculated_fields.json` - Sample field definitions

#### Modified Files:
- `views/configurator_templates.xml` - Updated to use dynamic calculation engine
- `models/__init__.py` - Added calculated field model import
- `wizards/__init__.py` - Added import wizard
- `__manifest__.py` - Added new views to data files

### 🚀 **Getting Started**

#### 1. **Access the Admin Interface**
- Navigate to: **Configuration Matrix → Calculated Fields**
- Or go to: **Settings → Technical → Configuration Matrix → Calculated Fields**

#### 2. **Import Sample Fields**
- Click **"Import from JSON"** button
- Copy content from `data/sample_calculated_fields.json`
- Paste into the JSON Data field
- Click **"Import"**

#### 3. **Test a Field**
- Open any calculated field
- Go to **"Testing"** tab
- Add test values in JSON format:
  ```json
  {
    "bx_dbl_hinge_make_left_door_height_mm_manual": 2100,
    "bx_dbl_hinge_make_right_door_height_mm_manual": 2000,
    "bx_dbl_hinge_deduction_assistance": "no"
  }
  ```
- Click **"Test Formula"**

#### 4. **Use in Visibility Conditions**
- Create visibility conditions using calculated field names
- Example: `_CALCULATED_smallest_door_height > 1500`
- Example: `_CALCULATED_condition_lh_ge_1053 && _CALCULATED_condition_halfway_plus_32`

### 🎯 **Next Steps**

1. **Import your complete field definitions** from `calcuated_fields.md`
2. **Test the configurator** with calculated fields enabled
3. **Create lock visibility expressions** using the new calculated fields
4. **Add more complex calculated fields** as needed

### 💡 **Benefits**

- ✅ **No more hardcoded JavaScript** - all calculations are configurable
- ✅ **Easy maintenance** - update formulas without code changes
- ✅ **Reusable logic** - calculated fields work across all templates
- ✅ **Testing built-in** - verify formulas before deployment
- ✅ **Import/Export** - easy backup and migration of field definitions
- ✅ **Dependency management** - automatic calculation ordering
