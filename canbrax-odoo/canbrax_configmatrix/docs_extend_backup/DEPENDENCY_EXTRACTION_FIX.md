# Dependency Extraction Fix - Technical Reference

## Overview

This document details the fix for the `_extract_dependencies_from_formula` method in the ConfigMatrix calculated fields system. The fix resolves an issue where string literals were incorrectly captured as variable dependencies.

## Problem Description

### Original Issue
The `_extract_dependencies_from_formula` method was using a simple regex pattern that captured all word-like tokens in formulas, including string literals within quotes.

### Example Problem
```python
# Formula: "bx_dbl_hinge_t_section_mullion_inswing == 'yes'"
# Old result: ['yes', 'bx_dbl_hinge_t_section_mullion_inswing']  ❌
# Expected result: ['bx_dbl_hinge_t_section_mullion_inswing']     ✅

# Formula: "bx_dbl_hinge_t_section_mullion_inswing == 'no'"
# Old result: ['no', 'bx_dbl_hinge_t_section_mullion_inswing']   ❌
# Expected result: ['bx_dbl_hinge_t_section_mullion_inswing']     ✅
```

### Impact
- Incorrect dependency tracking
- Potential calculation errors
- False positive dependency warnings
- Performance issues with unnecessary dependency checks

## Solution Implementation

### Fixed Method
```python
def _extract_dependencies_from_formula(self, formula):
    """Extract all variable dependencies from a formula"""
    import re
    
    # Remove string literals first to avoid capturing them as variables
    # This regex matches single-quoted strings, double-quoted strings, and backtick strings
    string_pattern = r"'([^'\\]|\\.)*'|\"([^\"\\]|\\.)*\"|`([^`\\]|\\.)*`"
    formula_without_strings = re.sub(string_pattern, '', formula)
    
    # Find all variable names in the formula (excluding string literals)
    variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
    variables = re.findall(variable_pattern, formula_without_strings)
    
    # Filter out built-in functions and constants
    built_ins = ['min', 'max', 'abs', 'round', 'ceil', 'floor', 'sqrt', 'pow', 
                 'log', 'log10', 'exp', 'sin', 'cos', 'tan', 'pi', 'e',
                 'parseFloat', 'parseInt', 'Number', 'Math', 'find_midrail_height']
    
    dependencies = [var for var in variables if var not in built_ins]
    return list(set(dependencies))  # Remove duplicates
```

### Key Changes

1. **String Literal Removal**: Added comprehensive regex pattern to remove all string literals before variable extraction
2. **Enhanced Built-ins List**: Added `find_midrail_height` to the built-in functions list
3. **Two-Step Process**: First remove strings, then extract variables

### String Pattern Details

The regex pattern `r"'([^'\\]|\\.)*'|\"([^\"\\]|\\.)*\"|`([^`\\]|\\.)*`"` handles:

- **Single-quoted strings**: `'text'` with escape sequence support
- **Double-quoted strings**: `"text"` with escape sequence support  
- **Backtick strings**: `` `text` `` with escape sequence support

### Escape Sequence Support

The pattern properly handles escape sequences:
- `'don\'t'` → Removed correctly
- `"say \"hello\""` → Removed correctly
- `` `back\`tick` `` → Removed correctly

## Test Cases

### Test Results
```python
# Test Case 1: Basic string comparison
formula = "bx_dbl_hinge_t_section_mullion_inswing == 'yes'"
result = _extract_dependencies_from_formula(formula)
# Result: ['bx_dbl_hinge_t_section_mullion_inswing'] ✅

# Test Case 2: Multiple string literals
formula = "field1 == 'test' && field2 == 'another test'"
result = _extract_dependencies_from_formula(formula)
# Result: ['field2', 'field1'] ✅

# Test Case 3: Mixed string types
formula = 'field1 == "double quotes" && field2 == \'single quotes\''
result = _extract_dependencies_from_formula(formula)
# Result: ['field2', 'field1'] ✅

# Test Case 4: Backtick strings
formula = "field1 == `backticks` && field2 == 'mixed'"
result = _extract_dependencies_from_formula(formula)
# Result: ['field2', 'field1'] ✅

# Test Case 5: Complex formula with functions
formula = "min(field1, field2) + field3"
result = _extract_dependencies_from_formula(formula)
# Result: ['field2', 'field1', 'field3'] ✅

# Test Case 6: Multiple OR conditions
formula = "field1 == 'yes' || field2 == 'no' || field3 == 'maybe'"
result = _extract_dependencies_from_formula(formula)
# Result: ['field2', 'field1', 'field3'] ✅
```

## Performance Impact

### Before Fix
- Incorrect dependencies caused unnecessary calculations
- False dependency warnings in logs
- Potential circular dependency detection errors

### After Fix
- Accurate dependency tracking
- Cleaner calculation logs
- Proper dependency resolution
- No performance degradation

## Backward Compatibility

### Breaking Changes
- None - this is a bug fix that improves accuracy

### Migration Required
- None - existing formulas continue to work correctly
- Improved accuracy for all existing calculated fields

## Integration Points

### Affected Components
1. **Calculated Fields System**: Primary beneficiary of the fix
2. **Dependency Resolution**: More accurate dependency tracking
3. **Formula Validation**: Cleaner validation results
4. **Debug Tools**: More accurate debugging information

### Related Methods
- `_sort_fields_by_dependencies()`: Benefits from accurate dependencies
- `_can_calculate_field()`: Uses dependency information for calculation readiness
- `analyze_calculated_field_dependencies()`: Provides more accurate analysis

## Validation

### Manual Testing
```python
# Test the fix manually
def test_dependency_extraction():
    calc_field = self.env['config.matrix.calculated.field']
    
    test_cases = [
        ("field1 == 'yes'", ['field1']),
        ("field1 == 'no'", ['field1']),
        ("field1 == 'yes' && field2 == 'no'", ['field2', 'field1']),
        ("min(field1, field2)", ['field2', 'field1']),
        ("field1 == `backticks`", ['field1']),
    ]
    
    for formula, expected in test_cases:
        result = calc_field._extract_dependencies_from_formula(formula)
        assert set(result) == set(expected), f"Failed for: {formula}"
        print(f"✅ {formula} → {result}")
```

### Automated Testing
```python
def test_dependency_extraction_fix(self):
    """Test that string literals are not captured as dependencies"""
    calc_field = self.env['config.matrix.calculated.field']
    
    # Test cases with string literals
    test_formulas = [
        "bx_dbl_hinge_t_section_mullion_inswing == 'yes'",
        "bx_dbl_hinge_t_section_mullion_inswing == 'no'",
        "field1 == 'test' && field2 == 'another test'",
        'field1 == "double quotes" && field2 == \'single quotes\'',
        "field1 == `backticks` && field2 == 'mixed'",
    ]
    
    for formula in test_formulas:
        dependencies = calc_field._extract_dependencies_from_formula(formula)
        
        # Ensure no string literals are captured
        string_literals = ['yes', 'no', 'test', 'another test', 'double quotes', 'single quotes', 'backticks', 'mixed']
        for literal in string_literals:
            self.assertNotIn(literal, dependencies, 
                           f"String literal '{literal}' should not be in dependencies for formula: {formula}")
        
        # Ensure field names are captured
        self.assertTrue(len(dependencies) > 0, 
                       f"Should have dependencies for formula: {formula}")
```

## Monitoring and Maintenance

### Logging
The fix improves logging accuracy by ensuring dependency logs only show actual field dependencies, not string literals.

### Debugging
Debug tools now provide more accurate dependency information, making troubleshooting easier.

### Future Considerations
- Monitor for any edge cases with complex string patterns
- Consider adding support for additional string literal formats if needed
- Ensure regex pattern remains efficient for large formulas

## Conclusion

This fix resolves a critical issue in the dependency extraction system, ensuring that only actual field names are captured as dependencies. The solution is robust, handles all common string literal formats, and maintains backward compatibility while improving accuracy and performance.

The fix is essential for the proper functioning of the calculated fields dependency resolution system and should be applied in all environments using the ConfigMatrix module.
