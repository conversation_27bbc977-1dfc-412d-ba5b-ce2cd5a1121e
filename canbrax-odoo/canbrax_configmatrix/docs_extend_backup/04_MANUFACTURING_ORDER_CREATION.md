# Manufacturing Order Creation for Configurable Products

## Overview

This document describes the complete process of Manufacturing Order creation for configurable products in the CanBrax ConfigMatrix module. The system automatically creates Manufacturing Orders with configuration-specific BOMs when Sale Orders containing configurable products are confirmed.

## Complete Workflow

### 🔧 Configuration Phase
```
User configures product → save_config() → config.generate_bom() ✅
```

### 📋 Sale Order Validation Phase
```
User confirms Sale Order → _validate_configurable_products_boms() ✅
Validation passes → _process_configured_products() ✅
```

### 🚀 Procurement Phase
```
Sale Order confirmation → action_confirm() ✅
Parent method called → _action_launch_stock_rule() ✅
Procurement created → _prepare_procurement_values() adds bom_id ✅
```

### 🏭 Manufacturing Rule Phase
```
Procurement processed → procurement.group.run() ✅
Rule selection → Manufacturing rule selected ✅
Manufacturing triggered → _run_manufacture() ✅
BOM retrieved → _get_matching_bom() uses config BOM ✅
MO created → _prepare_mo_vals() with correct BOM ✅
```

### ✅ Integration Phase
```
Manufacturing Order created → Linked to Sale Order ✅
Stock moves created → Picking generated ✅
Complete traceability → SO → MO → BOM ✅
```

## Key Components

### 1. Sale Order Line Extensions (`sale_order_line.py`)

#### Route Detection
```python
def _has_mto_and_manufacture_routes(self):
    """Check if product has both MTO and Manufacturing routes"""
    mto_route = self.env.ref('stock.route_warehouse0_mto')
    manufacture_route = self.env.ref('mrp.route_warehouse0_manufacture')
    
    product_routes = self.product_id.route_ids | self.product_id.categ_id.total_route_ids
    return mto_route in product_routes and manufacture_route in product_routes
```

#### BOM Requirement Check
```python
def _requires_configured_bom(self):
    """Check if this line requires a configured BOM for manufacturing"""
    return self.is_configurable and self._has_mto_and_manufacture_routes()
```

#### Procurement Values Enhancement
```python
def _prepare_procurement_values(self, group_id=False):
    """Add configuration BOM to procurement values"""
    values = super()._prepare_procurement_values(group_id)
    
    if self.is_configurable and self.config_id and self.config_id.bom_id:
        values['bom_id'] = self.config_id.bom_id
    
    return values
```

### 2. Stock Move Extensions (`stock_move.py`)

#### BOM Preservation
```python
def _prepare_procurement_values(self):
    """Preserve BOM ID for configurable products through procurement chain"""
    values = super()._prepare_procurement_values()
    
    if self.sale_line_id and self.sale_line_id.is_configurable:
        values['sale_line_id'] = self.sale_line_id.id
        
        if self.sale_line_id.config_id and self.sale_line_id.config_id.bom_id:
            values['bom_id'] = self.sale_line_id.config_id.bom_id
    
    return values
```

### 3. Stock Rule Extensions (`stock_rule.py`)

#### BOM Selection Override
```python
def _get_matching_bom(self, product_id, company_id, values):
    """Use BOM from configuration when available"""
    
    # Primary: Try procurement values
    if values.get('bom_id', False):
        return values['bom_id']
    
    # Fallback: Look up from sale line
    if values.get('sale_line_id'):
        sale_line = self.env['sale.order.line'].browse(values['sale_line_id'])
        if sale_line.config_id and sale_line.config_id.bom_id:
            return sale_line.config_id.bom_id
    
    # Standard BOM selection
    return super()._get_matching_bom(product_id, company_id, values)
```

#### MO Values Preparation
```python
def _prepare_mo_vals(self, product_id, product_qty, product_uom, location_dest_id, 
                     name, origin, company_id, values, bom):
    """Ensure MO is linked to sale order line"""
    mo_vals = super()._prepare_mo_vals(product_id, product_qty, product_uom, 
                                       location_dest_id, name, origin, company_id, values, bom)
    
    if values.get('sale_line_id'):
        mo_vals['sale_line_id'] = values['sale_line_id']
    
    return mo_vals
```

## Technical Implementation Details

### BOM Preservation Strategy

The system uses a **dual approach** to ensure configuration BOMs are preserved:

1. **Primary Path**: BOM added to procurement values in `sale_order_line._prepare_procurement_values()`
2. **Preservation**: BOM maintained through procurement chain in `stock_move._prepare_procurement_values()`
3. **Fallback**: Direct lookup from sale line in `stock_rule._get_matching_bom()`

### Route Priority Handling

The system handles route priority correctly:
- **MTO Route** (sequence 5): Creates stock moves from Stock to Customer
- **Manufacturing Route** (sequence 10): Creates Manufacturing Orders to replenish Stock
- **Both routes work together**: MTO triggers demand, Manufacturing fulfills it

### Procurement Chain Flow

```
Sale Order Line
    ↓ _prepare_procurement_values()
Procurement (with bom_id)
    ↓ procurement.group.run()
Stock Move (Stock → Customer)
    ↓ _prepare_procurement_values()
Procurement (Stock location, with bom_id)
    ↓ Manufacturing Rule
Manufacturing Order (with config BOM)
```

## Validation and Error Handling

### Pre-Confirmation Validation
```python
def _validate_configurable_products_boms(self):
    """Validate all configurable products have required BOMs"""
    for line in self.order_line:
        if line._requires_configured_bom():
            if not line.config_id or not line.config_id.bom_id:
                raise ValidationError("Missing BOM for configurable product")
```

### Comprehensive Logging

The system includes extensive logging for debugging:
- 🚀 Process initiation
- 🔍 Detailed analysis
- ✅ Success confirmations
- ⚠️ Warnings
- ❌ Errors

## Testing and Verification

### Expected Results After Sale Order Confirmation

1. **Manufacturing Order Created**: `WH/MO/XXXXX`
2. **Correct BOM Used**: Configuration-specific BOM ID
3. **Proper Linking**: MO linked to Sale Order and Sale Order Line
4. **Stock Integration**: Picking created and linked to MO
5. **Traceability**: Complete chain from SO to MO to BOM

### Verification Steps

1. Confirm Sale Order with configurable product
2. Check Manufacturing app for new MO
3. Verify MO uses correct configuration BOM
4. Confirm MO is linked to sale order line
5. Process MO through production workflow

## Troubleshooting

### Common Issues and Solutions

1. **No MO Created**: Check product routes (MTO + Manufacturing required)
2. **Wrong BOM Used**: Verify configuration has generated BOM
3. **MO Not Linked**: Check sale_line_id preservation in procurement values
4. **Validation Errors**: Ensure all configurable products are properly configured

### Debug Logging

Enable debug logging to trace the complete flow:
```python
_logger.setLevel(logging.INFO)
```

Look for log entries with `🚀 CANBRAX:` prefix to track the process.

## Integration Points

### With Sales Module
- Sale Order confirmation triggers the process
- Sale Order Lines provide configuration context
- Procurement values carry configuration data

### With Manufacturing Module
- Manufacturing rules process configurable products
- BOMs are selected based on configuration
- Manufacturing Orders use configuration-specific BOMs

### With Stock Module
- Stock moves preserve configuration context
- Procurement chain maintains BOM information
- Pickings are properly linked to Manufacturing Orders

## Performance Considerations

- BOM lookup is optimized with dual fallback strategy
- Logging can be disabled in production for performance
- Procurement values are efficiently preserved through the chain
- Route selection uses standard Odoo prioritization

## Future Enhancements

- Support for multi-level BOMs in configurations
- Integration with advanced manufacturing features
- Enhanced reporting and analytics
- Automated quality control integration
