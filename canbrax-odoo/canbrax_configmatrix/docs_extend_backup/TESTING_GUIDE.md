# ConfigMatrix Testing Guide

## Overview

This guide provides comprehensive testing procedures for the ConfigMatrix system, covering unit tests, integration tests, user acceptance tests, and performance testing. Based on the existing codebase structure and Odoo 18 development standards.

## Table of Contents

1. [Testing Environment Setup](#testing-environment-setup)
2. [Unit Testing Framework](#unit-testing-framework)
3. [Integration Testing](#integration-testing)
4. [User Acceptance Testing](#user-acceptance-testing)
5. [Performance Testing](#performance-testing)
6. [Test Data Management](#test-data-management)
7. [Testing Best Practices](#testing-best-practices)
8. [Common Test Scenarios](#common-test-scenarios)
9. [Troubleshooting Tests](#troubleshooting-tests)

## Testing Environment Setup

### Prerequisites

- Odoo 18 development environment
- Python 3.8+ with testing dependencies
- PostgreSQL database for testing
- Access to ConfigMatrix module source code

### Environment Configuration

```bash
# Clone the repository
git clone <repository-url>
cd canbrax-odoo

# Install Odoo dependencies
pip install -r requirements.txt

# Set up test database
createdb canbrax_test

# Configure test environment
export ODOO_DB=canbrax_test
export ODOO_CONFIG_FILE=test.conf
```

### Test Configuration File (test.conf)

```ini
[options]
addons_path = ./canbrax_configmatrix
db_host = localhost
db_port = 5432
db_user = your_user
db_password = your_password
db_name = canbrax_test
test_enable = True
test_tags = canbrax
log_level = test
```

## Unit Testing Framework

### Test Structure

Tests are organized in the `tests/` directory following Odoo's testing conventions:

```
tests/
├── __init__.py
├── test_manufacturing_order_creation.py
├── test_config_matrix_template.py
├── test_config_matrix_configuration.py
├── test_mesh_operations.py
├── test_calculated_fields.py
└── test_formula_conversion.py
```

### Basic Test Class Structure

```python
from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError
import json
import logging

_logger = logging.getLogger(__name__)

class TestConfigMatrixTemplate(TransactionCase):
    """Test Configuration Template functionality"""
    
    def setUp(self):
        """Set up test data"""
        super().setUp()
        
        # Create test data
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'state': 'draft',
        })
        
        self.section = self.env['config.matrix.section'].create({
            'name': 'Test Section',
            'template_id': self.template.id,
            'sequence': 1,
        })
    
    def test_template_creation(self):
        """Test template creation"""
        self.assertEqual(self.template.name, 'Test Template')
        self.assertEqual(self.template.code, 'TEST001')
        self.assertEqual(self.template.state, 'draft')
    
    def test_section_creation(self):
        """Test section creation"""
        self.assertEqual(self.section.name, 'Test Section')
        self.assertEqual(self.section.template_id, self.template)
        self.assertEqual(self.section.sequence, 1)
```

### Running Unit Tests

```bash
# Run all tests
python -m odoo -d canbrax_test --test-enable --stop-after-init

# Run specific test file
python -m odoo -d canbrax_test --test-enable --test-tags canbrax --stop-after-init

# Run specific test class
python -m odoo -d canbrax_test --test-enable --test-tags TestConfigMatrixTemplate --stop-after-init

# Run with verbose output
python -m odoo -d canbrax_test --test-enable --test-tags canbrax --log-level=test --stop-after-init
```

## Integration Testing

### Test Configuration Workflow

```python
class TestConfigMatrixWorkflow(TransactionCase):
    """Test complete configuration workflow"""
    
    def setUp(self):
        super().setUp()
        
        # Create complete test setup
        self.product_template = self.env['product.template'].create({
            'name': 'Test Product',
            'type': 'product',
            'is_configurable': True,
        })
        
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'product_template_id': self.product_template.id,
            'state': 'active',
        })
        
        self.section = self.env['config.matrix.section'].create({
            'name': 'Dimensions',
            'template_id': self.template.id,
            'sequence': 1,
        })
        
        self.height_field = self.env['config.matrix.field'].create({
            'name': 'Height',
            'technical_name': 'height',
            'field_type': 'number',
            'section_id': self.section.id,
            'required': True,
        })
        
        self.width_field = self.env['config.matrix.field'].create({
            'name': 'Width',
            'technical_name': 'width',
            'field_type': 'number',
            'section_id': self.section.id,
            'required': True,
        })
    
    def test_complete_configuration_workflow(self):
        """Test complete configuration workflow"""
        # 1. Create configuration
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.template.id,
            'product_id': self.product_template.product_variant_id.id,
            'config_data': json.dumps({
                'height': 1000,
                'width': 500,
            }),
        })
        
        self.assertTrue(config.exists())
        
        # 2. Generate BOM
        bom = config.generate_bom()
        self.assertTrue(bom.exists())
        
        # 3. Calculate price
        price = config.calculate_price()
        self.assertGreater(price, 0)
        
        # 4. Generate visualization
        viz = config.generate_visualization()
        self.assertTrue(viz)
```

### Test Sales Order Integration

```python
class TestSalesOrderIntegration(TransactionCase):
    """Test sales order integration"""
    
    def setUp(self):
        super().setUp()
        # Setup code here
    
    def test_sales_order_with_configuration(self):
        """Test sales order creation with configuration"""
        # Create sale order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })
        
        # Add configurable product
        line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.configurable_product.id,
            'product_uom_qty': 1,
        })
        
        # Create configuration
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.template.id,
            'product_id': self.configurable_product.id,
            'sale_order_line_id': line.id,
            'config_data': json.dumps({
                'height': 1000,
                'width': 500,
            }),
        })
        
        line.config_id = config.id
        
        # Confirm sale order
        sale_order.action_confirm()
        self.assertEqual(sale_order.state, 'sale')
```

## User Acceptance Testing

### Test Scenarios

#### Scenario 1: Basic Configuration Creation

**Objective**: Verify that users can create basic configurations

**Steps**:
1. Navigate to ConfigMatrix > Templates
2. Create new template
3. Add sections and fields
4. Set field types and validation rules
5. Activate template
6. Create configuration using template
7. Verify configuration saves correctly

**Expected Results**:
- Template created successfully
- Fields display correctly
- Configuration saves without errors
- All validation rules work

#### Scenario 2: Complex Configuration with Dependencies

**Objective**: Test complex field dependencies and visibility rules

**Steps**:
1. Create template with multiple sections
2. Add fields with visibility conditions
3. Add calculated fields with formulas
4. Test field visibility based on other field values
5. Verify calculated field updates

**Expected Results**:
- Fields show/hide based on conditions
- Calculated fields update automatically
- No circular dependency issues

#### Scenario 3: BOM Generation

**Objective**: Test bill of materials generation

**Steps**:
1. Create template with component mappings
2. Configure component relationships
3. Create configuration
4. Generate BOM
5. Verify component list and quantities

**Expected Results**:
- BOM generated successfully
- Correct components included
- Quantities calculated accurately

### Test Data Requirements

```python
class TestDataGenerator:
    """Generate test data for UAT"""
    
    @staticmethod
    def create_door_template():
        """Create realistic door template for testing"""
        template = {
            'name': 'Standard Door Template',
            'code': 'DOOR_STD',
            'sections': [
                {
                    'name': 'Dimensions',
                    'fields': [
                        {'name': 'Height', 'type': 'number', 'required': True},
                        {'name': 'Width', 'type': 'number', 'required': True},
                        {'name': 'Thickness', 'type': 'number', 'required': True},
                    ]
                },
                {
                    'name': 'Features',
                    'fields': [
                        {'name': 'Door Type', 'type': 'selection', 'options': ['Single', 'Double']},
                        {'name': 'Has Midrail', 'type': 'boolean'},
                        {'name': 'Midrail Position', 'type': 'number', 'visibility': 'has_midrail == True'},
                    ]
                }
            ]
        }
        return template
```

## Performance Testing

### Test Large Configurations

```python
class TestPerformance(TransactionCase):
    """Test performance with large configurations"""
    
    def setUp(self):
        super().setUp()
        # Create large template with many fields
    
    def test_large_template_performance(self):
        """Test performance with 100+ fields"""
        start_time = time.time()
        
        # Create configuration with many fields
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.large_template.id,
            'config_data': self.large_config_data,
        })
        
        creation_time = time.time() - start_time
        self.assertLess(creation_time, 5.0)  # Should complete in under 5 seconds
        
        # Test BOM generation performance
        start_time = time.time()
        bom = config.generate_bom()
        bom_time = time.time() - start_time
        self.assertLess(bom_time, 10.0)  # Should complete in under 10 seconds
    
    def test_concurrent_configurations(self):
        """Test concurrent configuration creation"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def create_config(thread_id):
            try:
                config = self.env['config.matrix.configuration'].create({
                    'template_id': self.template.id,
                    'config_data': json.dumps({'height': 1000 + thread_id, 'width': 500}),
                })
                results.put(('success', thread_id, config.id))
            except Exception as e:
                results.put(('error', thread_id, str(e)))
        
        # Create 10 concurrent configurations
        threads = []
        for i in range(10):
            thread = threading.Thread(target=create_config, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        success_count = 0
        while not results.empty():
            status, thread_id, result = results.get()
            if status == 'success':
                success_count += 1
        
        self.assertEqual(success_count, 10)  # All should succeed
```

### Memory Usage Testing

```python
def test_memory_usage(self):
    """Test memory usage with large configurations"""
    import psutil
    import gc
    
    process = psutil.Process()
    initial_memory = process.memory_info().rss
    
    # Create many configurations
    configs = []
    for i in range(100):
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.template.id,
            'config_data': json.dumps({
                'height': 1000 + i,
                'width': 500 + i,
            }),
        })
        configs.append(config)
    
    # Force garbage collection
    gc.collect()
    
    final_memory = process.memory_info().rss
    memory_increase = final_memory - initial_memory
    
    # Memory increase should be reasonable (less than 100MB)
    self.assertLess(memory_increase, 100 * 1024 * 1024)
```

## Test Data Management

### Test Data Factory

```python
class ConfigMatrixTestFactory:
    """Factory for creating test data"""
    
    @staticmethod
    def create_template(name="Test Template", code="TEST"):
        """Create test template"""
        return {
            'name': name,
            'code': code,
            'state': 'draft',
            'description': f'Test template {name}',
        }
    
    @staticmethod
    def create_section(name="Test Section", sequence=1):
        """Create test section"""
        return {
            'name': name,
            'sequence': sequence,
        }
    
    @staticmethod
    def create_field(name="Test Field", field_type="char", required=False):
        """Create test field"""
        return {
            'name': name,
            'technical_name': name.lower().replace(' ', '_'),
            'field_type': field_type,
            'required': required,
        }
    
    @staticmethod
    def create_configuration_data(**kwargs):
        """Create test configuration data"""
        default_data = {
            'height': 1000,
            'width': 500,
            'depth': 100,
        }
        default_data.update(kwargs)
        return default_data
```

### Test Database Management

```python
class TestDatabaseManager:
    """Manage test database state"""
    
    @staticmethod
    def clean_test_data(env):
        """Clean up test data after tests"""
        # Clean up configurations
        env['config.matrix.configuration'].search([]).unlink()
        
        # Clean up templates
        env['config.matrix.template'].search([]).unlink()
        
        # Clean up sections
        env['config.matrix.section'].search([]).unlink()
        
        # Clean up fields
        env['config.matrix.field'].search([]).unlink()
    
    @staticmethod
    def backup_test_data(env):
        """Backup test data before destructive tests"""
        # Implementation for backing up test data
        pass
    
    @staticmethod
    def restore_test_data(env):
        """Restore test data after destructive tests"""
        # Implementation for restoring test data
        pass
```

## Testing Best Practices

### 1. Test Organization

- **Group related tests** in the same test class
- **Use descriptive test method names** that explain what is being tested
- **Follow AAA pattern**: Arrange, Act, Assert
- **Keep tests independent** - each test should be able to run alone

### 2. Test Data Management

- **Create minimal test data** needed for each test
- **Clean up after tests** to avoid interference
- **Use factories** for creating complex test objects
- **Avoid hardcoded values** in test assertions

### 3. Performance Considerations

- **Test with realistic data sizes** to catch performance issues
- **Measure execution time** for critical operations
- **Test concurrent operations** to identify race conditions
- **Monitor memory usage** for memory leaks

### 4. Error Testing

- **Test error conditions** and edge cases
- **Verify error messages** are user-friendly
- **Test validation rules** thoroughly
- **Ensure graceful degradation** when errors occur

## Common Test Scenarios

### 1. Field Validation Testing

```python
def test_field_validation(self):
    """Test field validation rules"""
    # Test required fields
    with self.assertRaises(ValidationError):
        self.env['config.matrix.configuration'].create({
            'template_id': self.template.id,
            'config_data': json.dumps({'width': 500}),  # Missing height
        })
    
    # Test field type validation
    with self.assertRaises(ValidationError):
        self.env['config.matrix.configuration'].create({
            'template_id': self.template.id,
            'config_data': json.dumps({'height': 'invalid', 'width': 500}),
        })
```

### 2. Business Logic Testing

```python
def test_business_logic(self):
    """Test business logic implementation"""
    # Test state transitions
    self.template.action_validate()
    self.assertEqual(self.template.state, 'testing')
    
    # Test business rules
    with self.assertRaises(UserError):
        self.template.action_activate()  # Should fail if not tested
```

### 3. Integration Testing

```python
def test_integration(self):
    """Test integration with other modules"""
    # Test sales order integration
    sale_order = self.create_sale_order()
    self.assertTrue(sale_order.exists())
    
    # Test manufacturing order creation
    mo = self.create_manufacturing_order(sale_order)
    self.assertTrue(mo.exists())
```

## Troubleshooting Tests

### Common Test Issues

#### 1. Database Connection Issues

```bash
# Check database connection
psql -h localhost -U your_user -d canbrax_test

# Verify database exists
\l | grep canbrax_test

# Check Odoo configuration
python -m odoo -d canbrax_test --help
```

#### 2. Test Data Issues

```python
# Debug test data creation
_logger.info(f"Creating template: {template_data}")
template = self.env['config.matrix.template'].create(template_data)
_logger.info(f"Template created: {template.id}")

# Check if records exist
self.assertTrue(template.exists(), "Template should exist")
self.assertTrue(template.active, "Template should be active")
```

#### 3. Performance Issues

```python
# Profile test execution
import cProfile
import pstats

def profile_test():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Run test
    self.test_performance()
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(10)  # Top 10 functions
```

### Debug Mode Testing

```python
# Enable debug mode for tests
class TestConfigMatrixDebug(TransactionCase):
    """Test with debug information"""
    
    def setUp(self):
        super().setUp()
        self.env['ir.config_parameter'].set_param('debug', 'True')
    
    def test_with_debug_info(self):
        """Test with debug information enabled"""
        # This will provide more detailed error information
        config = self.env['config.matrix.configuration'].create({
            'template_id': self.template.id,
            'config_data': json.dumps({'height': 1000, 'width': 500}),
        })
        
        # Debug information will be available in logs
        _logger.debug(f"Configuration created: {config.read()}")
```

## Running Tests in CI/CD

### GitHub Actions Example

```yaml
name: Test ConfigMatrix

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.8'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tests
      run: |
        python -m odoo -d test_db --test-enable --test-tags canbrax --stop-after-init
      env:
        ODOO_DB: test_db
        ODOO_CONFIG_FILE: test.conf
```

### Local CI Testing

```bash
#!/bin/bash
# test_local.sh

echo "Setting up test environment..."
createdb canbrax_test_local

echo "Running tests..."
python -m odoo -d canbrax_test_local --test-enable --test-tags canbrax --stop-after-init

echo "Cleaning up..."
dropdb canbrax_test_local

echo "Tests completed!"
```

## Summary

This testing guide provides a comprehensive framework for testing the ConfigMatrix system. Key points:

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions and workflows
3. **User Acceptance Tests**: Test from user perspective
4. **Performance Tests**: Ensure system meets performance requirements
5. **Best Practices**: Follow established testing patterns
6. **Troubleshooting**: Common issues and solutions

Remember to:
- Run tests regularly during development
- Maintain test data and fixtures
- Monitor test performance and coverage
- Update tests when functionality changes
- Use tests as documentation for expected behavior

For additional testing resources, refer to:
- [Odoo Testing Documentation](https://www.odoo.com/documentation/16.0/developer/reference/testing.html)
- [Python Testing Best Practices](https://docs.python-guide.org/writing/tests/)
- [ConfigMatrix Developer Guide](06_DEVELOPER_GUIDE.md)
