# Special Condition Evaluation - Technical Implementation Summary

## 🎯 **Feature Overview**

The Special Condition Evaluation system enables dynamic price matrix selection based on configuration values, specifically for `mulion_mohair_price_grid_id` cases. The system evaluates JavaScript formulas against configuration data to determine which price matrix should be used.

## 🔧 **Core Implementation**

### **1. Main Evaluation Method**

**Location**: `canbrax_configmatrix/models/pricing/config_matrix_price_matrix.py`

```python
def evaluate_special_conditions(self, configuration_values, door_height_field_name=None, door_width_field_name=None):
    """Evaluate special conditions with field name resolution"""
    self.ensure_one()
    
    try:
        from odoo.tools.safe_eval import safe_eval
        import math
        
        # Get the calculated field model for formula conversion
        calc_field_model = self.env['config.matrix.calculated.field']
        
        # Field name resolution with fallback logic
        height = self._extract_dimension_value(configuration_values, door_height_field_name, 'height')
        width = self._extract_dimension_value(configuration_values, door_width_field_name, 'width')
        
        if not height or not width:
            _logger.warning(f"Cannot evaluate special conditions - missing height/width values")
            return False
        
        # Use existing get_special_conditions method to get the condition
        conditions = self.get_special_conditions(height, width)
        
        if conditions:
            # Evaluate dimension-specific conditions
            return self._evaluate_dimension_conditions(conditions, configuration_values, calc_field_model)
        else:
            # Check general special_conditions field
            return self._evaluate_general_conditions(configuration_values, calc_field_model)
            
    except Exception as e:
        _logger.error(f"Error evaluating special conditions: {str(e)}")
        return False
```

### **2. Field Name Resolution**

```python
def _extract_dimension_value(self, configuration_values, custom_field_name, dimension_type):
    """Extract dimension value with fallback logic"""
    # Priority 1: Use provided custom field name
    if custom_field_name and custom_field_name in configuration_values:
        return configuration_values[custom_field_name]
    
    # Priority 2: Standard door field names
    standard_names = {
        'height': ['door_height', 'height'],
        'width': ['door_width', 'width']
    }
    
    for field_name in standard_names[dimension_type]:
        if field_name in configuration_values:
            return configuration_values[field_name]
    
    return None
```

### **3. Condition Evaluation**

```python
def _evaluate_dimension_conditions(self, conditions, configuration_values, calc_field_model):
    """Evaluate dimension-specific conditions"""
    for condition in conditions:
        if isinstance(condition, str):
            try:
                # Convert JavaScript formula to Python
                python_formula = calc_field_model._convert_js_to_python(condition)
                
                # Create safe evaluation context
                ctx = dict(configuration_values)
                ctx.update(self._get_math_functions())
                
                # Evaluate the condition
                result = safe_eval(python_formula, ctx)
                
                # If any condition fails, return False
                if not result:
                    _logger.info(f"Special condition '{condition}' failed")
                    return False
                    
            except Exception as e:
                _logger.warning(f"Error evaluating condition '{condition}': {str(e)}")
                return False
    
    # All conditions passed
    return True

def _evaluate_general_conditions(self, configuration_values, calc_field_model):
    """Evaluate general special_conditions field"""
    if not self.special_conditions or not self.special_conditions.strip():
        _logger.info("Matrix has no special_conditions - not valid")
        return False
    
    try:
        # Convert JavaScript formula to Python
        python_formula = calc_field_model._convert_js_to_python(self.special_conditions)
        
        # Create safe evaluation context
        ctx = dict(configuration_values)
        ctx.update(self._get_math_functions())
        
        # Evaluate the condition
        result = safe_eval(python_formula, ctx)
        return bool(result)
        
    except Exception as e:
        _logger.warning(f"Error evaluating general condition: {str(e)}")
        return False
```

### **4. Math Functions Context**

```python
def _get_math_functions(self):
    """Get math functions for safe evaluation context"""
    import math
    return {
        'round': round,
        'ceil': math.ceil,
        'floor': math.floor,
        'abs': abs,
        'max': max,
        'min': min,
        'sum': sum
    }
```

## 🔄 **Template Integration**

### **Location**: `canbrax_configmatrix/models/config_matrix_template.py`

```python
def get_configuration_price(self, configuration_values):
    """Get total price with special condition evaluation"""
    # ... existing logic ...
    
    # SPECIAL CONDITION EVALUATION: For mulion_mohair_price_grid_id cases
    if self.mulion_mohair_price_grid_id:
        _logger.info(f"[PRICE_MATRIX] Evaluating special conditions for mulion_mohair_price_grid_id case")
        
        # Get all price matrices with is_sale_price_matrix=True
        sale_price_matrices = self.env['config.matrix.price.matrix'].search([
            ('is_sale_price_matrix', '=', True),
            ('active', '=', True)
        ])
        
        # Find the first matrix that meets special conditions
        selected_matrix = None
        for matrix in sale_price_matrices:
            try:
                if matrix.evaluate_special_conditions(
                    configuration_values, 
                    door_height_field_name=self.door_height_field_id.name if self.door_height_field_id else None,
                    door_width_field_name=self.door_width_field_id.name if self.door_width_field_id else None
                ):
                    selected_matrix = matrix
                    _logger.info(f"[PRICE_MATRIX] Selected matrix '{matrix.name}' based on special conditions")
                    break
                else:
                    _logger.info(f"[PRICE_MATRIX] Matrix '{matrix.name}' failed special conditions")
            except Exception as e:
                _logger.warning(f"[PRICE_MATRIX] Error evaluating matrix '{matrix.name}': {str(e)}")
                continue
        
        if selected_matrix:
            # Replace the mulion_mohair_price_grid_id with the selected matrix
            for config in price_matrix_configs:
                if config['type'] == 'mulion_mohair':
                    config['matrix'] = selected_matrix
                    _logger.info(f"[PRICE_MATRIX] Replaced mulion_mohair_price_grid_id with selected matrix")
                    break
        else:
            _logger.warning(f"[PRICE_MATRIX] No sale price matrix met special conditions")
    
    # ... continue with existing pricing logic ...
```

## 🧪 **Testing & Debugging**

### **1. Matrix Testing Method**

```python
def test_special_conditions(self, configuration_values, door_height_field_name=None, door_width_field_name=None):
    """Test special conditions evaluation with detailed results"""
    self.ensure_one()
    
    result = {
        'matrix_id': self.id,
        'matrix_name': self.name,
        'has_special_conditions': bool(self.special_conditions),
        'special_conditions_raw': self.special_conditions,
        'evaluation_result': False,
        'evaluation_details': [],
        'errors': [],
        'field_names_used': {
            'height_field': door_height_field_name,
            'width_field': door_width_field_name
        }
    }
    
    # ... implementation details ...
    
    return result
```

### **2. Template Testing Method**

```python
def test_special_condition_evaluation(self, configuration_values):
    """Test special condition evaluation for price matrices"""
    self.ensure_one()
    
    result = {
        'template_id': self.id,
        'template_name': self.name,
        'has_mulion_mohair_price_grid': bool(self.mulion_mohair_price_grid_id),
        'available_sale_price_matrices': [],
        'evaluation_results': [],
        'selected_matrix': None
    }
    
    # ... implementation details ...
    
    return result
```

## 📊 **Data Flow**

### **1. Configuration Values Input**

```python
# Example configuration values
configuration_values = {
    'custom_height_field': 1500,      # Custom height field name
    'custom_width_field': 1200,       # Custom width field name
    'has_midrail': True,              # Feature flag
    'door_type': 'sliding',           # Door type
    'material': 'aluminum',           # Material
    'finish': 'powder_coated'         # Finish
}
```

### **2. Field Name Resolution**

```python
# Template field definitions
template.door_height_field_id.name = "custom_height_field"
template.door_width_field_id.name = "custom_width_field"

# Field name resolution priority
1. door_height_field_name (if provided)
2. door_width_field_name (if provided)
3. 'door_height' (fallback)
4. 'door_width' (fallback)
5. 'height' (fallback)
6. 'width' (fallback)
```

### **3. Condition Evaluation Flow**

```
Configuration Values → Field Name Resolution → Dimension Extraction → Condition Lookup → Formula Conversion → Safe Evaluation → Result
```

## 🔒 **Security Implementation**

### **1. Safe Evaluation**

```python
# Use safe_eval instead of eval
from odoo.tools.safe_eval import safe_eval

# Create safe context with only allowed functions
ctx = dict(configuration_values)
ctx.update(self._get_math_functions())

# Safe evaluation
result = safe_eval(python_formula, ctx)
```

### **2. Input Validation**

```python
# Validate field names
if not height or not width:
    _logger.warning(f"Cannot evaluate special conditions - missing height/width values")
    return False

# Validate configuration values
if not isinstance(configuration_values, dict):
    _logger.error("Configuration values must be a dictionary")
    return False
```

### **3. Error Handling**

```python
try:
    # Evaluation logic
    result = self._evaluate_conditions(conditions, configuration_values)
    return result
except Exception as e:
    _logger.error(f"Error evaluating special conditions: {str(e)}")
    # Don't expose internal errors to users
    return False
```

## 📈 **Performance Considerations**

### **1. Database Queries**

```python
# Single query for all sale price matrices
sale_price_matrices = self.env['config.matrix.price.matrix'].search([
    ('is_sale_price_matrix', '=', True),
    ('active', '=', True)
])

# Early termination on first valid matrix
for matrix in sale_price_matrices:
    if matrix.evaluate_special_conditions(configuration_values, ...):
        selected_matrix = matrix
        break  # Stop after first valid matrix
```

### **2. Caching Opportunities**

```python
# Potential caching implementation
@tools.ormcache('self.id', 'config_hash')
def evaluate_special_conditions_cached(self, configuration_values, ...):
    return self._evaluate_special_conditions_uncached(configuration_values, ...)

def _evaluate_special_conditions_uncached(self, configuration_values, ...):
    # Original implementation
    pass
```

### **3. Batch Processing**

```python
# Potential batch evaluation
def evaluate_multiple_matrices(self, matrices, configuration_values, ...):
    """Evaluate multiple matrices in batch"""
    results = {}
    for matrix in matrices:
        results[matrix.id] = matrix.evaluate_special_conditions(
            configuration_values, ...
        )
    return results
```

## 🚨 **Error Handling Patterns**

### **1. Graceful Degradation**

```python
# Continue processing if individual matrices fail
for matrix in sale_price_matrices:
    try:
        if matrix.evaluate_special_conditions(configuration_values, ...):
            selected_matrix = matrix
            break
    except Exception as e:
        _logger.warning(f"Error evaluating matrix '{matrix.name}': {str(e)}")
        continue  # Try next matrix
```

### **2. Comprehensive Logging**

```python
# Log all important steps
_logger.info(f"[PRICE_MATRIX] Evaluating special conditions for mulion_mohair_price_grid_id case")
_logger.info(f"[PRICE_MATRIX] Found {len(sale_price_matrices)} sale price matrices to evaluate")
_logger.info(f"[PRICE_MATRIX] Selected matrix '{matrix.name}' based on special conditions")
_logger.warning(f"[PRICE_MATRIX] No sale price matrix met special conditions")
```

### **3. User-Friendly Errors**

```python
# Don't expose internal errors to users
try:
    result = self._evaluate_conditions(conditions, configuration_values)
    return result
except Exception as e:
    _logger.error(f"Internal error evaluating conditions: {str(e)}")
    # Return False instead of raising exception
    return False
```

## 🔧 **Configuration Requirements**

### **1. Matrix Configuration**

```python
# Matrix must have either:
# 1. Dimension-specific conditions in matrix data, OR
# 2. General special_conditions field populated

# Example 1: Dimension-specific conditions
special_conditions = {
    "1200_800": ["has_midrail === true", "door_type === 'sliding'"],
    "1500_1000": ["has_midrail === true", "door_type === 'sliding'"]
}

# Example 2: General conditions
special_conditions = "door_height <= 2000 and door_width <= 1200 and has_midrail === true"
```

### **2. Template Configuration**

```python
# Template must have:
template.door_height_field_id = field_reference  # Height field reference
template.door_width_field_id = field_reference   # Width field reference
template.mulion_mohair_price_grid_id = matrix_reference  # Base matrix reference
```

### **3. Field Configuration**

```python
# Fields must be properly configured in template
# Field names must match configuration value keys
# Fields must have proper data types and validation
```

## 📝 **Usage Examples**

### **1. Basic Usage**

```python
# Evaluate conditions for a matrix
matrix = env['config.matrix.price.matrix'].browse(matrix_id)
result = matrix.evaluate_special_conditions(
    configuration_values={
        'door_height': 1500,
        'door_width': 1200,
        'has_midrail': True
    }
)
```

### **2. Custom Field Names**

```python
# Use custom field names
result = matrix.evaluate_special_conditions(
    configuration_values={
        'custom_height_field': 1500,
        'custom_width_field': 1200,
        'has_midrail': True
    },
    door_height_field_name='custom_height_field',
    door_width_field_name='custom_width_field'
)
```

### **3. Template Integration**

```python
# Get configuration price with automatic matrix selection
template = env['config.matrix.template'].browse(template_id)
price_result = template.get_configuration_price(configuration_values)
```

## 🔍 **Debugging & Troubleshooting**

### **1. Common Issues**

1. **Missing Height/Width Values**
   - Check field names in template configuration
   - Verify configuration values contain required fields
   - Check field name resolution fallback logic

2. **Formula Evaluation Errors**
   - Validate JavaScript formula syntax
   - Check formula conversion to Python
   - Verify math functions availability

3. **Matrix Not Selected**
   - Check matrix special_conditions field
   - Verify matrix is_active and is_sale_price_matrix
   - Check condition evaluation logic

### **2. Debug Commands**

```python
# Test matrix conditions
matrix = env['config.matrix.price.matrix'].browse(matrix_id)
test_result = matrix.test_special_conditions(configuration_values)
print(test_result)

# Test template evaluation
template = env['config.matrix.template'].browse(template_id)
test_result = template.test_special_condition_evaluation(configuration_values)
print(test_result)
```

### **3. Log Analysis**

```bash
# Check logs for evaluation details
grep "PRICE_MATRIX" /var/log/odoo/odoo.log

# Look for specific evaluation steps
grep "Evaluating special conditions" /var/log/odoo/odoo.log
grep "Selected matrix" /var/log/odoo/odoo.log
grep "failed special conditions" /var/log/odoo/odoo.log
```

---

**Document Created**: 2024-12-19  
**Version**: 1.0  
**Status**: Technical Reference  
**Next Update**: After Performance Optimization Implementation
