# SVG Component Guide for ConfigMatrix

This guide explains how to create and use SVG components in the ConfigMatrix system.

## Overview

SVG components are used to create dynamic visualizations of products based on configuration values. There are two types of components:

1. **Base SVG (Background)** - The foundation of the visualization
2. **SVG Layer (Conditional)** - Additional elements that are added based on conditions

## Creating SVG Components

### 1. Base SVG Component

The base SVG component should:
- Include the full SVG tag with proper namespace
- Define the viewBox and dimensions
- Include the basic structure of the product

Example:
```xml
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600" width="100%" height="100%">
    <!-- Door Frame -->
    <rect x="50" y="50" width="300" height="500" fill="#f5f5f5" stroke="#333" stroke-width="3"/>
    <!-- This is the base door - other components will be added dynamically -->
</svg>
```

### 2. Layer SVG Component

Layer components should:
- NOT include the SVG tag, just the elements to be added
- Have a condition that determines when they should be shown
- Be designed to be inserted into the base SVG

Example:
```xml
<rect x="70" y="70" width="260" height="460" fill="#e0e0e0" stroke="#555" stroke-width="1"/>
<text x="175" y="300" text-anchor="middle" font-size="24" fill="#333">BBQ</text>
```

With a condition like: `location == 'BBQ'`

## Adding SVG Components in the Database

1. Go to ConfigMatrix > Configuration > SVG Components
2. Click "Create" to add a new component
3. Fill in the following fields:
   - **Component Name**: A descriptive name (e.g., "Base Door", "BBQ Layer")
   - **Template**: Select the configuration template this component belongs to
   - **Component Type**: Choose "Base SVG (Background)" or "SVG Layer (Conditional)"
   - **Z-Index**: Set the display order (higher numbers are displayed on top)
   - **SVG Content**: Enter the SVG markup
   - **Visibility Condition**: For layers, enter a JavaScript condition (e.g., `location == 'BBQ'`)

## Using Variables in SVG Components

You can use variables from the configuration in your SVG content using the `${fieldName}` syntax:

```xml
<rect x="${x_position}" y="${y_position}" width="${width}" height="${height}" fill="#f5f5f5" stroke="#333" stroke-width="3"/>
```

These variables will be replaced with the actual values from the configuration.

## Visibility Conditions

Visibility conditions are JavaScript expressions that determine when a layer should be shown. They can use any field from the configuration:

- Simple equality: `location == 'BBQ'`
- Numeric comparison: `width > 1000`
- Multiple conditions: `width > 1000 && door_type === 'sliding'`

## Rendering Process

1. The base SVG is rendered first
2. For each layer component:
   - The condition is evaluated
   - If the condition is true, the layer is added to the SVG
   - The layer is inserted before the closing `</svg>` tag
3. The combined SVG is displayed in the configurator

## Troubleshooting

- Make sure your SVG content is valid XML
- Check that your conditions are properly formatted
- Use the browser console to debug issues
- Ensure that field names in conditions match the technical names in the configuration template

## Example

Here's a complete example of a base component and a conditional layer:

**Base SVG:**
```xml
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600" width="100%" height="100%">
    <rect x="50" y="50" width="300" height="500" fill="#f5f5f5" stroke="#333" stroke-width="3"/>
</svg>
```

**Layer SVG (with condition `location == 'BBQ'`):**
```xml
<rect x="70" y="70" width="260" height="460" fill="#e0e0e0" stroke="#555" stroke-width="1"/>
<text x="175" y="300" text-anchor="middle" font-size="24" fill="#333">BBQ</text>
```

**Rendered Result (when condition is true):**
```xml
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600" width="100%" height="100%">
    <rect x="50" y="50" width="300" height="500" fill="#f5f5f5" stroke="#333" stroke-width="3"/>
    <rect x="70" y="70" width="260" height="460" fill="#e0e0e0" stroke="#555" stroke-width="1"/>
    <text x="175" y="300" text-anchor="middle" font-size="24" fill="#333">BBQ</text>
</svg>
```
