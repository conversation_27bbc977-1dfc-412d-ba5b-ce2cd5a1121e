# ConfigMatrix: Advanced Dynamic Product Configuration & BOM Generation

[![Odoo Version](https://img.shields.io/badge/Odoo-18.0+-green.svg)](https://www.odoo.com)
[![License](https://img.shields.io/badge/License-LGPL--3-blue.svg)](https://www.gnu.org/licenses/lgpl-3.0.html)
[![Python](https://img.shields.io/badge/Python-3.8+-yellow.svg)](https://www.python.org)

ConfigMatrix is a comprehensive product configuration system for Odoo 18 that provides advanced dynamic configuration capabilities with automatic BOM generation, pricing matrices, visual previews, and seamless integration with sales and manufacturing workflows.

## 🚀 Features

### Core Configuration System
- **Dynamic Configuration Interface**: Intuitive Q&A format with conditional visibility
- **Multi-Use Case Support**: Separate configurations for Check Measure, Sales/Quoting, and Online Sales
- **Real-time BOM Generation**: Automatic Bill of Materials creation based on configuration choices
- **Visual Product Previews**: SVG-based visual representations with conditional layers
- **Advanced Pricing System**: Multi-dimensional pricing matrices with labor time calculations
- **Component Mapping**: Flexible 1:many component relationships with dynamic conditions

### Advanced Features
- **Dynamic Help & Error Messages**: Context-sensitive help text and error messages using field placeholders
- **Dynamic Default Values**: Intelligent default values based on other field values
- **Range Validation**: Dynamic min/max values with expression-based constraints
- **Calculated Fields**: Formula-based computed fields with dependency tracking
- **Visibility Conditions**: Complex conditional logic for field and option visibility
- **Performance Optimization**: Caching, debouncing, and memory management for large configurations

### Integration & Workflows
- **Sales Integration**: Seamless integration with Odoo's sales workflow
- **Manufacturing Integration**: Configured BOMs flow directly to manufacturing orders
- **Website Portal**: Customer-facing configuration interface
- **Builder Portal**: Specialized interface for professional builders
- **Import/Export Tools**: Excel and JSON import/export capabilities
- **Template Management**: Comprehensive administrative interface

## 📋 Requirements

- **Odoo 18.0+**: Full compatibility with Odoo 18 features and syntax
- **Dependencies**: `base`, `product`, `sale_management`, `mrp`, `web`, `website_sale`
- **Performance**: Optimized for large configurations with hundreds of fields
- **Security**: Role-based access control with user, admin, and builder groups

## 🛠️ Installation

### 1. Install the Module

```bash
# Clone the repository
git clone <repository-url>
cd canbrax-odoo

# Copy the module to your Odoo addons directory
cp -r canbrax_configmatrix /path/to/odoo/addons/

# Update the addons list in Odoo
# Go to Apps → Update Apps List
```

### 2. Install via Odoo Interface

1. Navigate to **Apps** → **Update Apps List**
2. Search for "ConfigMatrix" or "Matrix - Dynamic Product Configuration"
3. Click **Install**

### 3. Configure Security Groups

1. Go to **Settings** → **Users & Companies** → **Groups**
2. Configure the following groups:
   - **ConfigMatrix User**: Basic configuration access
   - **ConfigMatrix Administrator**: Full administrative access
   - **ConfigMatrix Builder**: Builder portal access
3. Assign users to appropriate groups

## 🚀 Quick Start

### 1. Create Your First Configuration Template

1. **Navigate to ConfigMatrix**:
   ```
   ConfigMatrix → Configuration → Templates
   ```

2. **Create a New Template**:
   - Click **Create**
   - Enter **Template Name**: "Standard Door Configuration"
   - Enter **Template Code**: "DOOR_STD_001"
   - Select **Product Template**: Choose a configurable product
   - Enable appropriate **Use Cases**: Check Measure, Sales/Quoting, Online Sales

3. **Add Sections**:
   - Go to **Sections** tab
   - Click **Add a line**
   - Create sections like "Door Dimensions", "Hardware", "Materials"

4. **Add Fields**:
   - Within each section, go to **Fields** tab
   - Click **Add a line**
   - Configure fields with appropriate types and validation

### 2. Configure a Product

1. **Make Product Configurable**:
   - Go to **Inventory** → **Products** → **Products**
   - Select a product
   - Check **"Is Configurable"** checkbox
   - Click **"Create Configuration Template"**

2. **Set Up Pricing Matrix**:
   - Go to **ConfigMatrix** → **Pricing** → **Price Matrices**
   - Create a new price matrix
   - Configure height and width dimensions
   - Populate pricing data

### 3. Test Configuration

1. **Create Sales Order**:
   - Go to **Sales** → **Orders** → **Quotations**
   - Add your configurable product
   - Click **"Configure"** button

2. **Complete Configuration**:
   - Fill in configuration fields
   - Watch real-time validation and pricing
   - Save configuration

3. **Verify Results**:
   - Check generated BOM
   - Verify pricing calculations
   - Review configuration data

## 📚 Documentation

### Core Documentation
- **[Overview](00_OVERVIEW.md)**: High-level overview and core concepts
- **[Data Models](01_DATA_MODELS.md)**: Complete database structure and relationships
- **[Developer Guide](06_DEVELOPER_GUIDE.md)**: Technical implementation details
- **[User Guide](07_USER_GUIDE.md)**: Step-by-step user instructions

### Specialized Guides
- **[BOM Generation](03_BOM_GENERATION.md)**: Bill of Materials creation process
- **[Website Portal](10_WEBSITE_PORTAL.md)**: Customer portal integration
- **[Odoo 18 Guidelines](ODOO_18_GUIDELINES.md)**: Odoo 18 specific syntax and best practices
- **[Enhanced JSON Import](ENHANCED_JSON_IMPORT.md)**: Data import/export functionality
- **[Manage Pricing Complete](MANAGE_PRICING_COMPLETE.md)**: Pricing matrix management
- **[Dynamic Field Matching](12_DYNAMIC_FIELD_MATCHING.md)**: Dynamic field behavior
- **[Calculated Fields](calcuated_fields.md)**: Formula-based computed fields
- **[SVG Component Guide](svg_component_guide.md)**: Visual component rendering

### Technical Documentation
- **[Python Dictionary](python_dictionary.md)**: Python code patterns and data structures
- **[Codebase Analysis](codebase_analysis_cleanup_plan.md)**: Code quality and optimization
- **[Dynamic Error Messages](dynamic_error_messages_examples.md)**: Error handling examples
- **[Lock Visibility Project Notes](lock_visibility_project_notes.md)**: UI state management

### Documentation Index
- **[INDEX.md](INDEX.md)**: Complete documentation index and navigation guide

## 🏗️ Architecture

### Backend Models
```
config.matrix.template          # Main configuration template
├── config.matrix.section       # Template sections
│   └── config.matrix.field     # Configuration fields
│       ├── config.matrix.option # Field options
│       └── config.matrix.field.component.mapping # Component mappings
├── config.matrix.component.mapping # Template-level component mappings
├── config.matrix.svg.component # Visual components
└── config.matrix.configuration # Configuration instances
```

### Pricing Models
```
config.matrix.category          # Matrix categories
├── config.matrix.price.matrix  # Price matrices
└── config.matrix.labor.time.matrix # Labor time matrices
```

### Integration Models
```
sale.order.line                 # Sales integration
product.template                # Product integration
mrp.production                  # Manufacturing integration
```

### Frontend Components
- **OWL Components**: Modern JavaScript framework for UI components
- **Configurator Widget**: Main configuration interface
- **Visual Editor**: Matrix editing and management tools
- **Expression Builder**: Complex condition and formula creation

## 🔧 Configuration

### Template Structure
- **Sections**: Logical grouping of related fields
- **Fields**: Individual configuration questions with multiple types
- **Options**: Selection choices with individual component mappings
- **Calculated Fields**: Formula-based computed values

### Advanced Rules Engine
- **Visibility Conditions**: Show/hide fields and options based on complex conditions
- **Range Conditions**: Dynamic validation with expression-based min/max values
- **Dependencies**: Track field relationships for efficient updates
- **Dynamic Content**: Help text, error messages, and default values using field placeholders

### Component Mapping System
- **Unified Component Mapping**: 1:many relationships between fields and components
- **Dynamic Conditions**: Include components based on configuration values
- **Quantity Formulas**: Calculate component quantities using expressions
- **Product Filtering**: Dynamic product selection based on reference values

### Pricing & Labor System
- **Price Matrices**: Multi-dimensional pricing based on product dimensions
- **Labor Time Matrices**: Calculate labor costs and times
- **Matrix Categories**: Organize pricing matrices by type and purpose
- **Special Conditions**: Handle complex pricing scenarios

## 🎨 Visual Components

### SVG Components
- **Vector-based Visual Representations**: Scalable vector graphics for product previews
- **Conditional Layers**: Show/hide visual elements based on configuration
- **Instructional Images**: Guide users through measurement and selection processes
- **Preview Panels**: Real-time visual feedback during configuration

### Visual Editor
- **Matrix Editor**: Visual editing of pricing and labor matrices
- **Range Editor**: Manage dimension ranges and intervals
- **Component Builder**: Visual component mapping interface
- **Expression Builder**: Visual condition and formula creation

## 🔒 Security

### Access Control
- **Role-based Access**: Different access levels for users, admins, and builders
- **Record Rules**: Data access control based on user groups
- **Field-level Security**: Sensitive fields protected by user groups
- **Portal Security**: Secure customer portal access

### Data Protection
- **Input Validation**: Comprehensive validation of all user inputs
- **Expression Security**: Safe evaluation of dynamic expressions
- **CSRF Protection**: Cross-site request forgery protection
- **Audit Trail**: Complete logging of configuration changes

## 📊 Performance

### Optimization Features
- **Caching**: Computed field caching with dependencies
- **Debouncing**: UI update optimization for real-time features
- **Memory Management**: Efficient data handling for large configurations
- **Database Optimization**: Optimized queries and indexing

### Scalability
- **Large Configurations**: Support for hundreds of fields per template
- **Multiple Use Cases**: Efficient handling of different use case scenarios
- **Concurrent Users**: Optimized for multiple simultaneous users
- **Data Volume**: Efficient handling of large amounts of configuration data

## 🧪 Testing

### Test Coverage
- **Unit Tests**: Comprehensive testing of individual components
- **Integration Tests**: Testing of complete workflows
- **Performance Tests**: Load testing with large configurations
- **Security Tests**: Validation of security measures

### Test Data
- **Sample Templates**: Pre-built templates for testing
- **Test Configurations**: Sample configurations for validation
- **Performance Benchmarks**: Standard performance test scenarios

## 🤝 Contributing

### Development Setup
1. **Fork the Repository**: Create your own fork
2. **Create Feature Branch**: `git checkout -b feature/amazing-feature`
3. **Make Changes**: Implement your feature or fix
4. **Test Thoroughly**: Ensure all tests pass
5. **Submit Pull Request**: Create a pull request with detailed description

### Code Standards
- **PEP 8**: Follow Python code style guidelines
- **Odoo Standards**: Adhere to Odoo development standards
- **Documentation**: Update documentation for new features
- **Testing**: Add tests for new functionality

## 📄 License

This project is licensed under the LGPL-3.0 License - see the [LICENSE](../LICENSE) file for details.

## 🆘 Support

### Documentation
- **User Documentation**: Complete user guides and tutorials
- **Developer Documentation**: Technical implementation details
- **API Reference**: Complete API documentation
- **Video Tutorials**: Step-by-step video guides

### Community Support
- **Community Forum**: User community discussions
- **Issue Tracker**: Report bugs and request features
- **Feature Requests**: Suggest new features and improvements
- **Contributions**: Contribute to the project

### Professional Support
- **Technical Support**: Professional technical support
- **Training**: Custom training and consulting
- **Customization**: Custom development services
- **Implementation**: Full implementation services

## 🔄 Version History

### Version ********.5 (Current)
- **Odoo 18 Compatibility**: Full compatibility with Odoo 18
- **Performance Improvements**: Enhanced caching and optimization
- **New Features**: Advanced pricing matrices and visual components
- **Security Enhancements**: Improved access control and validation
- **Documentation**: Comprehensive documentation updates

### Previous Versions
- **Version ********.4**: Initial Odoo 18 release
- **Version ********.3**: Beta release with core features
- **Version ********.2**: Alpha release for testing

## 📞 Contact

- **Website**: [ITMS Group](https://www.itmsgroup.com.au)
- **Email**: <EMAIL>
- **Phone**: +61 (0) X XXX XXX XXX
- **Address**: [Company Address]

---

**ConfigMatrix** - Advanced Dynamic Product Configuration & BOM Generation for Odoo 18

*Built with ❤️ by ITMS Group* 