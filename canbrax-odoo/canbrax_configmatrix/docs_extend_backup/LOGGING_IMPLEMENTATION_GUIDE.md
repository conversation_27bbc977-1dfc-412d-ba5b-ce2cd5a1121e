# Logging Implementation Guide for CanBrax ConfigMatrix

## Overview

This guide provides comprehensive instructions for adding logging to the CanBrax ConfigMatrix module. The logging system was temporarily removed for production requirements but can be easily re-implemented when needed for debugging, monitoring, or development purposes.

## Why Logging Was Removed

- **Performance**: Logging can impact performance in production environments
- **Log Volume**: Extensive logging can generate large log files
- **Requirements**: Production deployment requirements specified minimal logging
- **Maintenance**: Reduced log maintenance and storage requirements

## When to Re-Enable Logging

### Development Scenarios
- Debugging new features or issues
- Performance analysis and optimization
- Integration testing with other modules
- Code review and quality assurance

### Production Scenarios
- Troubleshooting specific issues
- Monitoring system performance
- Auditing configuration changes
- Compliance and traceability requirements

## Logging Architecture Used

### Logging Levels and Patterns
```python
import logging
_logger = logging.getLogger(__name__)

# Different log levels with emoji patterns for easy identification
_logger.info("🚀 CANBRAX: Process initiation")      # Major process starts
_logger.info("🔍 CANBRAX: Detailed analysis")       # Investigation/checking
_logger.info("✅ CANBRAX: Success confirmation")     # Successful operations
_logger.info("🎯 CANBRAX: Key result/finding")       # Important results
_logger.info("📋 CANBRAX: Data/configuration")       # Data display
_logger.info("🛣️ CANBRAX: Route/path information")   # Route/path details
_logger.info("🔧 CANBRAX: Processing/working")       # Active processing
_logger.info("📞 CANBRAX: Method calls")             # Method invocations
_logger.info("🏭 CANBRAX: Manufacturing specific")   # Manufacturing operations
_logger.info("🔄 CANBRAX: State changes")            # State transitions
_logger.info("🔗 CANBRAX: Linking/relationships")    # Object linking
_logger.info("📦 CANBRAX: Data packages/values")     # Data structures
_logger.info("🎉 CANBRAX: Completion/success")       # Process completion

_logger.warning("⚠️ CANBRAX: Warning condition")     # Warnings
_logger.error("❌ CANBRAX: Error condition")         # Errors
_logger.debug("🔍 CANBRAX: Debug information")       # Debug details
```

## File-by-File Logging Implementation

### 1. Sale Order Line (`models/sale_order_line.py`)

#### Import Section
```python
import logging
_logger = logging.getLogger(__name__)
```

#### Route Detection Logging
```python
def _has_mto_and_manufacture_routes(self):
    """Check if product has both MTO and Manufacturing routes"""
    try:
        mto_route = self.env.ref('stock.route_warehouse0_mto')
        manufacture_route = self.env.ref('mrp.route_warehouse0_manufacture')
        
        if not mto_route or not manufacture_route:
            _logger.warning("⚠️ CANBRAX: Required routes not found - MTO: %s, Manufacture: %s", 
                           bool(mto_route), bool(manufacture_route))
            return False

        # Check if product has both routes
        product_routes = self.product_id.route_ids | self.product_id.categ_id.total_route_ids
        has_mto = mto_route in product_routes
        has_manufacture = manufacture_route in product_routes

        _logger.info("🛣️ CANBRAX: Product %s route check - MTO: %s, Manufacture: %s, All routes: %s", 
                    self.product_id.name, has_mto, has_manufacture, [r.name for r in product_routes])

        return has_mto and has_manufacture
    except Exception as e:
        _logger.error("❌ CANBRAX: Error checking routes for product %s: %s", 
                     self.product_id.name, str(e))
        return False
```

#### BOM Requirement Logging
```python
def _requires_configured_bom(self):
    """Check if this line requires a configured BOM for manufacturing"""
    self.ensure_one()
    
    _logger.debug("🔍 CANBRAX: Checking if %s requires configured BOM", self.product_id.name)
    
    if not self.is_configurable:
        _logger.debug("❌ CANBRAX: Product %s is not configurable", self.product_id.name)
        return False
    
    has_routes = self._has_mto_and_manufacture_routes()
    _logger.debug("🛣️ CANBRAX: Product %s has MTO+Manufacturing routes: %s", self.product_id.name, has_routes)

    return self.is_configurable and has_routes
```

#### Procurement Values Logging
```python
def _prepare_procurement_values(self, group_id=False):
    """Override to add configuration BOM to procurement values"""
    values = super()._prepare_procurement_values(group_id)
    
    # Add configuration BOM if this is a configurable product with a BOM
    if self.is_configurable and self.config_id and self.config_id.bom_id:
        values['bom_id'] = self.config_id.bom_id
        _logger.info("🎯 CANBRAX: Added BOM %s to procurement values for configured product %s", 
                    self.config_id.bom_id.id, self.product_id.name)
    elif self.is_configurable:
        _logger.warning("⚠️ CANBRAX: Configurable product %s missing config or BOM - Config: %s, BOM: %s", 
                       self.product_id.name, 
                       self.config_id.id if self.config_id else 'None',
                       self.config_id.bom_id.id if self.config_id and self.config_id.bom_id else 'None')

    _logger.info("📦 CANBRAX: Procurement values for %s: %s", self.product_id.name, 
                {k: v for k, v in values.items() if k in ['bom_id', 'route_ids', 'warehouse_id', 'sale_line_id']})
    
    _logger.info("🔍 CANBRAX: All procurement value keys for %s: %s", self.product_id.name, list(values.keys()))

    return values
```

#### Stock Rule Launch Logging
```python
def _action_launch_stock_rule(self, previous_product_uom_qty=False):
    """Override to add logging for configurable products"""
    _logger.info("🚀 CANBRAX: _action_launch_stock_rule called for %d lines", len(self))
    
    # Log all lines being processed
    for line in self:
        _logger.info("🔍 CANBRAX: Processing line - Product: %s, Type: %s, Configurable: %s, State: %s", 
                    line.product_id.name, line.product_id.type, line.is_configurable, line.state)
        
        # Log configurable line details
        if line.is_configurable:
            _logger.info("📋 CANBRAX: Configurable line details - Routes: %s, Has Config: %s, Config BOM: %s", 
                        [r.name for r in line.product_id.route_ids],
                        bool(line.config_id),
                        line.config_id.bom_id.id if line.config_id and line.config_id.bom_id else 'None')
    
    # Call the standard method
    _logger.info("📞 CANBRAX: Calling parent _action_launch_stock_rule method")
    result = super()._action_launch_stock_rule(previous_product_uom_qty)
    _logger.info("✅ CANBRAX: Parent method completed successfully")
    
    return result
```

### 2. Stock Move (`models/stock_move.py`)

#### Complete Logging Implementation
```python
import logging
_logger = logging.getLogger(__name__)

def _prepare_procurement_values(self):
    """Override to preserve BOM ID for configurable products"""
    values = super()._prepare_procurement_values()
    
    _logger.info("🔄 CANBRAX: stock.move._prepare_procurement_values called for product %s", self.product_id.name)
    
    # Check if this move is related to a configurable product sale order line
    if self.sale_line_id and self.sale_line_id.is_configurable:
        _logger.info("🔍 CANBRAX: Move linked to configurable sale line %s", self.sale_line_id.id)
        
        # Preserve sale_line_id for MO linking
        values['sale_line_id'] = self.sale_line_id.id
        _logger.info("🔗 CANBRAX: Preserved sale_line_id %s in stock move procurement values", self.sale_line_id.id)
        
        # Check if the sale line has a configuration with BOM
        if self.sale_line_id.config_id and self.sale_line_id.config_id.bom_id:
            config_bom = self.sale_line_id.config_id.bom_id
            values['bom_id'] = config_bom
            _logger.info("✅ CANBRAX: Preserved configuration BOM %s in stock move procurement values for product %s", 
                        config_bom.id, self.product_id.name)
        else:
            _logger.warning("⚠️ CANBRAX: Configurable sale line %s missing config or BOM - Config: %s, BOM: %s", 
                           self.sale_line_id.id,
                           self.sale_line_id.config_id.id if self.sale_line_id.config_id else 'None',
                           self.sale_line_id.config_id.bom_id.id if self.sale_line_id.config_id and self.sale_line_id.config_id.bom_id else 'None')
    
    _logger.info("📦 CANBRAX: Stock move procurement values for %s: %s", self.product_id.name,
                {k: v for k, v in values.items() if k in ['bom_id', 'sale_line_id', 'group_id']})
    
    return values
```

### 3. Stock Rule (`models/stock_rule.py`)

#### BOM Matching Logging
```python
def _get_matching_bom(self, product_id, company_id, values):
    """Override to use BOM from configuration when available"""
    _logger.info("🏭 CANBRAX: _get_matching_bom called for product %s", product_id.name)
    _logger.info("🔍 CANBRAX: Procurement values keys: %s", list(values.keys()))
    
    # Check if a specific BOM is provided in values (from configuration)
    if values.get('bom_id', False):
        config_bom = values['bom_id']
        _logger.info("✅ CANBRAX: Using configuration BOM %s for product %s", config_bom.id, product_id.name)
        return config_bom
    else:
        _logger.info("ℹ️ CANBRAX: No bom_id in procurement values for product %s", product_id.name)
    
    # Alternative approach: Look up BOM from sale order line if available
    sale_line_id = values.get('sale_line_id')
    if sale_line_id:
        _logger.info("🔍 CANBRAX: Found sale_line_id %s, checking for configuration BOM", sale_line_id)
        sale_line = self.env['sale.order.line'].browse(sale_line_id)
        if sale_line.exists() and sale_line.is_configurable and sale_line.config_id and sale_line.config_id.bom_id:
            config_bom = sale_line.config_id.bom_id
            _logger.info("✅ CANBRAX: Found configuration BOM %s from sale line %s for product %s", 
                        config_bom.id, sale_line_id, product_id.name)
            return config_bom
        else:
            _logger.info("ℹ️ CANBRAX: Sale line %s has no configuration BOM for product %s", 
                        sale_line_id, product_id.name)
    
    # Fall back to standard BOM selection
    _logger.info("📞 CANBRAX: Calling parent _get_matching_bom for product %s", product_id.name)
    bom = super()._get_matching_bom(product_id, company_id, values)
    _logger.info("🎯 CANBRAX: Parent method returned BOM %s for product %s", bom.id if bom else 'None', product_id.name)
    return bom
```

## Logging Configuration

### Odoo Configuration File
```ini
[options]
log_level = info
log_handler = :INFO
log_db = False

# For development/debugging
log_level = debug
log_handler = :DEBUG
```

### Python Logging Configuration
```python
# In your module's __init__.py or specific files
import logging

# Set specific logger level
logging.getLogger('odoo.addons.canbrax_configmatrix').setLevel(logging.INFO)

# Or for specific modules
logging.getLogger('odoo.addons.canbrax_configmatrix.models.sale_order_line').setLevel(logging.DEBUG)
```

## Best Practices for Logging

### 1. Use Appropriate Log Levels
- **DEBUG**: Detailed diagnostic information
- **INFO**: General information about program execution
- **WARNING**: Something unexpected happened, but the software is still working
- **ERROR**: A serious problem occurred

### 2. Include Context Information
```python
_logger.info("🔍 CANBRAX: Processing %s with config %s for customer %s", 
            product.name, config.id, customer.name)
```

### 3. Use Consistent Patterns
- Always prefix with "🚀 CANBRAX:" for easy filtering
- Use emojis for visual categorization
- Include relevant object IDs and names

### 4. Performance Considerations
```python
# Good: Only format if logging level allows
if _logger.isEnabledFor(logging.DEBUG):
    _logger.debug("🔍 CANBRAX: Complex data: %s", expensive_operation())

# Better: Use lazy formatting
_logger.debug("🔍 CANBRAX: Processing %s", lambda: expensive_operation())
```

### 5. Error Handling with Logging
```python
try:
    result = risky_operation()
    _logger.info("✅ CANBRAX: Operation successful: %s", result)
except Exception as e:
    _logger.error("❌ CANBRAX: Operation failed: %s", str(e))
    _logger.debug("🔍 CANBRAX: Full traceback:", exc_info=True)
    raise
```

## Selective Logging Implementation

### Enable Logging for Specific Features
```python
# Add this flag to control logging
ENABLE_MANUFACTURING_LOGGING = False  # Set to True when needed

def _prepare_procurement_values(self, group_id=False):
    values = super()._prepare_procurement_values(group_id)
    
    if ENABLE_MANUFACTURING_LOGGING and self.is_configurable and self.config_id and self.config_id.bom_id:
        _logger.info("🎯 CANBRAX: Added BOM %s to procurement values", self.config_id.bom_id.id)
    
    return values
```

### Environment-Based Logging
```python
import os

# Enable logging based on environment variable
CANBRAX_DEBUG = os.environ.get('CANBRAX_DEBUG', 'False').lower() == 'true'

if CANBRAX_DEBUG:
    _logger.info("🔍 CANBRAX: Debug mode enabled")
```

## Log Analysis and Monitoring

### Filtering Logs
```bash
# Filter CanBrax logs
grep "CANBRAX:" /var/log/odoo/odoo.log

# Filter specific processes
grep "🏭 CANBRAX:" /var/log/odoo/odoo.log

# Filter errors only
grep "❌ CANBRAX:" /var/log/odoo/odoo.log
```

### Log Rotation and Management
```bash
# Configure logrotate for Odoo logs
/etc/logrotate.d/odoo
```

This comprehensive guide provides everything needed to re-implement logging in the CanBrax ConfigMatrix module when debugging or monitoring is required.
