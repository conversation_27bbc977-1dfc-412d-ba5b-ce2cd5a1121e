# Operation Costs Quantity Integration - Technical Documentation

## Overview

This document describes the integration of quantity multiplication in operation costs calculations within the ConfigMatrix system. The implementation ensures that operation costs scale correctly with the quantity specified in the configuration.

## Implementation Details

### Controller Method: `calculate_operation_costs`

**Location**: `canbrax_configmatrix/controllers/configuration_controller.py`

**Purpose**: Calculate operation costs based on current field values with quantity multiplier

```python
@http.route('/config_matrix/calculate_operation_costs', type='json', auth='public', website=True)
def calculate_operation_costs(self, template_id, field_values=None, **kw):
    """Calculate operation costs based on current field values with quantity multiplier"""
    _logger.info(f"[OPERATION_COSTS_DEBUG] ===== OPERATION COSTS CALCULATION STARTED =====")
    _logger.info(f"[OPERATION_COSTS_DEBUG] Template ID: {template_id}")
    _logger.info(f"[OPERATION_COSTS_DEBUG] Field values received: {field_values}")
    
    try:
        template = request.env['config.matrix.template'].sudo().browse(int(template_id))
        if not template.exists():
            _logger.warning(f"[OPERATION_COSTS_DEBUG] Template {template_id} not found")
            return {'success': False, 'error': 'Template not found'}

        if not field_values:
            field_values = {}
            _logger.info(f"[OPERATION_COSTS_DEBUG] No field values provided, using empty dict")

        # Get the global quantity multiplier
        quantity_multiplier = self._get_quantity_multiplier(field_values)
        _logger.info(f"[OPERATION_COSTS_DEBUG] ===== QUANTITY MULTIPLIER ANALYSIS =====")
        _logger.info(f"[OPERATION_COSTS_DEBUG] Found quantity multiplier: {quantity_multiplier}")
        _logger.info(f"[OPERATION_COSTS_DEBUG] Multiplier type: {type(quantity_multiplier)}")
        
        # Debug: Check for _quantity fields specifically
        quantity_fields = {k: v for k, v in field_values.items() if k.endswith('_quantity')}
        _logger.info(f"[OPERATION_COSTS_DEBUG] All _quantity fields found: {quantity_fields}")
        
        operations = []
        total_cost = 0.0

        # Process field operation mappings
        for section in template.section_ids:
            for field in section.field_ids:
                if not self._evaluate_visibility_condition(field.visibility_condition, field_values):
                    continue

                # Check for legacy single operation mapping first
                if field.workcenter_id and field.operation_name:
                    try:
                        # Calculate duration using legacy formula
                        duration_value = 0.0
                        if field.duration_formula:
                            duration_value = self._evaluate_formula(field.duration_formula, field_values, template)
                        else:
                            duration_value = 60.0  # Default

                        # For legacy fields, assume cost equals duration (backward compatibility)
                        cost_value = duration_value

                        # Apply quantity multiplier to operation cost
                        if quantity_multiplier != 1.0:
                            cost_value = cost_value * quantity_multiplier
                            _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to operation {field.operation_name}: {duration_value} -> {cost_value}")

                        if duration_value > 0:
                            operation_data = {
                                'name': field.operation_name,
                                'workcenter': field.workcenter_id.name if field.workcenter_id else '',
                                'cost': cost_value,
                                'duration': duration_value,
                                'formula': field.duration_formula or '60.0',
                                'question_number': field.question_number if field.question_number else None,
                                'field_name': field.name,
                                'source_type': 'field_legacy'
                            }

                            operations.append(operation_data)
                            total_cost += cost_value
                    except Exception as e:
                        _logger.warning(f"Error calculating cost for legacy field operation {field.operation_name}: {e}")

                # Get new field operation mappings
                for mapping in field.operation_mapping_ids:
                    try:
                        # Check if this operation should be included based on conditions
                        if mapping.condition:
                            condition_result = self._evaluate_condition(mapping.condition, field_values)
                            if not condition_result:
                                continue

                        # Calculate duration and cost using the operation template
                        duration_value = 0.0
                        cost_value = 0.0

                        if mapping.operation_template_id:
                            duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
                            cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
                        elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                            duration_value = mapping.default_duration
                            cost_value = mapping.default_duration  # Fallback: assume cost equals duration

                        # Apply quantity multiplier to operation cost
                        if quantity_multiplier != 1.0:
                            cost_value = cost_value * quantity_multiplier
                            _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to operation {mapping.operation_name}: {cost_value / quantity_multiplier} -> {cost_value}")

                        if duration_value > 0:
                            operation_data = {
                                'name': mapping.operation_name,
                                'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                                'cost': cost_value,
                                'duration': duration_value,
                                'formula': mapping.duration_formula or 'Default value',
                                'question_number': field.question_number if field.question_number else None,
                                'field_name': field.name,
                                'source_type': 'field'
                            }

                            operations.append(operation_data)
                            total_cost += cost_value

                    except Exception as e:
                        _logger.warning(f"Error calculating cost for field operation {mapping.operation_name}: {e}")
                        continue

        # Process option operation mappings
        for section in template.section_ids:
            for field in section.field_ids:
                if not self._evaluate_visibility_condition(field.visibility_condition, field_values):
                    continue

                field_value = field_values.get(field.technical_name)
                for option in field.option_ids.filtered(lambda o: o.value == field_value):

                    # Check for legacy single operation mapping first
                    if option.workcenter_id and option.operation_name:
                        try:
                            # Calculate duration using legacy formula
                            duration_value = 0.0
                            if option.duration_formula:
                                duration_value = self._evaluate_formula(option.duration_formula, field_values, template)
                            else:
                                duration_value = 60.0  # Default

                            # For legacy options, assume cost equals duration (backward compatibility)
                            cost_value = duration_value

                            # Apply quantity multiplier to operation cost
                            if quantity_multiplier != 1.0:
                                cost_value = cost_value * quantity_multiplier
                                _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to option operation {option.operation_name}: {duration_value} -> {cost_value}")

                            if duration_value > 0:
                                operation_data = {
                                    'name': option.operation_name,
                                    'workcenter': option.workcenter_id.name if option.workcenter_id else '',
                                    'cost': cost_value,
                                    'duration': duration_value,
                                    'formula': option.duration_formula or '60.0',
                                    'question_number': field.question_number if field.question_number else None,
                                    'field_name': field.name,
                                    'option_name': option.name,
                                    'source_type': 'option_legacy'
                                }

                                operations.append(operation_data)
                                total_cost += cost_value
                        except Exception as e:
                            _logger.warning(f"Error calculating cost for legacy option operation {option.operation_name}: {e}")

                    # Get new option operation mappings
                    for mapping in option.operation_mapping_ids:
                        try:
                            # Check if this operation should be included based on conditions
                            if mapping.condition:
                                condition_result = self._evaluate_condition(mapping.condition, field_values)
                                if not condition_result:
                                    continue

                            # Calculate duration and cost using the operation template
                            duration_value = 0.0
                            cost_value = 0.0

                            if mapping.operation_template_id:
                                duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
                                cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
                            elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                                duration_value = mapping.default_duration
                                cost_value = mapping.default_duration  # Fallback: assume cost equals duration

                            # Apply quantity multiplier to operation cost
                            if quantity_multiplier != 1.0:
                                cost_value = cost_value * quantity_multiplier
                                _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to option operation {mapping.operation_name}: {cost_value / quantity_multiplier} -> {cost_value}")

                            if duration_value > 0:
                                operation_data = {
                                    'name': mapping.operation_name,
                                    'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                                    'cost': cost_value,
                                    'duration': duration_value,
                                    'formula': mapping.duration_formula or 'Default value',
                                    'question_number': field.question_number if field.question_number else None,
                                    'field_name': field.name,
                                    'option_name': option.name,
                                    'source_type': 'option'
                                }

                                operations.append(operation_data)
                                total_cost += cost_value

                        except Exception as e:
                            _logger.warning(f"Error calculating cost for option operation {mapping.operation_name}: {e}")
                            continue

        _logger.info(f"[OPERATION_COSTS_DEBUG] ===== FINAL RESULTS =====")
        _logger.info(f"[OPERATION_COSTS_DEBUG] Returning {len(operations)} operations with total cost {total_cost}")
        _logger.info(f"[OPERATION_COSTS_DEBUG] Quantity multiplier applied: {quantity_multiplier}")
        _logger.info(f"[OPERATION_COSTS_DEBUG] Operations details:")
        for i, op in enumerate(operations):
            _logger.info(f"[OPERATION_COSTS_DEBUG]   Operation {i+1}: {op.get('name', 'Unknown')} - Cost: ${op.get('cost', 0)}")
        
        result = {
            'success': True,
            'operations': operations,
            'total_cost': total_cost,
            'quantity_multiplier': quantity_multiplier
        }
        _logger.info(f"[OPERATION_COSTS_DEBUG] Final result object: {result}")
        return result

    except Exception as e:
        _logger.error(f"[OPERATION_COSTS] Error calculating operation costs: {e}")
        import traceback
        _logger.error(f"[OPERATION_COSTS] Traceback: {traceback.format_exc()}")
        return {'success': False, 'error': str(e)}
```

### Quantity Multiplier Method

**Location**: `canbrax_configmatrix/controllers/configuration_controller.py`

```python
def _get_quantity_multiplier(self, config_values):
    """Get the global quantity multiplier from configuration values"""
    for key, value in config_values.items():
        if key.endswith('_quantity'):
            try:
                # Handle both string and numeric values
                if isinstance(value, str):
                    # Convert string to float
                    numeric_value = float(value)
                elif isinstance(value, (int, float)):
                    numeric_value = float(value)
                else:
                    continue
                
                if numeric_value > 0:
                    return numeric_value
            except (ValueError, TypeError):
                # Skip invalid values
                continue
    return 1.0
```

## Operation Types and Quantity Application

### 1. Legacy Field Operations

**Source**: Fields with direct `workcenter_id` and `operation_name`

```python
# Check for legacy single operation mapping first
if field.workcenter_id and field.operation_name:
    try:
        # Calculate duration using legacy formula
        duration_value = 0.0
        if field.duration_formula:
            duration_value = self._evaluate_formula(field.duration_formula, field_values, template)
        else:
            duration_value = 60.0  # Default

        # For legacy fields, assume cost equals duration (backward compatibility)
        cost_value = duration_value

        # Apply quantity multiplier to operation cost
        if quantity_multiplier != 1.0:
            cost_value = cost_value * quantity_multiplier
            _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to operation {field.operation_name}: {duration_value} -> {cost_value}")

        if duration_value > 0:
            operation_data = {
                'name': field.operation_name,
                'workcenter': field.workcenter_id.name if field.workcenter_id else '',
                'cost': cost_value,
                'duration': duration_value,
                'formula': field.duration_formula or '60.0',
                'question_number': field.question_number if field.question_number else None,
                'field_name': field.name,
                'source_type': 'field_legacy'
            }

            operations.append(operation_data)
            total_cost += cost_value
    except Exception as e:
        _logger.warning(f"Error calculating cost for legacy field operation {field.operation_name}: {e}")
```

### 2. New Field Operation Mappings

**Source**: Fields with `operation_mapping_ids`

```python
# Get new field operation mappings
for mapping in field.operation_mapping_ids:
    try:
        # Check if this operation should be included based on conditions
        if mapping.condition:
            condition_result = self._evaluate_condition(mapping.condition, field_values)
            if not condition_result:
                continue

        # Calculate duration and cost using the operation template
        duration_value = 0.0
        cost_value = 0.0

        if mapping.operation_template_id:
            duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
            cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
        elif hasattr(mapping, 'default_duration') and mapping.default_duration:
            duration_value = mapping.default_duration
            cost_value = mapping.default_duration  # Fallback: assume cost equals duration

        # Apply quantity multiplier to operation cost
        if quantity_multiplier != 1.0:
            cost_value = cost_value * quantity_multiplier
            _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to operation {mapping.operation_name}: {cost_value / quantity_multiplier} -> {cost_value}")

        if duration_value > 0:
            operation_data = {
                'name': mapping.operation_name,
                'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                'cost': cost_value,
                'duration': duration_value,
                'formula': mapping.duration_formula or 'Default value',
                'question_number': field.question_number if field.question_number else None,
                'field_name': field.name,
                'source_type': 'field'
            }

            operations.append(operation_data)
            total_cost += cost_value

    except Exception as e:
        _logger.warning(f"Error calculating cost for field operation {mapping.operation_name}: {e}")
        continue
```

### 3. Legacy Option Operations

**Source**: Options with direct `workcenter_id` and `operation_name`

```python
# Check for legacy single operation mapping first
if option.workcenter_id and option.operation_name:
    try:
        # Calculate duration using legacy formula
        duration_value = 0.0
        if option.duration_formula:
            duration_value = self._evaluate_formula(option.duration_formula, field_values, template)
        else:
            duration_value = 60.0  # Default

        # For legacy options, assume cost equals duration (backward compatibility)
        cost_value = duration_value

        # Apply quantity multiplier to operation cost
        if quantity_multiplier != 1.0:
            cost_value = cost_value * quantity_multiplier
            _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to option operation {option.operation_name}: {duration_value} -> {cost_value}")

        if duration_value > 0:
            operation_data = {
                'name': option.operation_name,
                'workcenter': option.workcenter_id.name if option.workcenter_id else '',
                'cost': cost_value,
                'duration': duration_value,
                'formula': option.duration_formula or '60.0',
                'question_number': field.question_number if field.question_number else None,
                'field_name': field.name,
                'option_name': option.name,
                'source_type': 'option_legacy'
            }

            operations.append(operation_data)
            total_cost += cost_value
    except Exception as e:
        _logger.warning(f"Error calculating cost for legacy option operation {option.operation_name}: {e}")
```

### 4. New Option Operation Mappings

**Source**: Options with `operation_mapping_ids`

```python
# Get new option operation mappings
for mapping in option.operation_mapping_ids:
    try:
        # Check if this operation should be included based on conditions
        if mapping.condition:
            condition_result = self._evaluate_condition(mapping.condition, field_values)
            if not condition_result:
                continue

        # Calculate duration and cost using the operation template
        duration_value = 0.0
        cost_value = 0.0

        if mapping.operation_template_id:
            duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
            cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
        elif hasattr(mapping, 'default_duration') and mapping.default_duration:
            duration_value = mapping.default_duration
            cost_value = mapping.default_duration  # Fallback: assume cost equals duration

        # Apply quantity multiplier to operation cost
        if quantity_multiplier != 1.0:
            cost_value = cost_value * quantity_multiplier
            _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to option operation {mapping.operation_name}: {cost_value / quantity_multiplier} -> {cost_value}")

        if duration_value > 0:
            operation_data = {
                'name': mapping.operation_name,
                'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                'cost': cost_value,
                'duration': duration_value,
                'formula': mapping.duration_formula or 'Default value',
                'question_number': field.question_number if field.question_number else None,
                'field_name': field.name,
                'option_name': option.name,
                'source_type': 'option'
            }

            operations.append(operation_data)
            total_cost += cost_value

    except Exception as e:
        _logger.warning(f"Error calculating cost for option operation {mapping.operation_name}: {e}")
        continue
```

## Response Format

### Success Response

```json
{
    "success": true,
    "operations": [
        {
            "name": "Receive Material Time",
            "workcenter": "MATERIAL RECEIVING",
            "cost": 5.6,
            "duration": 0.07,
            "formula": "60.0",
            "question_number": 9,
            "field_name": "Frame Colour",
            "source_type": "field"
        },
        {
            "name": "CNC Load / Unload Extrusion & Set Up",
            "workcenter": "DOORS: LOCK CUT OUT, CNC HOLES & BENDER",
            "cost": 2.64,
            "duration": 0.033,
            "formula": "60.0",
            "question_number": 9,
            "field_name": "Frame Colour",
            "source_type": "field"
        }
    ],
    "total_cost": 95.92,
    "quantity_multiplier": 1.0
}
```

### Error Response

```json
{
    "success": false,
    "error": "Template not found"
}
```

## Debugging and Logging

### Debug Log Format

```
[OPERATION_COSTS_DEBUG] ===== OPERATION COSTS CALCULATION STARTED =====
[OPERATION_COSTS_DEBUG] Template ID: 24
[OPERATION_COSTS_DEBUG] Field values received: {'bx_dbl_hinge_quantity': '1', ...}
[OPERATION_COSTS_DEBUG] Field values type: <class 'dict'>
[OPERATION_COSTS_DEBUG] Field values keys: ['bx_dbl_hinge_quantity', ...]
[OPERATION_COSTS_DEBUG] ===== QUANTITY MULTIPLIER ANALYSIS =====
[OPERATION_COSTS_DEBUG] Found quantity multiplier: 1.0
[OPERATION_COSTS_DEBUG] Multiplier type: <class 'float'>
[OPERATION_COSTS_DEBUG] All _quantity fields found: {'bx_dbl_hinge_quantity': '1'}
[OPERATION_COSTS] Applied quantity multiplier 2.0 to operation Receive Material Time: 5.6 -> 11.2
[OPERATION_COSTS_DEBUG] ===== FINAL RESULTS =====
[OPERATION_COSTS_DEBUG] Returning 41 operations with total cost 95.92
[OPERATION_COSTS_DEBUG] Quantity multiplier applied: 1.0
[OPERATION_COSTS_DEBUG] Operations details:
[OPERATION_COSTS_DEBUG]   Operation 1: Receive Material Time - Cost: $5.6
[OPERATION_COSTS_DEBUG]   Operation 2: CNC Load / Unload Extrusion & Set Up - Cost: $2.64
```

### Quantity Multiplier Logging

When quantity multiplier is applied:

```
[OPERATION_COSTS] Applied quantity multiplier 2.0 to operation Receive Material Time: 5.6 -> 11.2
[OPERATION_COSTS] Applied quantity multiplier 2.0 to operation CNC Load / Unload Extrusion & Set Up: 2.64 -> 5.28
```

## Testing Scenarios

### Test Case 1: Quantity = 1

**Input**:
```json
{
    "template_id": 24,
    "field_values": {
        "bx_dbl_hinge_quantity": "1",
        "bx_dbl_hinge_location": "alfresco",
        "bx_dbl_hinge_frame_colour": "black_custom_matt_gn248a"
    }
}
```

**Expected Output**:
```json
{
    "success": true,
    "total_cost": 95.92,
    "quantity_multiplier": 1.0,
    "operations": [
        {
            "name": "Receive Material Time",
            "cost": 5.6,
            "duration": 0.07
        }
    ]
}
```

### Test Case 2: Quantity = 2

**Input**:
```json
{
    "template_id": 24,
    "field_values": {
        "bx_dbl_hinge_quantity": "2",
        "bx_dbl_hinge_location": "alfresco",
        "bx_dbl_hinge_frame_colour": "black_custom_matt_gn248a"
    }
}
```

**Expected Output**:
```json
{
    "success": true,
    "total_cost": 191.84,
    "quantity_multiplier": 2.0,
    "operations": [
        {
            "name": "Receive Material Time",
            "cost": 11.2,
            "duration": 0.07
        }
    ]
}
```

### Test Case 3: Invalid Quantity

**Input**:
```json
{
    "template_id": 24,
    "field_values": {
        "bx_dbl_hinge_quantity": "abc",
        "bx_dbl_hinge_location": "alfresco"
    }
}
```

**Expected Output**:
```json
{
    "success": true,
    "total_cost": 95.92,
    "quantity_multiplier": 1.0,
    "operations": [...]
}
```

## Performance Considerations

### Optimization Strategies

1. **Efficient Quantity Detection**: Only processes fields ending with `_quantity`
2. **Early Return**: Returns first valid quantity found
3. **Error Handling**: Graceful handling of invalid values
4. **Logging Control**: Debug logging can be disabled in production

### Memory Usage

- Minimal memory overhead for quantity multiplier calculation
- No persistent caching (calculated on-demand)
- Efficient string-to-float conversion

## Integration with Other Systems

### 1. Sales Price Integration

Operation costs and sales prices now use the same quantity multiplier logic:

```python
# Both use the same _get_quantity_multiplier method
quantity_multiplier = self._get_quantity_multiplier(field_values)

# Operation costs
cost_value = base_cost * quantity_multiplier

# Sales prices (in template model)
price = base_price * quantity_multiplier
```

### 2. UI Component Integration

Frontend components send quantity as string values, which are properly parsed:

```javascript
// Frontend sends quantity as string
const fieldValues = {
    'bx_dbl_hinge_quantity': '2',  // String value
    'bx_dbl_hinge_location': 'alfresco'
};

// Backend properly converts and applies
// quantity_multiplier = 2.0
```

### 3. API Integration

The endpoint is used by various frontend components:

- Configuration forms
- Real-time cost calculators
- Price comparison tools
- Manufacturing cost estimators

## Conclusion

The operation costs quantity integration provides:

- ✅ **Consistent quantity handling** across all operation types
- ✅ **String value support** for UI-generated quantities
- ✅ **Comprehensive logging** for debugging
- ✅ **Error handling** for invalid quantities
- ✅ **Performance optimization** with efficient calculations
- ✅ **Integration compatibility** with sales prices and UI components

The implementation ensures that operation costs scale correctly with the quantity specified in the configuration, providing accurate cost calculations for manufacturing operations.
