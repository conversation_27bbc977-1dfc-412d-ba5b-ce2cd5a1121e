# ConfigMatrix Performance Fix Implementation Plan

## Phase 1: Immediate Fixes (Week 1-2)

### 1.1 Add Dependency Tracking System

**Create dependency graph builder:**

```javascript
// Add to static/src/js/configurator.js
class DependencyTracker {
    constructor() {
        this.dependencies = new Map();
        this.reverseDependencies = new Map();
    }
    
    // Build dependency graph from field visibility conditions
    buildDependencyGraph(fields) {
        fields.forEach(field => {
            if (field.visibility_condition) {
                const dependencies = this.extractFieldReferences(field.visibility_condition);
                this.dependencies.set(field.name, dependencies);
                
                // Build reverse lookup
                dependencies.forEach(dep => {
                    if (!this.reverseDependencies.has(dep)) {
                        this.reverseDependencies.set(dep, new Set());
                    }
                    this.reverseDependencies.get(dep).add(field.name);
                });
            }
        });
    }
    
    // Extract field names from visibility condition
    extractFieldReferences(condition) {
        const fieldPattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g;
        const matches = condition.match(fieldPattern) || [];
        return matches.filter(match => 
            !['and', 'or', 'not', 'true', 'false', 'if', 'else'].includes(match)
        );
    }
    
    // Get fields that depend on the changed field
    getDependentFields(fieldName) {
        return Array.from(this.reverseDependencies.get(fieldName) || []);
    }
}

// Initialize dependency tracker
const dependencyTracker = new DependencyTracker();
```

### 1.2 Optimize Field Change Handler

**Replace the current onFieldChange method:**

```javascript
// Optimized field change handler
function onFieldChange(fieldName, value) {
    console.log(`[PERF] Field changed: ${fieldName} = ${value}`);
    
    // Update field value
    fieldValues[fieldName] = value;
    
    // Only update dependent fields instead of all fields
    const dependentFields = dependencyTracker.getDependentFields(fieldName);
    
    if (dependentFields.length > 0) {
        console.log(`[PERF] Updating ${dependentFields.length} dependent fields`);
        updateVisibilitySelective(dependentFields);
    }
    
    // Update dynamic defaults only for affected fields
    updateDynamicDefaultsSelective(dependentFields);
    
    // Debounced debug panel update
    debouncedUpdateDebugPanel();
}

// Selective visibility update
function updateVisibilitySelective(fieldNames) {
    fieldNames.forEach(fieldName => {
        const field = document.querySelector(`[data-field-name="${fieldName}"]`);
        if (field) {
            const shouldShow = evaluateVisibilityForField(fieldName);
            toggleFieldVisibility(field, shouldShow);
        }
    });
}
```

### 1.3 Add Expression Caching

**Backend caching in models/config_matrix_field.py:**

```python
from odoo import tools
import json
import hashlib

class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    @tools.ormcache('self.id', 'values_hash')
    def evaluate_visibility_cached(self, values_hash, field_values):
        """Cached visibility evaluation"""
        return self._evaluate_visibility_uncached(field_values)
    
    def _evaluate_visibility_uncached(self, field_values):
        """Original visibility evaluation logic"""
        if not self.visibility_condition:
            return True
        try:
            return safe_eval(self.visibility_condition, field_values)
        except Exception as e:
            _logger.error(f"Error evaluating visibility for {self.name}: {e}")
            return True
    
    def evaluate_visibility(self, field_values):
        """Main visibility evaluation with caching"""
        # Create hash of field values for cache key
        values_str = json.dumps(field_values, sort_keys=True)
        values_hash = hashlib.md5(values_str.encode()).hexdigest()
        
        return self.evaluate_visibility_cached(values_hash, field_values)
```

**Frontend caching:**

```javascript
// Expression cache
class ExpressionCache {
    constructor(maxSize = 1000) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }
    
    get(key) {
        if (this.cache.has(key)) {
            // Move to end (LRU)
            const value = this.cache.get(key);
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }
        return null;
    }
    
    set(key, value) {
        if (this.cache.size >= this.maxSize) {
            // Remove oldest entry
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }
    
    clear() {
        this.cache.clear();
    }
}

const expressionCache = new ExpressionCache();
```

### 1.4 Add Debouncing

**Debounce rapid field changes:**

```javascript
// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Debounced functions
const debouncedUpdateDebugPanel = debounce(updateDebugPanel, 200);
const debouncedUpdateVisibility = debounce(updateVisibility, 100);
const debouncedSaveConfiguration = debounce(saveConfiguration, 500);
```

## Phase 2: Advanced Optimizations (Week 3-4)

### 2.1 Implement Progressive Updates

**Batch processing for large field sets:**

```javascript
async function updateVisibilityProgressive(fieldNames) {
    const batchSize = 10;
    const batches = [];
    
    // Split into batches
    for (let i = 0; i < fieldNames.length; i += batchSize) {
        batches.push(fieldNames.slice(i, i + batchSize));
    }
    
    // Process batches with yield points
    for (const batch of batches) {
        batch.forEach(fieldName => {
            const field = document.querySelector(`[data-field-name="${fieldName}"]`);
            if (field) {
                const shouldShow = evaluateVisibilityForField(fieldName);
                toggleFieldVisibility(field, shouldShow);
            }
        });
        
        // Yield to browser for UI updates
        await new Promise(resolve => setTimeout(resolve, 0));
    }
}
```

### 2.2 Add Performance Monitoring

**Performance tracking:**

```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            fieldChanges: 0,
            evaluations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalTime: 0
        };
    }
    
    startTimer(operation) {
        return {
            operation,
            startTime: performance.now()
        };
    }
    
    endTimer(timer) {
        const duration = performance.now() - timer.startTime;
        this.metrics.totalTime += duration;
        console.log(`[PERF] ${timer.operation}: ${duration.toFixed(2)}ms`);
        return duration;
    }
    
    logMetrics() {
        console.table(this.metrics);
    }
}

const perfMonitor = new PerformanceMonitor();
```

### 2.3 Memory Management

**Cleanup old calculations:**

```javascript
class MemoryManager {
    constructor() {
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 60000); // Cleanup every minute
    }
    
    cleanup() {
        // Clear expression cache periodically
        if (expressionCache.cache.size > 500) {
            expressionCache.clear();
            console.log('[MEMORY] Expression cache cleared');
        }
        
        // Clear old field values
        this.cleanupOldFieldValues();
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }
    
    cleanupOldFieldValues() {
        // Remove field values that haven't been accessed recently
        const cutoff = Date.now() - (5 * 60 * 1000); // 5 minutes
        Object.keys(fieldValues).forEach(key => {
            if (fieldValues[key]?.lastAccessed < cutoff) {
                delete fieldValues[key];
            }
        });
    }
    
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
    }
}

const memoryManager = new MemoryManager();
```

## Phase 3: Backend Optimizations (Week 5-6)

### 3.1 Database Query Optimization

**Optimize field loading:**

```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def get_fields_optimized(self):
        """Load fields with minimal queries"""
        # Use read() instead of browse() for better performance
        field_data = self.env['config.matrix.field'].search_read([
            ('template_id', '=', self.id)
        ], [
            'name', 'field_type', 'visibility_condition', 
            'dynamic_default_template', 'sequence'
        ])
        
        return field_data
    
    @api.model
    def get_dependency_graph(self, template_id):
        """Pre-calculate dependency graph on backend"""
        fields = self.env['config.matrix.field'].search([
            ('template_id', '=', template_id)
        ])
        
        dependencies = {}
        for field in fields:
            if field.visibility_condition:
                deps = self._extract_field_references(field.visibility_condition)
                dependencies[field.name] = deps
        
        return dependencies
```

### 3.2 Caching Strategy

**Redis caching for complex calculations:**

```python
import redis
import json

class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    def _get_redis_client(self):
        """Get Redis client for caching"""
        return redis.Redis(
            host=self.env['ir.config_parameter'].get_param('redis.host', 'localhost'),
            port=int(self.env['ir.config_parameter'].get_param('redis.port', 6379)),
            db=int(self.env['ir.config_parameter'].get_param('redis.db', 0))
        )
    
    def evaluate_visibility_with_redis(self, field_values):
        """Evaluate visibility with Redis caching"""
        cache_key = f"visibility:{self.id}:{hash(json.dumps(field_values, sort_keys=True))}"
        
        try:
            redis_client = self._get_redis_client()
            cached_result = redis_client.get(cache_key)
            
            if cached_result is not None:
                return json.loads(cached_result)
            
            # Calculate and cache result
            result = self._evaluate_visibility_uncached(field_values)
            redis_client.setex(cache_key, 300, json.dumps(result))  # 5 minute TTL
            
            return result
            
        except Exception as e:
            _logger.warning(f"Redis cache error: {e}")
            return self._evaluate_visibility_uncached(field_values)
```

## Implementation Steps

### Step 1: Backup and Preparation
```bash
# Backup current system
cp -r canbrax_configmatrix canbrax_configmatrix_backup

# Create performance branch
git checkout -b performance-optimization
```

### Step 2: Implement Phase 1 Changes
1. Add dependency tracking to `static/src/js/configurator.js`
2. Replace `onFieldChange` method
3. Add expression caching
4. Implement debouncing

### Step 3: Test Phase 1
```javascript
// Add to configurator for testing
function runPerformanceTest() {
    const startTime = performance.now();
    
    // Simulate 50 field changes
    for (let i = 0; i < 50; i++) {
        onFieldChange(`test_field_${i}`, `value_${i}`);
    }
    
    const endTime = performance.now();
    console.log(`Performance test: ${endTime - startTime}ms`);
}
```

### Step 4: Deploy and Monitor
1. Deploy to staging environment
2. Run performance tests
3. Monitor memory usage
4. Collect user feedback

### Step 5: Implement Phases 2-3
Continue with advanced optimizations based on Phase 1 results.

## Expected Performance Improvements

### Before Optimization:
- 50 fields × 50 evaluations = 2,500 operations per change
- 500ms+ response time
- Memory leaks over time
- UI blocking during calculations

### After Optimization:
- ~5-10 evaluations per change (dependency tracking)
- <50ms response time (90% improvement)
- Stable memory usage
- Non-blocking UI updates

## Success Metrics

### Performance Targets:
- Field change response time: <100ms
- Memory usage: Stable over 1-hour session
- Cache hit rate: >80%
- User satisfaction: No reported sluggishness

### Monitoring Dashboard:
```javascript
// Add performance dashboard
function showPerformanceDashboard() {
    const dashboard = {
        'Field Changes': perfMonitor.metrics.fieldChanges,
        'Evaluations': perfMonitor.metrics.evaluations,
        'Cache Hit Rate': `${(perfMonitor.metrics.cacheHits / (perfMonitor.metrics.cacheHits + perfMonitor.metrics.cacheMisses) * 100).toFixed(1)}%`,
        'Average Response Time': `${(perfMonitor.metrics.totalTime / perfMonitor.metrics.fieldChanges).toFixed(2)}ms`,
        'Memory Usage': `${(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB`
    };
    
    console.table(dashboard);
}
```

This implementation plan addresses the core performance issues while maintaining the existing functionality. The phased approach allows for testing and validation at each step.