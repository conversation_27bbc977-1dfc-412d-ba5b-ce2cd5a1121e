# Quantity Multiplier Technical Reference

## Overview

This document provides a comprehensive technical reference for the quantity multiplier functionality in the ConfigMatrix system, including the implementation details, the double application issue that was fixed, and best practices for future development.

## System Architecture

### Global Quantity Multiplier Concept

The ConfigMatrix system implements a **global quantity multiplier** that applies to all components in a configuration. This is designed for scenarios where multiple units of the same configuration are needed.

#### Key Characteristics

- **Single Multiplier**: Only one `_quantity` field is supported per configuration
- **Global Application**: The multiplier applies to ALL components in the configuration
- **Formula Isolation**: `_quantity` fields are excluded from formula evaluation context
- **Single Application**: Multiplier is applied only once during BOM generation
- **Default Behavior**: Default multiplier is 1.0 if no `_quantity` field is present

## Implementation Details

### Core Methods

#### 1. `_get_quantity_multiplier(config_values)`

```python
def _get_quantity_multiplier(self, config_values):
    """Get the global quantity multiplier from configuration values"""
    for key, value in config_values.items():
        if key.endswith('_quantity') and isinstance(value, (int, float)) and value > 0:
            return float(value)
    return 1.0
```

**Purpose**: Identifies and extracts the single quantity multiplier from configuration values.

**Parameters**:
- `config_values` (dict): Configuration values dictionary

**Returns**:
- `float`: The quantity multiplier value, or 1.0 if none found

**Logic**:
- Searches for any key ending with `_quantity`
- Validates that the value is a positive number
- Returns the first valid multiplier found
- Defaults to 1.0 if no multiplier is found

#### 2. `_calculate_component_quantity(formula, config_values, default, field_name)`

```python
def _calculate_component_quantity(self, formula, config_values, default=1.0, field_name=None):
    """Calculate component quantity using formula and apply global multiplier"""
    if not formula:
        return default
    
    try:
        # Get global quantity multiplier (single multiplier for all components)
        multiplier = self._get_quantity_multiplier(config_values)
        
        # Create safe evaluation context with configuration values
        # Remove _quantity fields to prevent double application in formulas
        ctx = {k: v for k, v in config_values.items() if not k.endswith('_quantity')}
        
        # Add math functions for formula evaluation
        math_context = {
            'round': round, 'ceil': math.ceil, 'floor': math.floor,
            'abs': abs, 'max': max, 'min': min, 'sum': sum
        }
        ctx.update(math_context)
        
        # Evaluate formula to get base quantity
        base_qty = safe_eval(formula, ctx)
        base_qty = float(base_qty) if base_qty is not None else default
        
        # Apply global multiplier if not 1.0
        if multiplier != 1.0:
            final_qty = base_qty * multiplier
        else:
            final_qty = base_qty
            
        return final_qty
    except Exception as e:
        _logger.error(f"Error evaluating quantity formula '{formula}': {str(e)}")
        return default
```

**Purpose**: Calculates component quantity using formula and applies the global multiplier.

**Parameters**:
- `formula` (str): Python expression for quantity calculation
- `config_values` (dict): Configuration values dictionary
- `default` (float): Default quantity if formula fails
- `field_name` (str): Field name for debugging (optional)

**Returns**:
- `float`: Final calculated quantity with multiplier applied

**Logic**:
1. Gets the global quantity multiplier
2. Creates evaluation context excluding `_quantity` fields
3. Evaluates the formula to get base quantity
4. Applies the global multiplier to the base quantity
5. Returns the final quantity

## The Double Application Issue

### Problem Description

Previously, the system had a critical issue where quantity multipliers were applied twice:

1. **During BOM Generation**: Correctly applied multiplier to component quantities
2. **During BOM Preview**: Incorrectly applied multiplier again to already-multiplied quantities

This caused a "squared" effect:
- Quantity 5 became 25 (5²)
- Quantity 10 became 100 (10²)
- Quantity 2 became 4 (2²)

### Root Cause Analysis

The issue occurred in the `generate_bom_preview` method in `controllers/main.py`:

```python
# PROBLEMATIC CODE (before fix)
for line in bom.bom_line_ids:
    base_qty = line.product_qty  # Already includes multiplier from BOM generation
    # WRONG: Applied multiplier again
    if quantity_multiplier != 1.0:
        adjusted_qty = base_qty * quantity_multiplier  # Double application!
        base_qty = adjusted_qty
    line_price = line.product_id.lst_price * base_qty
```

### Solution Implementation

The fix ensures quantity multipliers are applied only once:

```python
# CORRECTED CODE (after fix)
for line in bom.bom_line_ids:
    # Use BOM line quantity directly (already has multiplier applied)
    base_qty = line.product_qty  # Already includes multiplier from BOM generation
    line_price = line.product_id.lst_price * base_qty
```

### Architecture Flow

#### Correct Flow (After Fix)

1. **Configuration Input**: User provides `bx_dbl_hinge_quantity = 5.0`
2. **BOM Generation**: 
   - `_calculate_component_quantity` applies multiplier once
   - Component A: base 2.0 × 5.0 = 10.0
   - Component B: base 1.0 × 5.0 = 5.0
3. **BOM Storage**: BOM lines stored with correct quantities (10.0, 5.0)
4. **BOM Preview**: Uses stored quantities directly (10.0, 5.0)
5. **Result**: Correct quantities without double application

#### Incorrect Flow (Before Fix)

1. **Configuration Input**: User provides `bx_dbl_hinge_quantity = 5.0`
2. **BOM Generation**: 
   - `_calculate_component_quantity` applies multiplier once
   - Component A: base 2.0 × 5.0 = 10.0
   - Component B: base 1.0 × 5.0 = 5.0
3. **BOM Storage**: BOM lines stored with correct quantities (10.0, 5.0)
4. **BOM Preview**: Applied multiplier again
   - Component A: 10.0 × 5.0 = 50.0 ❌
   - Component B: 5.0 × 5.0 = 25.0 ❌
5. **Result**: Incorrect squared quantities

## Usage Examples

### Basic Usage

```python
# Configuration with global multiplier
config_values = {
    'door_height': 2100,
    'door_width': 900,
    'hinge_count': 3,
    'bx_dbl_hinge_quantity': 5.0  # Global multiplier for all components
}

# Component quantities are calculated as:
# Base quantity (from formula) × Global multiplier
# Hinges: 3 × 5.0 = 15.0
# Frame: 1 × 5.0 = 5.0
# Lock: 1 × 5.0 = 5.0
```

### Formula with Multiplier

```python
# Component with area-based formula
quantity_formula = "height * width / 1000000"  # Convert mm² to m²
config_values = {
    'height': 2000,
    'width': 1000,
    'panel_quantity': 3.0  # Global multiplier
}

# Calculation:
# Base quantity: 2000 * 1000 / 1000000 = 2.0 m²
# Final quantity: 2.0 * 3.0 = 6.0 m²
```

### No Multiplier (Default Behavior)

```python
# Configuration without multiplier
config_values = {
    'door_height': 2100,
    'door_width': 900,
    'hinge_count': 3
    # No _quantity field present
}

# Component quantities use default multiplier of 1.0:
# Hinges: 3 × 1.0 = 3.0
# Frame: 1 × 1.0 = 1.0
# Lock: 1 × 1.0 = 1.0
```

## Debugging and Logging

### Debug Logging Implementation

The system includes comprehensive debug logging to track quantity calculations:

```python
# In _get_quantity_multiplier
_logger.info(f"[QUANTITY_DEBUG] _get_quantity_multiplier called with config_values: {config_values}")
_logger.info(f"[QUANTITY_DEBUG] Found quantity multiplier: {key} = {multiplier}")
_logger.info(f"[QUANTITY_DEBUG] Retrieved multiplier: {multiplier}")

# In _calculate_component_quantity
_logger.info(f"[QUANTITY_DEBUG] _calculate_component_quantity called with:")
_logger.info(f"[QUANTITY_DEBUG] - formula: {formula}")
_logger.info(f"[QUANTITY_DEBUG] - config_values: {config_values}")
_logger.info(f"[QUANTITY_DEBUG] - field_name: {field_name}")
_logger.info(f"[QUANTITY_DEBUG] Retrieved multiplier: {multiplier}")
_logger.info(f"[QUANTITY_DEBUG] Original context: {config_values}")
_logger.info(f"[QUANTITY_DEBUG] Filtered context (removed _quantity fields): {ctx}")
_logger.info(f"[QUANTITY_DEBUG] Converted formula: {converted_formula}")
_logger.info(f"[QUANTITY_DEBUG] Formula evaluation result: {formula} -> base_qty: {base_qty}")
_logger.info(f"[QUANTITY_DEBUG] Applied multiplier {multiplier} to base quantity {base_qty}, result: {final_qty}")
_logger.info(f"[QUANTITY_DEBUG] Final calculated quantity: {final_qty}")

# In BOM Preview
_logger.info(f"[BOM_PREVIEW_DEBUG] BOM lines already have correct quantities (multipliers applied during generation)")
_logger.info(f"[BOM_PREVIEW_DEBUG] Using BOM line quantities directly without additional multiplier")
_logger.info(f"[BOM_PREVIEW_DEBUG] Processing component: {line.product_id.name}")
_logger.info(f"[BOM_PREVIEW_DEBUG] - Quantity from BOM (already includes multiplier): {base_qty}")
```

### Log Analysis

To debug quantity multiplier issues, look for these log patterns:

1. **Multiplier Detection**: `[QUANTITY_DEBUG] Found quantity multiplier:`
2. **Formula Evaluation**: `[QUANTITY_DEBUG] Formula evaluation result:`
3. **Multiplier Application**: `[QUANTITY_DEBUG] Applied multiplier`
4. **BOM Preview**: `[BOM_PREVIEW_DEBUG] Processing component:`

## Best Practices

### Development Guidelines

1. **Always Use Single Multiplier**: Only one `_quantity` field per configuration
2. **Exclude from Formulas**: Never reference `_quantity` fields in quantity formulas
3. **Apply Once**: Apply multiplier only during BOM generation, not in preview
4. **Validate Input**: Ensure multiplier values are positive numbers
5. **Log Debugging**: Include comprehensive logging for troubleshooting

### Testing Guidelines

1. **Test Single Application**: Verify multiplier is applied only once
2. **Test Default Behavior**: Verify default multiplier of 1.0 when no `_quantity` field
3. **Test Formula Isolation**: Verify `_quantity` fields don't affect formula evaluation
4. **Test Edge Cases**: Test with multiplier values of 0, negative numbers, and very large numbers
5. **Test BOM Preview**: Verify BOM preview uses quantities directly without additional multiplier

### Performance Considerations

1. **Efficient Lookup**: The multiplier lookup is O(n) where n is the number of configuration fields
2. **Context Filtering**: Filtering `_quantity` fields from evaluation context is efficient
3. **Caching**: Consider caching multiplier values for frequently accessed configurations
4. **Logging Overhead**: Debug logging can impact performance in production

## Integration Points

### BOM Generation Integration

The quantity multiplier integrates with the BOM generation system:

```python
def _generate_bom_lines(self, bom, config_values):
    """Generate BOM lines with quantity multipliers applied"""
    for section in self.template_id.section_ids:
        for field in section.field_ids:
            if field.component_product_id:
                qty = self._calculate_component_quantity(
                    field.quantity_formula, 
                    config_values, 
                    1.0, 
                    field.technical_name
                )
                # Add BOM line with calculated quantity
```

### BOM Preview Integration

The BOM preview system uses the generated BOM lines directly:

```python
def generate_bom_preview(self, template_id=None, config_data=None, **kw):
    """Generate BOM preview using existing BOM lines"""
    for line in bom.bom_line_ids:
        # Use BOM line quantity directly (already includes multiplier)
        base_qty = line.product_qty
        line_price = line.product_id.lst_price * base_qty
```

### Configuration Saving Integration

The configuration saving system preserves `_quantity` fields:

```python
def save_config(self, **kw):
    """Save configuration including quantity multipliers"""
    values = {}
    for key, value in kw.items():
        if key.startswith('field_'):
            field_id = key.replace('field_', '')
            values[field_id] = value
            # _quantity fields are automatically preserved
```

## Troubleshooting

### Common Issues

1. **Double Application**: If quantities are squared, check for double multiplier application
2. **Missing Multiplier**: If multiplier isn't applied, verify `_quantity` field naming
3. **Formula Errors**: If formulas fail, check for `_quantity` field references in formulas
4. **Performance Issues**: If slow, check for excessive debug logging

### Debugging Steps

1. **Enable Debug Logging**: Set log level to INFO to see quantity debug messages
2. **Check Configuration**: Verify `_quantity` field is present and has valid value
3. **Trace Calculation**: Follow the log messages through the calculation process
4. **Verify BOM Lines**: Check that BOM lines have correct quantities
5. **Test BOM Preview**: Verify BOM preview uses quantities directly

## Future Enhancements

### Potential Improvements

1. **Multiple Multipliers**: Support for different multipliers for different component groups
2. **Conditional Multipliers**: Apply multipliers based on specific conditions
3. **Multiplier Validation**: Add validation rules for multiplier values
4. **Performance Optimization**: Cache multiplier values for better performance
5. **UI Integration**: Add UI controls for setting quantity multipliers

### Backward Compatibility

The current implementation maintains backward compatibility:

- Configurations without `_quantity` fields work as before (multiplier = 1.0)
- Existing quantity formulas continue to work
- BOM generation behavior is unchanged for non-multiplier configurations

## Conclusion

The quantity multiplier system provides a powerful way to handle multiple units of the same configuration while maintaining simplicity and preventing common issues like double application. The implementation follows Odoo best practices and includes comprehensive debugging capabilities for troubleshooting.

The fix for the double application issue ensures that quantity multipliers work correctly and predictably, providing users with accurate component quantities for their configurations.
