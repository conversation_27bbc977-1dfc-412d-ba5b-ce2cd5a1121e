# Enhanced Dynamic Error Messages - Usage Examples

## Overview

Your existing dynamic error message system has been enhanced to support **conditional logic syntax** with ternary operators. This allows for complex, context-aware error messages that change based on multiple conditions.

## New Syntax Support

### Ternary Operator Syntax
```javascript
{condition ? "message1" : (condition2 ? "message2" : "")}
```

### Multiple Nested Conditions
```javascript
{condition1 ? "message1" : (condition2 ? "message2" : (condition3 ? "message3" : "default message"))}
```

## How to Use in the "Use Cases" Tab

### 1. **Basic Conditional Error Message**

In your field's **Use Cases** tab:

**Dynamic Error Message Template:**
```
{height < 700 ? "Height must be at least 700mm" : (height > 3100 ? "Height cannot exceed 3100mm" : "")}
```

**Error Condition:**
```
height < 700 or height > 3100
```

### 2. **Complex Pre-condition Based Messages**

For the small pet door example you showed:

**Dynamic Error Message Template:**
```
{_CALCULATED_pre_condition_1_small_pet_door_custom_height_location_mm == 'yes' ? 'Small Pet Door Height Location must be greater than 200mm and less than Height - 210mm (' + (height - 210) + 'mm)' : (_CALCULATED_pre_condition_2_small_pet_door_custom_height_location_mm == 'yes' ? 'Small Pet Door Height Location must be greater than 232mm and less than Height - 242mm (' + (height - 242) + 'mm)' : '')}
```

**Error Condition:**
```
(_CALCULATED_pre_condition_1_small_pet_door_custom_height_location_mm == 'yes' && (small_pet_door_custom_height_location_mm <= 200 || small_pet_door_custom_height_location_mm >= (height - 210))) || (_CALCULATED_pre_condition_2_small_pet_door_custom_height_location_mm == 'yes' && (small_pet_door_custom_height_location_mm <= 232 || small_pet_door_custom_height_location_mm >= (height - 242)))
```

### 3. **Use Case Specific Messages**

Different messages for different use cases:

**Check Measure Use Case:**
```
{width > 2000 ? "Width " + width + "mm exceeds maximum of 2000mm for standard frames" : (width < 500 ? "Width " + width + "mm is below minimum of 500mm" : "")}
```

**Sales Use Case:**
```
{width > 2000 ? "Consider recommending double door configuration for " + width + "mm width" : (width < 500 ? "This width may require custom manufacturing" : "")}
```

**Online Use Case:**
```
{width > 2000 ? "Width " + width + "mm requires special shipping - contact us for quote" : (width < 500 ? "Custom size - 2-3 week lead time" : "")}
```

### 4. **Dynamic Calculations in Messages**

**Template:**
```
{door_split_type == 'double' ? "Each door panel will be " + round(width / 2) + "mm wide" : (width > 1200 ? "Consider double door for easier operation" : "")}
```

### 5. **Multiple Field Dependencies**

**Template:**
```
{width > 2000 && height > 2500 ? "Large opening detected - recommend reinforced frame (+" + round((width * height) / 10000 * 50) + " cost)" : (width * height > 5000000 ? "Opening area " + round(width * height / 1000000, 1) + "m² requires structural assessment" : "")}
```

## Advanced Examples

### 1. **Frame Size Dependent Messages**

**Template:**
```
{frame_size == 50 ? (height < 700 ? "Minimum height for 50mm frame is 700mm" : (height > 3000 ? "Maximum height for 50mm frame is 3000mm" : "")) : (frame_size == 75 ? (height < 600 ? "Minimum height for 75mm frame is 600mm" : (height > 3200 ? "Maximum height for 75mm frame is 3200mm" : "")) : "")}
```

### 2. **Color and Material Dependencies**

**Template:**
```
{color == 'custom' && material == 'aluminum' ? "Custom aluminum colors require 3-4 week lead time" : (color == 'custom' && material == 'timber' ? "Custom timber stains require 2-3 week lead time" : (color == 'custom' ? "Custom colors available - contact for options" : ""))}
```

### 3. **Quantity Based Pricing Messages**

**Template:**
```
{quantity >= 10 ? "Bulk discount available: " + round((quantity * unit_price * 0.1), 2) + " savings" : (quantity >= 5 ? "Volume pricing available for 5+ units" : (quantity == 1 ? "" : "Consider ordering " + (10 - quantity) + " more for bulk pricing"))}
```

## Best Practices

### 1. **Keep Messages User-Friendly**
- Use clear, actionable language
- Include specific values when helpful
- Provide solutions, not just problems

### 2. **Performance Considerations**
- Avoid overly complex nested conditions
- Use calculated fields for complex logic
- Test with various field combinations

### 3. **Maintenance**
- Document complex conditions
- Use descriptive field names
- Test across all use cases

### 4. **Error Condition Matching**
- Ensure error conditions match template logic
- Use the same field references
- Test edge cases

## Testing Your Dynamic Error Messages

### 1. **In the Backend**
1. Go to your field's **Use Cases** tab
2. Enable **Dynamic Error Message** for the use case
3. Enter your template in **Dynamic Error Template**
4. Set the **Error Condition**
5. Save the field

### 2. **In the Frontend**
1. Open the configurator
2. Change field values to trigger conditions
3. Verify messages appear/disappear correctly
4. Check message content is accurate
5. Test across different use cases

### 3. **Debug Console**
Open browser console to see:
- Template evaluation logs
- Error condition evaluation
- Field value changes
- Calculation updates

## Common Patterns

### 1. **Range Validation with Dynamic Limits**
```javascript
{value < min_value ? "Value must be at least " + min_value : (value > max_value ? "Value cannot exceed " + max_value : "")}
```

### 2. **Conditional Requirements**
```javascript
{required_field == 'yes' && !dependent_field ? "This field is required when " + required_field_label + " is selected" : ""}
```

### 3. **Compatibility Warnings**
```javascript
{option_a == 'selected' && option_b == 'incompatible' ? "Warning: " + option_a + " is not compatible with " + option_b : ""}
```

### 4. **Cost Impact Messages**
```javascript
{premium_option == 'yes' ? "Premium option adds $" + premium_cost + " to total" : (budget_option == 'yes' ? "Budget option saves $" + savings + "" : "")}
```

This enhanced system gives you powerful, flexible error messaging that adapts to your users' specific configurations and use cases.
