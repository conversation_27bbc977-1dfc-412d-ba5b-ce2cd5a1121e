# Sales Fixed Pricing System Guide

## Overview

The **Sales Fixed Pricing** system allows you to create conditional pricing rules that automatically apply fixed amounts (discounts or surcharges) to configurations based on specific conditions. This system runs as a separate step alongside the existing price matrices to provide flexible and dynamic pricing.

## Key Features

- **Conditional Pricing**: Apply fixed prices based on configuration values
- **All Matching Rules**: ALL rules that meet their conditions are applied (not just first match)
- **Flexible Conditions**: Use Python expressions for complex pricing logic
- **Template Integration**: Link pricing rules to specific product templates
- **Separate Step Processing**: Runs independently alongside matrix pricing
- **Automatic Application**: Rules are automatically evaluated during configuration

## How It Works

### 1. Rule Evaluation Process

When a configuration is processed, the system:

1. **Processes all matrix pricing** (mesh, frame, mullion_mohair, plugh)
2. **Processes special condition matrices** (ALL matching matrices)
3. **Fetches all active sales fixed pricing rules** for the template
4. **Evaluates ALL rules** in priority order (lowest number = highest priority)
5. **Applies ALL matching rules** (cumulative pricing)
6. **Adds all fixed prices** to the total configuration price

### 2. Priority System

Rules are evaluated in priority order:
- **Lower priority numbers = Higher priority**
- **Example**: Priority 10 is evaluated before Priority 50
- **ALL matching rules are applied** - multiple rules can contribute to total price

### 3. Condition Evaluation

Conditions use Python expressions with access to:
- **Configuration field values** (e.g., `door_height`, `door_type`)
- **Helper functions** (`len`, `str`, `int`, `float`, `bool`, `min`, `max`, `sum`, `abs`, `round`)
- **Logical operators** (`and`, `or`, `not`)
- **Comparison operators** (`==`, `!=`, `<`, `<=`, `>`, `>=`)

### 4. Cumulative Pricing

Unlike traditional pricing systems that stop at the first match, Sales Fixed Pricing applies **ALL rules that meet their conditions**:

- **Multiple rules can apply** to the same configuration
- **Prices are cumulative** - all matching rule prices are added together
- **Order matters** - rules are evaluated in priority order
- **Transparency** - each applied rule appears in the pricing breakdown

**Example**: If you have rules for "Large Door Discount" (-$50) and "Commercial Customer Discount" (-$100), and both conditions are met, both discounts will be applied for a total of -$150.

## Usage Examples

### Example 1: Large Door Discount

**Rule**: Apply $50 discount for doors with height >= 2000mm

```python
# Condition
door_height >= 2000

# Fixed Price
-50.00

# Priority
50
```

**Result**: When `door_height` is 2000mm or more, a $50 discount is applied.

### Example 2: Commercial Customer Pricing

**Rule**: Apply $100 discount for commercial customers

```python
# Condition
customer_type == 'commercial'

# Fixed Price
-100.00

# Priority
40
```

**Result**: When `customer_type` equals 'commercial', a $100 discount is applied.

### Example 3: Sliding Door Premium

**Rule**: Apply $75 surcharge for sliding doors with midrails

```python
# Condition
door_type == 'sliding' and has_midrail == True

# Fixed Price
75.00

# Priority
60
```

**Result**: When both conditions are true, a $75 surcharge is applied.

### Example 4: Bulk Order Discount

**Rule**: Apply $200 discount for orders with quantity >= 5

```python
# Condition
quantity >= 5

# Fixed Price
-200.00

# Priority
30
```

**Result**: When quantity is 5 or more, a $200 discount is applied.

### Example 5: Express Delivery Surcharge

**Rule**: Apply $150 surcharge for express delivery

```python
# Condition
delivery_type == 'express'

# Fixed Price
150.00

# Priority
70
```

**Result**: When delivery type is 'express', a $150 surcharge is applied.

### Example 6: Cumulative Pricing Scenario

**Configuration**: Commercial customer ordering a large sliding door with express delivery

**Rule 1**: Large Door Discount
```python
# Condition
door_height >= 2000

# Fixed Price
-50.00

# Priority
50
```

**Rule 2**: Commercial Customer Discount
```python
# Condition
customer_type == 'commercial'

# Fixed Price
-100.00

# Priority
40
```

**Rule 3**: Sliding Door Premium
```python
# Condition
door_type == 'sliding'

# Fixed Price
75.00

# Priority
60
```

**Rule 4**: Express Delivery Surcharge
```python
# Condition
delivery_type == 'express'

# Fixed Price
150.00

# Priority
70
```

**Result**: All four rules apply because all conditions are met:
- Large Door Discount: -$50
- Commercial Customer Discount: -$100  
- Sliding Door Premium: +$75
- Express Delivery Surcharge: +$150
- **Total Fixed Pricing**: -$50 + (-$100) + $75 + $150 = +$75

## Creating Fixed Pricing Rules

### Step 1: Access the Sales Fixed Pricing Menu

1. Navigate to **Matrix → Pricing → Sales Fixed Pricing**
2. Click **Create** to add a new rule

### Step 2: Configure Basic Information

- **Name**: Descriptive name for the rule (e.g., "Large Door Discount")
- **Description**: Detailed explanation of when the rule applies
- **Product Template**: Select the product this rule applies to
- **Sequence**: Display order in the list view
- **Priority**: Evaluation priority (lower numbers = higher priority)

### Step 3: Set Pricing and Conditions

- **Fixed Price**: The amount to add/subtract (negative = discount, positive = surcharge)
- **Conditions**: Python expression that determines when the rule applies

### Step 4: Save and Activate

- **Active**: Check to enable the rule
- **Save** the record

## Best Practices

### 1. Priority Management

- **Use priority 10-50** for core business rules
- **Use priority 51-100** for standard rules
- **Use priority 101+** for fallback or special cases
- **Remember**: ALL matching rules are applied, so design conditions carefully to avoid conflicts

### 2. Condition Design

- **Keep conditions simple** and readable
- **Use clear field names** that match your configuration
- **Test conditions** with various configuration values
- **Avoid complex nested logic** when possible
- **Design for cumulative pricing** - consider how rules will work together
- **Use mutually exclusive conditions** when you want only one rule to apply

### 3. Naming Conventions

- **Use descriptive names** that explain the rule's purpose
- **Include the condition** in the name (e.g., "Height >= 2000mm Discount")
- **Use consistent formatting** across all rules

### 4. Testing and Validation

- **Test with various configurations** to ensure rules work correctly
- **Verify priority order** works as expected
- **Check edge cases** and boundary conditions
- **Validate business logic** with stakeholders
- **Test cumulative scenarios** to ensure multiple rules work together correctly
- **Verify no conflicting rules** apply simultaneously when they shouldn't

## Integration with Existing Systems

### 1. Price Matrix Integration

Sales Fixed Pricing works alongside existing price matrices:
- **Price matrices** provide base pricing based on dimensions
- **Special condition matrices** provide additional matrix pricing
- **Sales fixed pricing rules** add conditional adjustments as separate step
- **Total price** = Matrix pricing + Special condition pricing + Fixed pricing adjustments

### 2. Configuration System Integration

Rules automatically evaluate during configuration:
- **Field values** are available for condition evaluation
- **Calculated fields** can be referenced in conditions
- **Real-time updates** as configuration changes

### 3. Sales Order Integration

Fixed pricing is applied when:
- **Creating sales orders** from configurations
- **Calculating line item prices**
- **Generating quotes** and proposals

## Advanced Features

### 1. Complex Conditions

You can create sophisticated pricing logic:

```python
# Multiple field conditions
door_height >= 2000 and door_width >= 1200 and door_type == 'sliding'

# Range conditions
800 <= width <= 1200 and height >= 1500

# Enumeration conditions
door_type in ['sliding', 'bi-fold'] and has_midrail == True

# Mathematical conditions
(door_height * door_width) >= 2400000  # Area >= 2.4m²
```

### 2. Helper Functions

Use built-in helper functions for calculations:

```python
# String operations
len(customer_name) > 10

# Mathematical operations
abs(door_height - 2000) <= 100  # Within 100mm of 2000mm

# Type conversions
float(width) >= 800.0
```

### 3. Dynamic Pricing

Create rules that adapt to configuration values:

```python
# Percentage-based adjustments
door_height >= 2500 and door_width >= 1500  # Large door premium

# Quantity-based pricing
quantity >= 10  # Bulk order discount

# Customer-specific pricing
customer_type == 'wholesale' and order_value >= 5000
```

## Troubleshooting

### Common Issues

1. **Rule Not Applying**
   - Check if rule is active
   - Verify condition syntax
   - Ensure field names match configuration
   - Check priority order
   - Remember: ALL matching rules are applied, not just the first one

2. **Incorrect Pricing**
   - Verify fixed price value (negative = discount)
   - Check condition logic
   - Test with sample data
   - Check if multiple rules are applying when only one should
   - Verify cumulative pricing calculations

3. **Cumulative Pricing Issues**
   - Check if multiple rules are applying unexpectedly
   - Verify conditions are mutually exclusive when needed
   - Review rule priorities and logic
   - Test with different configuration combinations

4. **Performance Issues**
   - Limit number of rules per template
   - Use simple conditions when possible
   - Consider rule priority carefully

### Debugging Tips

1. **Test conditions individually** with sample data
2. **Use simple conditions first**, then add complexity
3. **Check field names** in your configuration template
4. **Verify data types** (string vs integer vs boolean)
5. **Test edge cases** and boundary conditions
6. **Test cumulative scenarios** with multiple rules
7. **Check pricing breakdown** to see which rules are applying
8. **Use mutually exclusive conditions** when you want only one rule to apply

## Security Considerations

### 1. Input Validation

- **Conditions are validated** for security
- **Dangerous patterns** are blocked (e.g., `import`, `exec`, `eval`)
- **Field access is restricted** to configuration values only

### 2. Access Control

- **Admin users** can create and modify rules
- **Regular users** can view rules but not modify
- **Portal users** cannot access pricing rules

### 3. Data Protection

- **Company isolation** ensures rules only apply to your company
- **Template restrictions** limit rule scope
- **Audit trail** tracks rule changes

## Future Enhancements

### Planned Features

1. **Rule Templates**: Pre-built rule patterns for common scenarios
2. **Bulk Operations**: Import/export rules from Excel
3. **Advanced Conditions**: More sophisticated logic operators
4. **Performance Optimization**: Caching and rule compilation
5. **Analytics**: Rule usage and effectiveness reporting

### Customization Options

1. **Custom Helper Functions**: Add business-specific logic
2. **Rule Inheritance**: Create rule hierarchies
3. **Conditional Fields**: Dynamic field visibility based on rules
4. **Integration APIs**: Connect with external pricing systems

## Conclusion

The Sales Fixed Pricing system provides a powerful and flexible way to implement complex pricing rules in your ConfigMatrix system. With its unique cumulative pricing approach and separate step processing, it offers unprecedented flexibility for creating sophisticated pricing strategies.

### Key Benefits

- **Cumulative Pricing**: ALL matching rules are applied, allowing for complex pricing scenarios
- **Separate Step Processing**: Runs independently alongside matrix pricing for maximum flexibility
- **Transparent Breakdown**: Each applied rule appears in the pricing breakdown for easy debugging
- **Priority Control**: Fine-grained control over rule evaluation order
- **Flexible Conditions**: Support for complex Python expressions

By following the best practices outlined in this guide, you can create effective pricing strategies that automatically adapt to your customers' needs and business requirements while maintaining transparency and control over your pricing logic.

For additional support or questions, please refer to the main ConfigMatrix documentation or contact your system administrator.
