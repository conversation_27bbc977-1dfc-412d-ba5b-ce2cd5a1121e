# Sales Fixed Pricing Implementation Guide

## Overview

The **Sales Fixed Pricing** system is a conditional pricing rule system that provides an additional pricing layer for all components in the ConfigMatrix system. It allows administrators to define fixed pricing rules that apply when specific conditions are met, running as a separate step alongside regular pricing calculations.

## Architecture

### Core Components

#### 1. Sales Fixed Pricing Model (`config.matrix.sales.fixed.price`)
- **Purpose**: Stores conditional fixed pricing rules for configurable products
- **Integration**: Links to configuration templates and evaluates conditions dynamically
- **Separate Step**: Runs independently alongside matrix pricing

#### 2. Template Integration
- **Location**: Integrated into `config.matrix.template.get_configuration_price()`
- **Trigger**: Always runs as separate step after matrix pricing processing
- **Flow**: Evaluates conditions and applies fixed pricing as additional cost layer

#### 3. Pricing Flow Integration
```
Regular Grid Pricing → Special Condition Matrix → Sales Fixed Pricing (Separate Step)
     ↓                        ↓                        ↓
Grid lookup success    ALL matching matrices    Fixed pricing always runs
     ↓                        ↓                        ↓
Use grid price         Use all matching matrices    Sum all matching rules
     ↓                        ↓                        ↓
     └────────────────────────┼────────────────────────┘
                              ↓
                    Combined Pricing Result
```

## Data Model

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `sequence` | Integer | Display order (default: 10) | No |
| `active` | Boolean | Active status (default: True) | No |
| `template_id` | Many2one | Configuration template reference | Yes |
| `description` | Text | Detailed description of the pricing rule | No |
| `value_cost` | Float | Fixed price amount (digits: Product Price) | No |
| `condition` | Char | Primary condition expression (JavaScript syntax) | No |
| `code` | Char | Legacy condition field (fallback) | No |
| `company_id` | Many2one | Company reference (default: current company) | No |
| `currency_id` | Many2one | Currency (related to company) | No |

### Key Features

#### 1. Dual Condition Fields
- **`condition`**: Primary field for JavaScript expressions
- **`code`**: Legacy field for backward compatibility
- **Priority**: `condition` field takes precedence over `code`

#### 2. Template Association
- **Many-to-One**: Each fixed pricing rule links to one template
- **Scope**: Rules only apply to their assigned template
- **Isolation**: No cross-template pricing interference

#### 3. Sequence-Based Priority
- **Ordering**: Rules processed by `sequence` field (ascending)
- **All Matches**: ALL rules with met conditions are applied
- **Cumulative**: Multiple matching rules contribute to total price

## Condition Evaluation System

### JavaScript to Python Conversion

The system automatically converts JavaScript expressions to Python syntax using the existing `ConfigMatrixCalculatedField` conversion logic:

#### Supported JavaScript Syntax
```javascript
// Comparison operators
door_height >= 2000 && door_type === 'sliding'
width > 1000 || height > 2000

// Math functions
Math.max(width, height) > 1500
Math.min(width, height) < 800

// Boolean values
true && (condition1 || condition2)
!false && active === true

// Number functions
Number(value) || 0
parseFloat(height) > 1000
```

#### Converted Python Syntax
```python
# Comparison operators
door_height >= 2000 and door_type == 'sliding'
width > 1000 or height > 2000

# Math functions
max(width, height) > 1500
min(width, height) < 800

# Boolean values
True and (condition1 or condition2)
not False and active == True

# Number functions
float(value) or 0
float(height) > 1000
```

### Evaluation Context

The system provides a rich evaluation context with:

#### 1. Configuration Values
```python
# All configuration field values are available as variables
ctx = {
    'door_height': 2000,
    'door_width': 1000,
    'door_type': 'sliding',
    'active': True,
    # ... all other field values
}
```

#### 2. Math Functions
```python
math_context = {
    'round': round,
    'ceil': math.ceil,
    'floor': math.floor,
    'abs': abs,
    'max': max,
    'min': min,
    'sum': sum,
    'sqrt': math.sqrt
}
```

#### 3. JavaScript Compatibility
```python
js_compatibility = {
    'parseFloat': float,
    'parseInt': int,
    'Number': float,
    'Math': Math object with max, min, abs, ceil, floor, sqrt
}
```

#### 4. Boolean Values
```python
bool_context = {
    'true': True,
    'false': False,
    'True': True,
    'False': False
}
```

## Integration with Pricing System

### Complete Pricing Flow

#### 1. Matrix Pricing Processing
```python
# Process all configured price matrices (mesh, frame, mullion_mohair, plugh)
for config in price_matrix_configs:
    price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier * panel_quantity
    if price:
        total_price += price
        # Add to breakdown
```

#### 2. Special Condition Matrix Evaluation (ALL Matches)
```python
# Process ALL matrices that meet special conditions
matching_plugh_matrices = []
for matrix in plugh_sale_price_matrices:
    if matrix.evaluate_special_conditions(configuration_values):
        matching_plugh_matrices.append(matrix)
        # Each matching matrix gets its own breakdown entry
```

#### 3. Sales Fixed Pricing (Separate Step)
```python
# Sales fixed pricing always runs as separate step
sales_fixed_pricing_result = self._get_sales_fixed_pricing(configuration_values)
if sales_fixed_pricing_result['fixed_pricing_applied']:
    total_price += sales_fixed_pricing_result['total_price'] * final_quantity_multiplier * panel_quantity
    # Add individual fixed pricing entries to breakdown
```

### Sales Fixed Pricing Method

#### `_get_sales_fixed_pricing()`

**Purpose**: Retrieves and evaluates sales fixed pricing rules for all components

**Process**:
1. **Search**: Find active sales fixed pricing entries for the template
2. **Order**: Sort by sequence (ascending)
3. **Evaluate**: Check conditions for each entry
4. **Accumulate**: Sum prices of ALL matching rules
5. **Return**: Structured result with breakdown

**Return Structure**:
```python
{
    'fixed_pricing_applied': bool,
    'total_price': float,
    'breakdown': [
        {
            'id': int,
            'description': str,
            'price': float,
            'condition': str
        }
    ],
    'currency': record or None
}
```

## User Interface

### Views

#### 1. List View
- **Fields**: Sequence, Template, Description, Fixed Price, Condition
- **Features**: Drag-and-drop reordering, monetary formatting
- **Sorting**: Default order by sequence

#### 2. Form View
- **Layout**: Clean, organized structure
- **Fields**: Template selection, sequence, pricing, conditions
- **Validation**: Real-time condition validation
- **Help**: Contextual help text for condition fields

#### 3. Search View
- **Filters**: Active/Archived status
- **Grouping**: By template
- **Search**: By description, template, condition

### Menu Structure

```
ConfigMatrix
└── Pricing
    ├── Operations Fixed Pricing (renamed from "Fixed Price Tables")
    └── Sales Fixed Pricing (separate step pricing)
```

## Security and Validation

### Input Validation

#### 1. Security Constraints
```python
@api.constrains('condition')
def _check_condition_security(self):
    """Validate additional condition for security"""
    dangerous_patterns = [
        'import', 'exec', 'eval', '__', 'open', 'file',
        'system', 'subprocess', 'os.', 'sys.', 'globals'
    ]
    # Check for dangerous patterns and raise ValidationError
```

#### 2. Safe Evaluation
- **Context Isolation**: Limited function access
- **No Builtins**: Restricted Python execution environment
- **Error Handling**: Graceful fallback on evaluation errors

### Access Control

#### 1. Model Access Rights
```csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_config_matrix_sales_fixed_pricing_admin,config.matrix.sales.fixed.price.admin,model_config_matrix_sales_fixed_price,group_config_matrix_admin,1,1,1,1
access_config_matrix_sales_fixed_pricing_user,config.matrix.sales.fixed.price.user,model_config_matrix_sales_fixed_price,group_config_matrix_user,1,0,0,0
```

#### 2. Record Rules
- **Template Scoping**: Users can only access rules for their accessible templates
- **Company Isolation**: Multi-company support with proper isolation

## Usage Examples

### Example 1: Basic Height-Based Pricing

#### Configuration
```python
{
    'template_id': 1,
    'description': 'Tall door surcharge',
    'value_cost': 50.00,
    'condition': 'door_height >= 2000'
}
```

#### Result
- **Applies when**: Door height is 2000mm or greater
- **Price**: $50.00 additional surcharge
- **Use case**: Premium pricing for tall doors

### Example 2: Complex Condition with Multiple Fields

#### Configuration
```python
{
    'template_id': 1,
    'description': 'Premium sliding door package',
    'value_cost': 75.00,
    'condition': 'door_type === "sliding" && door_width >= 1200 && door_height >= 2000'
}
```

#### Result
- **Applies when**: Sliding door with width ≥1200mm and height ≥2000mm
- **Price**: $75.00 additional surcharge
- **Use case**: Premium package pricing for large sliding doors

### Example 3: Mathematical Expression

#### Configuration
```python
{
    'template_id': 1,
    'description': 'Size-based surcharge',
    'value_cost': 25.00,
    'condition': 'Math.max(door_width, door_height) > 1500'
}
```

#### Result
- **Applies when**: Largest dimension exceeds 1500mm
- **Price**: $25.00 additional surcharge
- **Use case**: Handling surcharge for oversized doors

## Best Practices

### 1. Condition Design

#### ✅ Good Practices
```javascript
// Clear, readable conditions
door_height >= 2000
door_type === 'sliding' && door_width >= 1200
Math.max(width, height) > 1500

// Use descriptive field names
product_category === 'premium'
is_custom_size === true
```

#### ❌ Avoid These
```javascript
// Overly complex conditions
(a && b) || (c && d) || (e && f) || (g && h)

// Unclear field references
field_123 === 'value'
temp_var > 1000

// Hard-coded magic numbers without context
value > 42
```

### 2. Pricing Strategy

#### ✅ Good Practices
- **Logical grouping**: Group related conditions together
- **Clear descriptions**: Use descriptive names for pricing rules
- **Reasonable values**: Set prices that make business sense
- **Sequence ordering**: Order rules from most specific to most general

#### ❌ Avoid These
- **Overlapping conditions**: Multiple rules that could apply simultaneously
- **Unclear pricing**: Vague descriptions of what the price covers
- **Extreme values**: Prices that are too high or too low
- **Poor sequencing**: Rules in random or illogical order

### 3. Template Organization

#### ✅ Good Practices
- **Template-specific rules**: Create rules for specific product types
- **Logical grouping**: Organize rules by product category or feature
- **Clear naming**: Use descriptive names for templates
- **Regular review**: Periodically review and update pricing rules

#### ❌ Avoid These
- **Global rules**: Rules that apply to all templates
- **Mixed categories**: Rules for different product types in same template
- **Unclear associations**: Templates with unclear purposes
- **Outdated rules**: Rules that are no longer relevant

## Troubleshooting

### Common Issues

#### 1. Conditions Not Evaluating

**Symptoms**: Fixed pricing rules never apply
**Causes**:
- Syntax errors in condition expressions
- Field names don't match configuration values
- JavaScript syntax not properly converted

**Solutions**:
```python
# Check condition syntax
print(f"Original condition: {rule.condition}")
print(f"Converted Python: {rule.env['config.matrix.calculated.field']._convert_js_to_python(rule.condition)}")

# Verify field names
print(f"Available fields: {list(configuration_values.keys())}")
```

#### 2. Multiple Rules Applying

**Symptoms**: Multiple fixed pricing rules apply when only one should
**Causes**:
- Overlapping conditions
- Incorrect sequence ordering
- Logic errors in condition expressions

**Solutions**:
```python
# Review rule sequence
rules = self.env['config.matrix.sales.fixed.price'].search([
    ('template_id', '=', template_id),
    ('active', '=', True)
], order='sequence ASC')

# Check for overlapping conditions
for rule in rules:
    print(f"Rule {rule.sequence}: {rule.condition}")
```

#### 3. Performance Issues

**Symptoms**: Slow pricing calculations
**Causes**:
- Too many fixed pricing rules
- Complex condition expressions
- Inefficient field lookups

**Solutions**:
```python
# Limit active rules
active_rules = self.env['config.matrix.sales.fixed.price'].search([
    ('template_id', '=', template_id),
    ('active', '=', True)
], limit=50)  # Limit to reasonable number

# Simplify conditions
# Instead of: Math.max(width, height) > 1500 && Math.min(width, height) < 800
# Use: (width > 1500 or height > 1500) and (width < 800 or height < 800)
```

### Debugging Tools

#### 1. Logging
```python
# Enable detailed logging
_logger.setLevel(logging.DEBUG)

# Check evaluation results
_logger.info(f"Evaluating condition: {condition}")
_logger.info(f"Configuration values: {configuration_values}")
_logger.info(f"Evaluation result: {result}")
```

#### 2. Condition Testing
```python
# Test condition evaluation
def test_condition(condition, test_values):
    """Test condition evaluation with sample values"""
    try:
        python_condition = self.env['config.matrix.calculated.field']._convert_js_to_python(condition)
        ctx = dict(test_values)
        # Add helper functions
        ctx.update(math_context)
        ctx.update(bool_context)
        
        result = safe_eval(python_condition, ctx)
        return result
    except Exception as e:
        print(f"Error: {e}")
        return False

# Test with sample data
test_values = {
    'door_height': 2000,
    'door_width': 1000,
    'door_type': 'sliding'
}
result = test_condition('door_height >= 2000 && door_type === "sliding"', test_values)
print(f"Test result: {result}")
```

## Future Enhancements

### Planned Features

#### 1. Advanced Condition Builder
- **Visual editor**: Drag-and-drop condition builder
- **Field picker**: Dropdown selection of available fields
- **Operator selection**: Predefined comparison operators
- **Validation**: Real-time condition validation

#### 2. Pricing Analytics
- **Usage tracking**: Monitor which rules are applied most
- **Performance metrics**: Track evaluation performance
- **Rule effectiveness**: Analyze pricing rule success rates
- **Optimization suggestions**: AI-powered rule optimization

#### 3. Bulk Operations
- **Mass import**: Excel/CSV import of pricing rules
- **Template copying**: Copy rules between templates
- **Batch updates**: Update multiple rules simultaneously
- **Rule templates**: Predefined rule templates for common scenarios

#### 4. Advanced Conditions
- **Date-based**: Time-sensitive pricing rules
- **Customer-based**: Customer-specific pricing
- **Quantity-based**: Volume discount rules
- **Seasonal**: Time-of-year pricing adjustments

### Integration Opportunities

#### 1. Sales Order Integration
- **Real-time pricing**: Apply fixed pricing during sales order creation
- **Quote generation**: Include fixed pricing in customer quotes
- **Approval workflows**: Require approval for high-value fixed pricing
- **Customer communication**: Explain fixed pricing to customers

#### 2. Manufacturing Integration
- **Cost calculation**: Include fixed pricing in manufacturing costs
- **BOM pricing**: Apply fixed pricing to bill of materials
- **Production planning**: Consider fixed pricing in production planning
- **Quality control**: Validate fixed pricing during quality checks

#### 3. Reporting and Analytics
- **Pricing reports**: Comprehensive pricing analysis
- **Margin analysis**: Impact of fixed pricing on margins
- **Customer profitability**: Customer-specific pricing analysis
- **Trend analysis**: Pricing trends over time

## Conclusion

The Sales Fixed Pricing system provides a powerful and flexible way to implement conditional pricing rules for all components in the ConfigMatrix system. By running as a separate step alongside matrix pricing and providing a robust condition evaluation system, it enables administrators to create sophisticated pricing strategies that respond dynamically to product configuration choices.

### Key Implementation Changes

1. **Separate Step Processing**: Sales fixed pricing now runs independently as a separate step after matrix pricing, not as a fallback mechanism.

2. **All Matching Rules**: The system processes ALL sales fixed pricing rules that meet their conditions, not just the first match.

3. **Comprehensive Integration**: Works alongside all matrix types (mesh, frame, mullion_mohair, plugh) and special condition matrices.

4. **Individual Breakdown Entries**: Each matching rule gets its own breakdown entry for better transparency and debugging.

The system's architecture ensures:
- **Reliability**: Robust error handling and graceful degradation
- **Performance**: Efficient condition evaluation and caching
- **Security**: Safe expression evaluation with input validation
- **Flexibility**: Support for complex JavaScript-like conditions
- **Integration**: Seamless integration with existing pricing workflows
- **Transparency**: Clear breakdown of all pricing components

With proper implementation and following best practices, the Sales Fixed Pricing system can significantly enhance the ConfigMatrix pricing capabilities while maintaining system stability and performance.
