# Odoo 18 Development Guidelines for ConfigMatrix

## Overview

This document provides comprehensive guidelines for Odoo 18 development standards, syntax changes, and compliance requirements specifically for the ConfigMatrix module. It covers all the critical changes from Odoo 16 to Odoo 18 and ensures proper implementation.

## Critical Odoo 18 Changes

### 1. XML View Structure Changes

#### Tree → List View Migration
```xml
<!-- ❌ Odoo 16 (DEPRECATED) -->
<tree string="Configuration Templates">
    <field name="name"/>
    <field name="code"/>
    <field name="state"/>
</tree>

<!-- ✅ Odoo 18 (CORRECT) -->
<list string="Configuration Templates">
    <field name="name"/>
    <field name="code"/>
    <field name="state"/>
</list>
```

#### Direct Attributes Instead of attrs
```xml
<!-- ❌ Odoo 16 (DEPRECATED) -->
<field name="advanced_field" attrs="{'invisible': [('state', '=', 'draft')]}"/>

<!-- ✅ Odoo 18 (CORRECT) -->
<field name="advanced_field" invisible="state == 'draft'"/>
```

#### Escaped Comparison Operators
```xml
<!-- ❌ Odoo 16 (DEPRECATED) -->
<field name="price" invisible="price < 100"/>

<!-- ✅ Odoo 18 (CORRECT) -->
<field name="price" invisible="price &lt; 100"/>
```

### 2. Translation Performance Optimization

#### Use self.env._() Instead of _()
```python
# ❌ Odoo 16 (DEPRECATED)
from odoo.tools.translate import _

class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def action_activate(self):
        message = _("Template activated successfully")
        return self.env['ir.actions.act_window'].message_post(body=message)

# ✅ Odoo 18 (CORRECT)
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def action_activate(self):
        message = self.env._("Template activated successfully")
        return self.env['ir.actions.act_window'].message_post(body=message)
```

### 3. Button Attributes

#### Proper Button Configuration
```xml
<!-- ❌ Odoo 16 (DEPRECATED) -->
<button name="action_configure" string="Configure" type="object"/>

<!-- ✅ Odoo 18 (CORRECT) -->
<button name="action_configure" string="Configure" type="object" class="btn-primary"/>
```

### 4. Widget Options Quoting

#### Single Quotes for Widget Options
```xml
<!-- ❌ Odoo 16 (DEPRECATED) -->
<field name="field_values" widget="json" options='{"mode": "json"}'/>

<!-- ✅ Odoo 18 (CORRECT) -->
<field name="field_values" widget="json" options="{'mode': 'json'}"/>
```

## Model Development Standards

### 1. Field Definitions

#### Proper Field Types and Attributes
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Configuration Matrix Template'
    
    # ✅ CORRECT - Use proper field types and attributes
    name = fields.Char(
        string='Template Name',
        required=True,
        tracking=True,
        help="Name of the configuration template"
    )
    
    code = fields.Char(
        string='Template Code',
        required=True,
        tracking=True,
        help="Unique code for the template"
    )
    
    # ✅ CORRECT - Use selection_add for extending existing selections
    state = fields.Selection(
        selection_add=[
            ('draft', 'Draft'),
            ('testing', 'Testing'),
            ('active', 'Active'),
            ('archived', 'Archived')
        ],
        default='draft',
        tracking=True,
        ondelete={
            'draft': 'set draft',
            'testing': 'set draft',
            'archived': 'set draft'
        }
    )
    
    # ✅ CORRECT - Use computed fields with proper dependencies
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(
                len(section.field_ids) for section in template.section_ids
            )
    
    field_count = fields.Integer(
        string='Field Count',
        compute='_compute_field_count',
        store=True
    )
```

### 2. Method Implementation

#### Proper Method Signatures and Error Handling
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    _description = 'Configuration Matrix Field'
    
    # ✅ CORRECT - Use proper method signatures
    def evaluate_visibility(self, field_values=None, use_case='sales'):
        """Evaluate field visibility based on conditions and use case"""
        self.ensure_one()
        
        if not field_values:
            field_values = {}
        
        if not self.visibility_condition:
            return True
        
        try:
            # Create safe context for evaluation
            safe_context = {
                'values': field_values,
                'field_values': field_values,
                'config_values': field_values,
            }
            
            # Add field values as individual variables
            for key, value in field_values.items():
                safe_context[key] = value
            
            # Evaluate the condition
            result = safe_eval(self.visibility_condition, safe_context)
            return bool(result)
        except Exception as e:
            _logger.error(f"Error evaluating visibility condition: {e}")
            return True
    
    # ✅ CORRECT - Use proper error handling
    def validate_value(self, value, field_values=None, use_case='sales'):
        """Validate field value against rules"""
        self.ensure_one()
        
        if not field_values:
            field_values = {}
        
        # Check required field
        if self.required and not value:
            return False, self.env._("This field is required")
        
        # Check range validation
        if self.min_value:
            try:
                min_val = safe_eval(self.min_value, {
                    'values': field_values,
                    'field_values': field_values,
                })
                if value < min_val:
                    return False, self.env._(f"Value must be at least {min_val}")
            except Exception as e:
                _logger.error(f"Error evaluating min value: {e}")
        
        if self.max_value:
            try:
                max_val = safe_eval(self.max_value, {
                    'values': field_values,
                    'field_values': field_values,
                })
                if value > max_val:
                    return False, self.env._(f"Value must be at most {max_val}")
            except Exception as e:
                _logger.error(f"Error evaluating max value: {e}")
        
        return True, None
```

### 3. Constraints and Validation

#### SQL Constraints and Python Constraints
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    # ✅ CORRECT - Use SQL constraints for data integrity
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!'),
        ('unique_name', 'unique(name)', 'Template name must be unique!')
    ]
    
    # ✅ CORRECT - Use Python constraints for complex validation
    @api.constrains('product_template_id')
    def _check_product_template(self):
        for template in self:
            if template.product_template_id and not template.product_template_id.is_configurable:
                raise ValidationError(
                    self.env._("Selected product template must be configurable")
                )
    
    @api.constrains('section_ids')
    def _check_sections(self):
        for template in self:
            if not template.section_ids:
                raise ValidationError(
                    self.env._("Template must have at least one section")
                )
```

## View Development Standards

### 1. Form Views

#### Proper Form Structure
```xml
<!-- ✅ CORRECT - Odoo 18 form view structure -->
<form string="Configuration Template">
    <header>
        <button name="action_activate" 
                string="Activate" 
                type="object" 
                class="btn-primary"
                invisible="state != 'draft'"/>
        <button name="action_test" 
                string="Test" 
                type="object" 
                class="btn-secondary"
                invisible="state != 'draft'"/>
        <button name="action_archive" 
                string="Archive" 
                type="object" 
                class="btn-warning"
                invisible="state == 'archived'"/>
        <field name="state" widget="statusbar" 
               statusbar_visible="draft,testing,active"/>
    </header>
    
    <sheet>
        <div class="oe_title">
            <h1>
                <field name="name" placeholder="Template Name"/>
            </h1>
        </div>
        
        <group>
            <group>
                <field name="code"/>
                <field name="product_template_id"/>
                <field name="use_case"/>
            </group>
            <group>
                <field name="field_count"/>
                <field name="active"/>
            </group>
        </group>
        
        <notebook>
            <page string="Sections" name="sections">
                <field name="section_ids">
                    <list>
                        <field name="name"/>
                        <field name="sequence" widget="handle"/>
                        <field name="field_count"/>
                    </list>
    </field>
            </page>
            
            <page string="Pricing" name="pricing">
                <field name="price_matrix_ids">
                    <list>
                        <field name="name"/>
                        <field name="category_id"/>
                        <field name="active"/>
                    </list>
    </field>
            </page>
        </notebook>
    </sheet>
</form>
```

### 2. List Views

#### Proper List Structure
```xml
<!-- ✅ CORRECT - Odoo 18 list view structure -->
<list string="Configuration Templates" 
      default_order="name asc"
      create="true"
      delete="true">
    <field name="name"/>
    <field name="code"/>
    <field name="product_template_id"/>
    <field name="state" widget="badge" 
           decoration-info="state == 'draft'"
           decoration-warning="state == 'testing'"
           decoration-success="state == 'active'"
           decoration-muted="state == 'archived'"/>
    <field name="field_count"/>
    <field name="active"/>
</list>
```

### 3. Search Views

#### Proper Search Structure
```xml
<!-- ✅ CORRECT - Odoo 18 search view structure -->
<search string="Configuration Templates">
    <field name="name"/>
    <field name="code"/>
    <field name="product_template_id"/>
    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
    <filter string="Testing" name="testing" domain="[('state', '=', 'testing')]"/>
    <filter string="Active Templates" name="active_templates" 
            domain="[('state', '=', 'active')]"/>
    <group expand="0" string="Group By">
        <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
        <filter string="Use Case" name="group_use_case" context="{'group_by': 'use_case'}"/>
        <filter string="Product Template" name="group_product" 
                context="{'group_by': 'product_template_id'}"/>
    </group>
</search>
```

## JavaScript Development Standards

### 1. OWL Component Registration

#### Proper Widget Registration
```javascript
// ✅ CORRECT - Use object with component property
import { registry } from "@web/core/registry";
import { ConfigMatrixConfigurator } from "./configurator";

registry.category("fields").add("config_matrix_widget", {
    component: ConfigMatrixConfigurator,
    supportedTypes: ["many2one"],
});

// ❌ INCORRECT - Direct class registration causes OWL errors
registry.category("fields").add("config_matrix_widget", ConfigMatrixConfigurator);
```

### 2. Component Structure

#### Proper OWL Component Implementation
```javascript
/** @odoo-module **/
import { Component, useState, onMounted, useRef } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class ConfigMatrixConfigurator extends Component {
    static template = "canbrax_configmatrix.Configurator";
    static props = {
        record: { type: Object, optional: true },
        fieldName: { type: String, optional: true },
        templateId: { type: Number, optional: true },
        configurationId: { type: Number, optional: true },
        useCase: { type: String, optional: true },
        onSave: { type: Function, optional: true },
        onCancel: { type: Function, optional: true },
    };
    
    setup() {
        // ✅ CORRECT - Use useState for reactive state
        this.state = useState({
            loading: false,
            error: false,
            fieldValues: {},
            calculatedValues: {},
            visibleFields: new Set(),
            validationErrors: {},
        });
        
        // ✅ CORRECT - Use service hooks
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.rootRef = useRef("root");
        
        // ✅ CORRECT - Use debouncing for performance
        this.debouncedUpdate = this.debounce(this.updateCalculatedFields, 300);
        this.debouncedVisibility = this.debounce(this.updateVisibility, 200);
        
        onMounted(() => {
            this.initializeConfiguration();
        });
    }
    
    // ✅ CORRECT - Implement proper debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
```

### 3. Template Structure

#### Proper XML Template Structure
```xml
<!-- ✅ CORRECT - Odoo 18 template structure -->
<template id="Configurator" name="ConfigMatrix Configurator">
    <div class="config-matrix-configurator" t-ref="root">
        <!-- Loading State -->
        <div t-if="state.loading" class="config-loading">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
                </div>
                </div>
        
        <!-- Error State -->
        <div t-if="state.error" class="config-error alert alert-danger">
            <i class="fa fa-exclamation-triangle"/> Error loading configuration
            </div>

        <!-- Configuration Form -->
        <div t-if="!state.loading and !state.error" class="config-form">
            <div class="config-sections">
                <t t-foreach="template.sections" t-as="section" t-key="section.id">
                    <div class="config-section">
                        <h3 class="section-title">
                            <t t-esc="section.name"/>
                        </h3>
                        
                        <div class="section-fields">
                            <t t-foreach="section.fields" t-as="field" t-key="field.id">
                                <div t-if="state.visibleFields.has(field.id)" 
                                     class="field-container">
                                    <label class="form-label" t-att-for="field.technical_name">
                                        <t t-esc="field.name"/>
                                        <span t-if="field.required" class="text-danger">*</span>
                                    </label>
                                    
                                    <ConfigMatrixField 
                                        field="field"
                                        value="state.fieldValues[field.technical_name]"
                                        error="state.validationErrors[field.technical_name]"
                                        onChange="(fieldId, value) => this.onFieldChange(fieldId, value)"/>
                                    
                                    <div t-if="field.help_text" class="form-text">
                                        <t t-esc="field.help_text"/>
            </div>
        </div>
    </t>
                        </div>
                    </div>
                </t>
            </div>
            
            <!-- Action Buttons -->
            <div class="config-actions">
                <button type="button" 
                        class="btn btn-secondary" 
                        t-on-click="() => this.cancelConfiguration()">
                    Cancel
                </button>
                <button type="button" 
                        class="btn btn-primary" 
                        t-on-click="() => this.saveConfiguration()"
                        t-att-disabled="state.loading">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
</template>
```

## Controller Development Standards

### 1. Route Definitions

#### Proper Route Configuration
```python
from odoo import http
from odoo.http import request
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixController(http.Controller):
    
    # ✅ CORRECT - Use proper route decorators
    @http.route('/config_matrix/get_template', type='json', auth='user', csrf=True)
    def get_template(self, template_id, use_case='sales'):
        """Get configuration template with fields and options"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            
            if not template.exists():
                return {'error': 'Template not found'}
            
            # Get template data with sections and fields
            template_data = template.get_template_data(use_case=use_case)
            return template_data
        except Exception as e:
            _logger.error(f"Error getting template: {e}")
            return {'error': str(e)}
    
    # ✅ CORRECT - Use proper error handling
    @http.route('/config_matrix/evaluate_visibility', type='json', auth='user', csrf=True)
    def evaluate_visibility(self, field_id, field_values, use_case='sales'):
        """Evaluate field visibility based on conditions"""
        try:
            field = request.env['config.matrix.field'].browse(field_id)
            
            if not field.exists():
                return {'visible': True}
            
            visible = field.evaluate_visibility(field_values, use_case=use_case)
            return {'visible': visible}
        except Exception as e:
            _logger.error(f"Error evaluating visibility: {e}")
            return {'visible': True}
```

### 2. Security Implementation

#### Proper Access Control
```python
class ConfigMatrixController(http.Controller):
    
    def _check_access_rights(self, template_id=None):
        """Check access rights for configuration operations"""
        user = request.env.user
        
        # Check if user has access to ConfigMatrix
        if not user.has_group('canbrax_configmatrix.group_config_matrix_user'):
            return False
        
        # Check template access if specified
        if template_id:
            template = request.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return False
            
            # Only admins can access draft templates
            if template.state == 'draft' and not user.has_group('canbrax_configmatrix.group_config_matrix_admin'):
                return False
        
        return True
    
    @http.route('/config_matrix/get_template', type='json', auth='user', csrf=True)
    def get_template(self, template_id, use_case='sales'):
        """Get configuration template with access control"""
        if not self._check_access_rights(template_id):
            return {'error': 'Access denied'}
        
        # ... rest of implementation
```

## Security Standards

### 1. Expression Validation

#### Safe Expression Evaluation
```python
import ast
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    def validate_expression(self, expression, allowed_vars=None):
        """Validate expression for security"""
        if not expression:
            return True, None
        
        # Check for dangerous patterns
        dangerous_patterns = [
            'import', 'exec', 'eval', '__', 'globals', 'locals',
            'open', 'file', 'system', 'subprocess', 'os.', 'sys.'
        ]
        
        for pattern in dangerous_patterns:
            if pattern in expression.lower():
                return False, f"Dangerous pattern '{pattern}' not allowed"
        
        # Validate syntax
        try:
            ast.parse(expression)
        except SyntaxError as e:
            return False, f"Invalid syntax: {e}"
        
        return True, None
    
    def safe_eval(self, expression, context):
        """Safely evaluate expression with restricted context"""
        # Validate expression first
        valid, error = self.validate_expression(expression)
        if not valid:
            raise ValueError(error)
        
        # Create safe context
        safe_context = {
            'abs': abs,
            'min': min,
            'max': max,
            'round': round,
            'len': len,
            'str': str,
            'int': int,
            'float': float,
            'bool': bool,
            'sum': sum,
            'pow': pow,
        }
        
        # Add allowed variables
        if context:
            safe_context.update(context)
        
        # Evaluate with restricted globals
        return eval(expression, {'__builtins__': {}}, safe_context)
```

### 2. Record Rules

#### Proper Access Control Rules
```xml
<!-- ✅ CORRECT - Record rules for access control -->
<record id="config_matrix_template_rule_user" model="ir.rule">
    <field name="name">ConfigMatrix: users can only access active templates</field>
    <field name="model_id" ref="model_config_matrix_template"/>
    <field name="domain_force">[('state', 'in', ['testing', 'active'])]</field>
    <field name="groups" eval="[(4, ref('canbrax_configmatrix.group_config_matrix_user'))]"/>
</record>

<record id="config_matrix_template_rule_admin" model="ir.rule">
    <field name="name">ConfigMatrix: admins can access all templates</field>
    <field name="model_id" ref="model_config_matrix_template"/>
    <field name="domain_force">[(1, '=', 1)]</field>
    <field name="groups" eval="[(4, ref('canbrax_configmatrix.group_config_matrix_admin'))]"/>
</record>

<record id="config_matrix_configuration_rule_user" model="ir.rule">
    <field name="name">ConfigMatrix: users can only access their configurations</field>
    <field name="model_id" ref="model_config_matrix_configuration"/>
    <field name="domain_force">[('create_uid', '=', user.id)]</field>
    <field name="groups" eval="[(4, ref('canbrax_configmatrix.group_config_matrix_user'))]"/>
</record>
```

## Performance Standards

### 1. Caching Implementation

#### Proper Caching Strategies
```python
from odoo import tools
import hashlib
import json

class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    @tools.ormcache('self.id', 'values_hash')
    def evaluate_visibility_cached(self, values_hash, field_values):
        """Cached visibility evaluation for performance"""
        return self._evaluate_visibility_uncached(field_values)
    
    def _evaluate_visibility_uncached(self, field_values):
        """Evaluate field visibility based on conditions"""
        if not self.visibility_condition:
            return True
        
        try:
            # Create safe context for evaluation
            safe_context = {
                'values': field_values,
                'field_values': field_values,
                'config_values': field_values,
            }
            
            # Add field values as individual variables
            for key, value in field_values.items():
                safe_context[key] = value
            
            # Evaluate the condition
            result = safe_eval(self.visibility_condition, safe_context)
            return bool(result)
        except Exception as e:
            _logger.error(f"Error evaluating visibility condition: {e}")
            return True
    
    def evaluate_visibility(self, field_values, use_case='sales'):
        """Evaluate visibility with caching"""
        # Create hash for caching
        values_str = json.dumps(field_values, sort_keys=True)
        values_hash = hashlib.md5(values_str.encode()).hexdigest()
        
        return self.evaluate_visibility_cached(values_hash, field_values)
```

### 2. Database Optimization

#### Proper Query Optimization
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def get_template_with_sections(self, template_id):
        """Get template with optimized queries"""
        template = self.env['config.matrix.template'].with_context(
            prefetch_fields=True
        ).browse(template_id)
        
        # Prefetch related records for performance
        template.section_ids.mapped('field_ids')
        template.section_ids.mapped('field_ids.option_ids')
        template.section_ids.mapped('field_ids.component_mapping_ids')
        
        return template
    
    def get_template_data(self, use_case='sales'):
        """Get template data with optimized queries"""
        self.ensure_one()
        
        # Use optimized queries
        sections = self.section_ids.with_context(prefetch_fields=True)
        fields = sections.mapped('field_ids').with_context(prefetch_fields=True)
        options = fields.mapped('option_ids')
        
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'use_case': use_case,
            'sections': [{
                'id': section.id,
                'name': section.name,
                'sequence': section.sequence,
                'fields': [{
                    'id': field.id,
                    'name': field.name,
                    'technical_name': field.technical_name,
                    'field_type': field.field_type,
                    'required': field.required,
                    'default_value': field.default_value,
                    'help_text': field.help_text,
                    'options': [{
                        'id': option.id,
                        'name': option.name,
                        'value': option.value,
                    } for option in field.option_ids],
                } for field in section.field_ids],
            } for section in sections],
        }
```

## Testing Standards

### 1. Unit Test Implementation

#### Proper Test Structure
```python
from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class TestConfigMatrixTemplate(TransactionCase):
    
    def setUp(self):
        super().setUp()
        # Create test data
        self.product_template = self.env['product.template'].create({
            'name': 'Test Product',
            'is_configurable': True,
        })
        
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'product_template_id': self.product_template.id,
            'use_case': 'sales',
        })
    
    def test_template_creation(self):
        """Test template creation with proper validation"""
        self.assertEqual(self.template.name, 'Test Template')
        self.assertEqual(self.template.code, 'TEST001')
        self.assertEqual(self.template.state, 'draft')
        self.assertTrue(self.template.active)
    
    def test_template_activation(self):
        """Test template activation workflow"""
        # Test activation
        self.template.action_activate()
        self.assertEqual(self.template.state, 'active')
        
        # Test archiving
        self.template.action_archive()
        self.assertEqual(self.template.state, 'archived')
    
    def test_field_visibility(self):
        """Test field visibility conditions"""
        # Create section and field
        section = self.env['config.matrix.section'].create({
            'name': 'Test Section',
            'matrix_id': self.template.id,
        })
        
        field = self.env['config.matrix.field'].create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': section.id,
            'visibility_condition': 'test_value == "visible"',
        })
        
        # Test visibility evaluation
        self.assertTrue(field.evaluate_visibility({'test_value': 'visible'}))
        self.assertFalse(field.evaluate_visibility({'test_value': 'hidden'}))
    
    def test_constraints(self):
        """Test model constraints"""
        # Test unique code constraint
        with self.assertRaises(ValidationError):
            self.env['config.matrix.template'].create({
                'name': 'Duplicate Template',
                'code': 'TEST001',  # Duplicate code
                'product_template_id': self.product_template.id,
            })
```

### 2. Integration Test Implementation

#### Proper Integration Testing
```python
class TestConfigMatrixIntegration(TransactionCase):
    
    def test_sales_integration(self):
        """Test complete sales workflow integration"""
        # Create complete test setup
        template = self.create_test_template()
        configuration = template.generate_configuration({
            'door_type': 'sliding',
            'width': 1000,
            'height': 2000,
        })
        
        # Create sales order
        customer = self.env['res.partner'].create({
            'name': 'Test Customer'
        })
        
        sale_order = self.env['sale.order'].create({
            'partner_id': customer.id,
        })
        
        sale_line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': template.product_template_id.product_variant_id.id,
            'configuration_id': configuration.id,
        })
        
        # Test integration
        self.assertEqual(sale_line.configuration_id, configuration)
        self.assertIsNotNone(configuration.bom_id)
        self.assertGreater(configuration.total_price, 0)
```

## Migration Guidelines

### 1. From Odoo 16 to Odoo 18

#### XML View Migration
```bash
# Migration script for XML views
find . -name "*.xml" -exec sed -i 's/<tree>/<list>/g' {} \;
find . -name "*.xml" -exec sed -i 's/<\/tree>/<\/list>/g' {} \;
```

#### Python Code Migration
```python
# Migration script for Python code
# Replace _() with self.env._()
# Update field definitions
# Update method signatures
```

### 2. Testing Migration

#### Migration Testing
```python
def test_migration_compatibility(self):
    """Test migration compatibility"""
    # Test all views render correctly
    # Test all methods work as expected
    # Test all constraints are enforced
    # Test all security rules work
```

## Best Practices Summary

### 1. Code Organization
- Follow Odoo 18 standards strictly
- Use proper field types and attributes
- Implement proper error handling
- Use computed fields for derived values

### 2. Performance
- Use caching for expensive operations
- Optimize database queries
- Implement proper indexing
- Monitor performance metrics

### 3. Security
- Validate all user inputs
- Use safe expression evaluation
- Implement proper access control
- Log security events

### 4. Testing
- Write comprehensive unit tests
- Test integration scenarios
- Validate migration compatibility
- Test security controls

### 5. Documentation
- Document all public APIs
- Provide usage examples
- Keep documentation updated
- Include troubleshooting guides

This document provides comprehensive guidelines for Odoo 18 development in the ConfigMatrix module. Follow these standards to ensure compatibility, performance, and maintainability.
