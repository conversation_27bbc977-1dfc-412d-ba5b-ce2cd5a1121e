# Manufacturing User Guide for Configurable Products

## Overview

This guide explains how to use the Manufacturing Order creation feature for configurable products in the CanBrax ConfigMatrix system. The system automatically creates Manufacturing Orders with the correct BOMs when you confirm Sale Orders containing configured products.

## Prerequisites

### Product Setup Requirements

Before using manufacturing with configurable products, ensure:

1. **Product Configuration**:
   - Product must be marked as `is_configurable = True`
   - Product type should be `Consumable` for optimal performance
   - Product must have both routes assigned:
     - ✅ **Replenish on Order (MTO)**
     - ✅ **Manufacture**

2. **Warehouse Configuration**:
   - Warehouse must have `manufacture_to_resupply = True`
   - Manufacturing routes must be active

3. **Template Setup**:
   - ConfigMatrix template must be properly configured
   - Component mappings must be defined
   - BOM generation rules must be set up

## Step-by-Step Process

### Step 1: Product Configuration
```
User opens product configurator → Selects options → Saves configuration ✅
System generates BOM automatically → BOM includes selected components ✅
```

**What happens internally**:
- Configuration saved with `save_config()`
- BOM generated with `config.generate_bom()`
- BOM linked to configuration record

### Step 2: Sale Order Creation
```
User creates Sale Order → Adds configured product → Sets quantity ✅
System validates configuration → Checks BOM availability ✅
```

**Important Notes**:
- Use the configured product variant, not the base product
- Ensure the configuration is complete before adding to sale order
- Quantity can be any amount (BOM will scale accordingly)

### Step 3: Sale Order Confirmation
```
User clicks "Confirm" → System validates all configurable products ✅
Validation passes → Sale Order state changes to "Sale Order" ✅
Manufacturing Order created automatically → Uses configuration BOM ✅
```

**Validation Process**:
- System checks all configurable products have valid configurations
- System verifies BOMs exist for all required products
- If validation fails, confirmation is blocked with error message

### Step 4: Manufacturing Order Processing
```
Manufacturing Order created → Status: "Confirmed" ✅
Components reserved → Work orders generated ✅
Production can begin → Follow standard MRP workflow ✅
```

## User Interface Elements

### Sale Order Line View

When viewing a sale order line with a configurable product:

- **Configuration**: Shows linked configuration ID
- **BOM**: Displays the configuration-specific BOM
- **Manufacturing Orders**: Lists related MOs (after confirmation)

### Manufacturing Order View

The created Manufacturing Order shows:

- **Product**: The configured product
- **BOM**: Configuration-specific BOM (not default product BOM)
- **Origin**: Links back to the Sale Order
- **Components**: All components from the configuration
- **Sale Order Line**: Direct link to originating sale line

### Configuration View

In the configuration record:

- **Generated BOM**: Shows the automatically created BOM
- **Sale Order Lines**: Lists all sale lines using this configuration
- **Manufacturing Orders**: Shows MOs created from this configuration

## Workflow Examples

### Example 1: Simple Door Configuration

1. **Configure Product**:
   - Product: "Custom Door"
   - Options: Wood Type = Oak, Handle = Brass, Size = 80x200cm
   - Save configuration → BOM generated with Oak panel, Brass handle, etc.

2. **Create Sale Order**:
   - Add "Custom Door" with saved configuration
   - Quantity: 5 units
   - Confirm order

3. **Result**:
   - Manufacturing Order created for 5 doors
   - BOM includes: 5x Oak panels, 5x Brass handles, etc.
   - Components automatically reserved

### Example 2: Complex Multi-Product Order

1. **Multiple Configurations**:
   - 3x Custom Door (Oak/Brass)
   - 2x Custom Door (Pine/Chrome)
   - 1x Custom Window (Double-glazed)

2. **Sale Order Confirmation**:
   - System validates all 3 configurations
   - Creates 3 separate Manufacturing Orders
   - Each MO uses its specific configuration BOM

3. **Production Planning**:
   - MOs can be scheduled independently
   - Components reserved per configuration
   - Work orders generated for each variant

## Troubleshooting

### Common Issues and Solutions

#### Issue: "Missing BOM for configurable product"
**Cause**: Configuration doesn't have a generated BOM
**Solution**: 
1. Open the configuration record
2. Check if BOM was generated
3. If missing, re-save the configuration
4. Verify component mappings are correct

#### Issue: Manufacturing Order not created
**Cause**: Product missing required routes
**Solution**:
1. Go to Product → Inventory tab
2. Ensure both routes are selected:
   - ✅ Replenish on Order (MTO)
   - ✅ Manufacture
3. Save product and retry

#### Issue: Wrong BOM used in Manufacturing Order
**Cause**: System using default product BOM instead of configuration BOM
**Solution**:
1. Check configuration has valid BOM
2. Verify BOM is linked to configuration
3. Contact system administrator if issue persists

#### Issue: Sale Order confirmation blocked
**Cause**: Validation failing for one or more configurable products
**Solution**:
1. Read error message carefully
2. Check each configurable product has valid configuration
3. Ensure all configurations have generated BOMs
4. Fix issues and retry confirmation

### Validation Error Messages

#### "Cannot confirm Sale Order [SO_NAME] due to missing BOMs"
- **Meaning**: One or more configurable products lack BOMs
- **Action**: Check each product's configuration and regenerate BOMs if needed

#### "Product [PRODUCT_NAME] requires configuration but none found"
- **Meaning**: Configurable product added without configuration
- **Action**: Configure the product or use non-configurable variant

#### "Configuration [CONFIG_ID] has no BOM generated"
- **Meaning**: Configuration exists but BOM generation failed
- **Action**: Re-save configuration or check component mappings

## Best Practices

### For Sales Users

1. **Always Configure First**: Complete product configuration before adding to sale orders
2. **Verify Configuration**: Check that configuration shows expected components
3. **Test Small Orders**: Start with single-item orders to verify setup
4. **Monitor Manufacturing**: Check that MOs are created after confirmation

### For Production Users

1. **Check BOM Details**: Verify MO uses correct configuration BOM
2. **Component Availability**: Ensure all configured components are in stock
3. **Work Order Sequence**: Follow standard MRP workflow for configured products
4. **Quality Control**: Verify finished product matches configuration

### For Administrators

1. **Route Configuration**: Ensure all configurable products have correct routes
2. **Warehouse Setup**: Verify manufacturing settings are enabled
3. **Template Maintenance**: Keep configuration templates up to date
4. **Performance Monitoring**: Monitor BOM generation and MO creation times

## Integration with Other Modules

### Sales Module
- Sale orders automatically validate configurable products
- Sale order lines show configuration and BOM information
- Delivery dates consider manufacturing lead times

### Manufacturing Module
- Manufacturing Orders use configuration-specific BOMs
- Work orders generated based on configured components
- Production planning considers configuration complexity

### Inventory Module
- Stock moves link Manufacturing Orders to Sale Orders
- Component reservations based on configuration BOMs
- Traceability from raw materials to finished configured products

### Accounting Module
- Cost calculations use configuration-specific BOMs
- Manufacturing costs allocated per configuration
- Profitability analysis per product configuration

## Reporting and Analytics

### Available Reports

1. **Manufacturing Orders by Configuration**: Shows MOs grouped by configuration
2. **BOM Usage Analysis**: Tracks which BOMs are used most frequently
3. **Configuration Profitability**: Analyzes profit margins per configuration
4. **Production Efficiency**: Measures manufacturing performance by configuration type

### Key Metrics

- **Configuration-to-MO Time**: Time from configuration save to MO creation
- **BOM Accuracy**: Percentage of MOs using correct configuration BOMs
- **Manufacturing Lead Time**: Average time from MO creation to completion
- **Component Utilization**: Usage rates of different configured components

## Support and Maintenance

### Regular Maintenance Tasks

1. **BOM Validation**: Periodically verify all configurations have valid BOMs
2. **Route Verification**: Check product routes are correctly configured
3. **Performance Review**: Monitor manufacturing order creation times
4. **Data Cleanup**: Archive old configurations and BOMs as needed

### Getting Help

For technical issues:
1. Check this user guide first
2. Review error messages carefully
3. Contact system administrator
4. Provide specific error details and steps to reproduce

For process improvements:
1. Document current workflow challenges
2. Suggest specific enhancements
3. Provide business case for changes
4. Work with development team on solutions
