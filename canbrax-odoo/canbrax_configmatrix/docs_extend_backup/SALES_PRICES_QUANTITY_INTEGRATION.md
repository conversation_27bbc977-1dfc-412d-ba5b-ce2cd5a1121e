# Sales Prices Quantity Integration - Technical Documentation

## Overview

This document describes the integration of quantity multiplication in sales prices calculations within the ConfigMatrix system. The implementation ensures that sales prices scale correctly with the quantity specified in the configuration, matching the behavior of operation costs.

## Implementation Details

### Template Method: `get_configuration_price`

**Location**: `canbrax_configmatrix/models/config_matrix_template.py`

**Purpose**: Get total price for a configuration using assigned price matrices with quantity multiplier

```python
def get_configuration_price(self, configuration_values):
    """Get total price for a configuration using assigned price matrices and field-specific matrices

    Args:
        configuration_values: Dict of field technical names to values

    Returns:
        dict: {
            'price': float,
            'currency_id': int,
            'breakdown': [{
                'matrix_name': str,
                'price': float,
                'dimensions': {'height': float, 'width': float},
                'field_name': str (optional)
            }]
        }
    """
    self.ensure_one()
    _logger.info(f"[PRICE_MATRIX] get_configuration_price called for template {self.id} ({self.name})")
    _logger.info(f"[PRICE_MATRIX] Configuration values: {configuration_values}")

    total_price = 0.0
    breakdown = []
    currency = self.env.company.currency_id
    panel_quantity = self.panel_quantity and int(self.panel_quantity) or 1
    
    # Get the dynamic quantity multiplier from configuration values (like operation costs do)
    quantity_multiplier = self._get_quantity_multiplier(configuration_values)
    _logger.info(f"[PRICE_MATRIX] Using quantity multiplier: {quantity_multiplier} (from configuration) and panel_quantity: {panel_quantity} (from template)")
    
    # Use the dynamic quantity multiplier for pricing (same as operation costs)
    final_quantity_multiplier = quantity_multiplier
    
    # Define price matrices with their corresponding dimension fields
    price_matrix_configs = []
    if self.mesh_price_grid_id:
        price_matrix_configs.append({
            'matrix': self.mesh_price_grid_id,
            'height_field': self.door_height_field_id.name,
            'width_field': self.door_width_field_id.name,
            'type': 'mesh'
        })
    if self.frame_price_grid_id:
        price_matrix_configs.append({
            'matrix': self.frame_price_grid_id,
            'height_field': self.door_height_field_id.name,
            'width_field': self.door_width_field_id.name,
            'type': 'frame'
        })
    if self.mulion_mohair_price_grid_id:
        price_matrix_configs.append({
            'matrix': self.mulion_mohair_price_grid_id,
            'height_field': self.door_height_field_id.name,
            'width_field': self.door_width_field_id.name,
            'type': 'mulion_mohair'
        })
    if self.plugh_price_grid_id:
        price_matrix_configs.append({
            'matrix': self.plugh_price_grid_id,
            'height_field': self.door_height_field_id.name,
            'width_field': self.door_width_field_id.name,
            'type': 'plugh'
        })
    
    if not price_matrix_configs:
        _logger.info(f"[PRICE_MATRIX] No price matrix found for template {self.id}")
        return {
            'price': 0.0,
            'currency_id': currency.id,
            'breakdown': []
        }

    # Process each price matrix configuration
    for config in price_matrix_configs:
        price_matrix = config['matrix']
        height_field = config['height_field']
        width_field = config['width_field']
        matrix_type = config['type']
        
        _logger.info(f"[PRICE_MATRIX] Processing {matrix_type} matrix: {price_matrix.name} (ID: {price_matrix.id})")
        _logger.info(f"[PRICE_MATRIX] Using height field: {height_field}, width field: {width_field}")
        
        # Skip if required fields are not configured
        if not height_field or not width_field:
            _logger.info(f"[PRICE_MATRIX] Skipping {matrix_type} matrix - missing dimension fields")
            continue
        
        height = self._extract_dimension_value(configuration_values, [height_field], 'height')
        width = self._extract_dimension_value(configuration_values, [width_field], 'width')
        _logger.info(f"[PRICE_MATRIX] Extracted dimensions for {matrix_type} - Height: {height}, Width: {width}")
        
        if not height or not width:
            _logger.info(f"[PRICE_MATRIX] Missing dimensions for {matrix_type} - Height: {height}, Width: {width}")
            continue

        # Get price from matrix using dimensions
        price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier
        _logger.info(f"[PRICE_MATRIX] Price from {matrix_type} matrix: {price} (base price * quantity multiplier {final_quantity_multiplier})")

        # SPECIAL HANDLING FOR PLUGH SPECIAL CONDITION MATRIX: Check for fixed pricing fallback
        if matrix_type == 'plugh_extra' and not price:
            _logger.info(f"[PRICE_MATRIX] Grid lookup failed for plugh special condition matrix, checking fixed pricing fallback")
            
            # Get fixed pricing for this template
            fixed_pricing_result = self._get_plugh_fixed_pricing_fallback(configuration_values)
            
            if fixed_pricing_result['fixed_pricing_applied']:
                # Use fixed pricing instead of grid pricing
                price = fixed_pricing_result['total_price'] * final_quantity_multiplier
                _logger.info(f"[PRICE_MATRIX] Using fixed pricing for plugh special condition matrix: {price} (base price * quantity multiplier {final_quantity_multiplier})")
                
                # Add fixed pricing breakdown entries
                for fixed_entry in fixed_pricing_result['breakdown']:
                    breakdown.append({
                        'matrix_name': f"Fixed Pricing - {fixed_entry['description']}",
                        'price': fixed_entry['price'] * final_quantity_multiplier,
                        'dimensions': {
                            'height': height,
                            'width': width
                        },
                        'matrix_type': 'plugh_extra_fixed',
                        'height_field': height_field,
                        'width_field': width_field,
                        'fixed_pricing_entry': fixed_entry['id'],
                        'pricing_source': 'fixed_pricing'
                    })
                
                # Use fixed pricing currency if available
                if fixed_pricing_result['currency']:
                    currency = fixed_pricing_result['currency']
                    _logger.info(f"[PRICE_MATRIX] Using fixed pricing currency: {currency.name}")
                
                # Continue to next iteration since we've handled pricing
                continue

        if price:
            total_price += price
            breakdown.append({
                'matrix_name': price_matrix.name,
                'price': price,
                'dimensions': {
                    'height': height,
                    'width': width
                },
                'matrix_type': matrix_type,
                'height_field': height_field,
                'width_field': width_field,
                'pricing_source': 'price_matrix'
            })
            _logger.info(f"[PRICE_MATRIX] Price from {matrix_type} matrix {price_matrix.name}: {price} for height={height}, width={width}")
        else:
            _logger.info(f"[PRICE_MATRIX] No price found for {matrix_type} matrix dimensions: height={height}, width={width}")

        # Use matrix currency if available
        if price_matrix.currency_id:
            currency = price_matrix.currency_id
            _logger.info(f"[PRICE_MATRIX] Using {matrix_type} matrix currency: {currency.name}")

    _logger.info(f"[PRICE_MATRIX] Final result - Total price: {total_price}, Currency: {currency.name}")
    _logger.info(f"[PRICE_MATRIX] Breakdown: {breakdown}")

    return {
        'price': total_price,
        'currency_id': currency.id,
        'breakdown': breakdown
    }
```

### Quantity Multiplier Method

**Location**: `canbrax_configmatrix/models/config_matrix_template.py`

```python
def _get_quantity_multiplier(self, config_values):
    """Get the global quantity multiplier from configuration values (same logic as operation costs)
    
    Args:
        config_values: Dict of field technical names to values
        
    Returns:
        float: Quantity multiplier (default 1.0)
    """
    for key, value in config_values.items():
        if key.endswith('_quantity'):
            try:
                # Handle both string and numeric values
                if isinstance(value, str):
                    # Convert string to float
                    numeric_value = float(value)
                elif isinstance(value, (int, float)):
                    numeric_value = float(value)
                else:
                    continue
                
                if numeric_value > 0:
                    return numeric_value
            except (ValueError, TypeError):
                # Skip invalid values
                continue
    return 1.0
```

## Price Matrix Types and Quantity Application

### 1. Mesh Price Matrix

**Configuration**: `mesh_price_grid_id`

```python
if self.mesh_price_grid_id:
    price_matrix_configs.append({
        'matrix': self.mesh_price_grid_id,
        'height_field': self.door_height_field_id.name,
        'width_field': self.door_width_field_id.name,
        'type': 'mesh'
    })
```

**Price Calculation**:
```python
# Get price from matrix using dimensions
price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier
_logger.info(f"[PRICE_MATRIX] Price from mesh matrix: {price} (base price * quantity multiplier {final_quantity_multiplier})")
```

### 2. Frame Price Matrix

**Configuration**: `frame_price_grid_id`

```python
if self.frame_price_grid_id:
    price_matrix_configs.append({
        'matrix': self.frame_price_grid_id,
        'height_field': self.door_height_field_id.name,
        'width_field': self.door_width_field_id.name,
        'type': 'frame'
    })
```

**Price Calculation**:
```python
# Get price from matrix using dimensions
price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier
_logger.info(f"[PRICE_MATRIX] Price from frame matrix: {price} (base price * quantity multiplier {final_quantity_multiplier})")
```

### 3. Mullion Mohair Price Matrix

**Configuration**: `mulion_mohair_price_grid_id`

```python
if self.mulion_mohair_price_grid_id:
    price_matrix_configs.append({
        'matrix': self.mulion_mohair_price_grid_id,
        'height_field': self.door_height_field_id.name,
        'width_field': self.door_width_field_id.name,
        'type': 'mulion_mohair'
    })
```

**Price Calculation**:
```python
# Get price from matrix using dimensions
price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier
_logger.info(f"[PRICE_MATRIX] Price from mulion_mohair matrix: {price} (base price * quantity multiplier {final_quantity_multiplier})")
```

### 4. Plugh Price Matrix

**Configuration**: `plugh_price_grid_id`

```python
if self.plugh_price_grid_id:
    price_matrix_configs.append({
        'matrix': self.plugh_price_grid_id,
        'height_field': self.door_height_field_id.name,
        'width_field': self.door_width_field_id.name,
        'type': 'plugh'
    })
```

**Price Calculation**:
```python
# Get price from matrix using dimensions
price = price_matrix.get_price_for_dimensions(height, width) * final_quantity_multiplier
_logger.info(f"[PRICE_MATRIX] Price from plugh matrix: {price} (base price * quantity multiplier {final_quantity_multiplier})")
```

## Fixed Pricing Fallback Integration

### Fixed Pricing with Quantity Multiplier

When grid lookup fails for plugh components, the system falls back to fixed pricing:

```python
# SPECIAL HANDLING FOR PLUGH SPECIAL CONDITION MATRIX: Check for fixed pricing fallback
if matrix_type == 'plugh_extra' and not price:
    _logger.info(f"[PRICE_MATRIX] Grid lookup failed for plugh special condition matrix, checking fixed pricing fallback")
    
    # Get fixed pricing for this template
    fixed_pricing_result = self._get_plugh_fixed_pricing_fallback(configuration_values)
    
    if fixed_pricing_result['fixed_pricing_applied']:
        # Use fixed pricing instead of grid pricing
        price = fixed_pricing_result['total_price'] * final_quantity_multiplier
        _logger.info(f"[PRICE_MATRIX] Using fixed pricing for plugh special condition matrix: {price} (base price * quantity multiplier {final_quantity_multiplier})")
        
        # Add fixed pricing breakdown entries
        for fixed_entry in fixed_pricing_result['breakdown']:
            breakdown.append({
                'matrix_name': f"Fixed Pricing - {fixed_entry['description']}",
                'price': fixed_entry['price'] * final_quantity_multiplier,
                'dimensions': {
                    'height': height,
                    'width': width
                },
                'matrix_type': 'plugh_extra_fixed',
                'height_field': height_field,
                'width_field': width_field,
                'fixed_pricing_entry': fixed_entry['id'],
                'pricing_source': 'fixed_pricing'
            })
        
        # Use fixed pricing currency if available
        if fixed_pricing_result['currency']:
            currency = fixed_pricing_result['currency']
            _logger.info(f"[PRICE_MATRIX] Using fixed pricing currency: {currency.name}")
        
        # Continue to next iteration since we've handled pricing
        continue
```

## Controller Integration

### API Endpoint: `get_price_from_matrix`

**Location**: `canbrax_configmatrix/controllers/main.py`

```python
@http.route('/config_matrix/get_price_from_matrix', type='json', auth='public', website=True)
def get_price_from_matrix(self, template_id=None, config_data=None, **kw):
    """Get price from price matrix based on configuration data"""
    _logger.info(f"[PRICE_MATRIX] get_price_from_matrix called with template_id={template_id}")
    
    try:
        if not template_id:
            return {'success': False, 'error': 'No template ID provided'}
        
        # Parse configuration data
        if config_data:
            try:
                configuration_values = json.loads(config_data)
            except Exception as e:
                _logger.error(f"[PRICE_MATRIX] Error parsing config_data: {str(e)}")
                return {'success': False, 'error': f'Invalid configuration data: {str(e)}'}
        else:
            configuration_values = {}
        
        # Get template
        template = request.env['config.matrix.template'].sudo().browse(int(template_id))
        if not template.exists():
            return {'success': False, 'error': 'Template not found'}

        # Calculate all calculated fields first
        calc_field_model = request.env['config.matrix.calculated.field']
        calculated_results = calc_field_model.calculate_fields(configuration_values, template.id)
        configuration_values.update(calculated_results)

        _logger.info(f"[PRICE_MATRIX] Configuration values: {configuration_values}")

        # Use the template's get_configuration_price method
        result = template.get_configuration_price(configuration_values)
        
        if not result or result.get('price', 0) == 0:
            _logger.info(f"[PRICE_MATRIX] No price found for configuration")
            return {'success': False, 'error': 'No price found for this configuration'}

        _logger.info(f"[PRICE_MATRIX] Total price found: {result['price']}")

        return {
            'success': True,
            'price': result['price'],
            'currency_id': result.get('currency_id'),
            'breakdown': result.get('breakdown', []),
            'sale_prices': result.get('breakdown', [])  # Add sale_prices for compatibility
        }
        
    except Exception as e:
        _logger.error(f"[PRICE_MATRIX] Error getting price from matrix: {str(e)}")
        import traceback
        _logger.error(f"[PRICE_MATRIX] Traceback: {traceback.format_exc()}")
        return {'success': False, 'error': str(e)}
```

## Response Format

### Success Response

```json
{
    "success": true,
    "price": 191.84,
    "currency_id": 1,
    "breakdown": [
        {
            "matrix_name": "Mesh Price Matrix",
            "price": 95.92,
            "dimensions": {
                "height": 2500,
                "width": 1000
            },
            "matrix_type": "mesh",
            "height_field": "door_height",
            "width_field": "door_width",
            "pricing_source": "price_matrix"
        },
        {
            "matrix_name": "Frame Price Matrix",
            "price": 95.92,
            "dimensions": {
                "height": 2500,
                "width": 1000
            },
            "matrix_type": "frame",
            "height_field": "door_height",
            "width_field": "door_width",
            "pricing_source": "price_matrix"
        }
    ],
    "sale_prices": [
        {
            "matrix_name": "Mesh Price Matrix",
            "price": 95.92,
            "dimensions": {
                "height": 2500,
                "width": 1000
            },
            "matrix_type": "mesh",
            "height_field": "door_height",
            "width_field": "door_width",
            "pricing_source": "price_matrix"
        }
    ]
}
```

### Error Response

```json
{
    "success": false,
    "error": "Template not found"
}
```

## Debugging and Logging

### Debug Log Format

```
[PRICE_MATRIX] get_configuration_price called for template 24 (Double Hinge Door Template)
[PRICE_MATRIX] Configuration values: {'bx_dbl_hinge_quantity': '2', 'door_height': 2500, 'door_width': 1000}
[PRICE_MATRIX] Using quantity multiplier: 2.0 (from configuration) and panel_quantity: 1 (from template)
[PRICE_MATRIX] Processing mesh matrix: Mesh Price Matrix (ID: 5)
[PRICE_MATRIX] Using height field: door_height, width field: door_width
[PRICE_MATRIX] Extracted dimensions for mesh - Height: 2500, Width: 1000
[PRICE_MATRIX] Price from mesh matrix: 191.84 (base price * quantity multiplier 2.0)
[PRICE_MATRIX] Price from mesh matrix Mesh Price Matrix: 191.84 for height=2500, width=1000
[PRICE_MATRIX] Final result - Total price: 191.84, Currency: USD
[PRICE_MATRIX] Breakdown: [{'matrix_name': 'Mesh Price Matrix', 'price': 191.84, ...}]
```

### Quantity Multiplier Logging

When quantity multiplier is applied:

```
[PRICE_MATRIX] Using quantity multiplier: 2.0 (from configuration) and panel_quantity: 1 (from template)
[PRICE_MATRIX] Price from mesh matrix: 191.84 (base price * quantity multiplier 2.0)
[PRICE_MATRIX] Using fixed pricing for plugh special condition matrix: 95.92 (base price * quantity multiplier 2.0)
```

## Testing Scenarios

### Test Case 1: Quantity = 1

**Input**:
```json
{
    "template_id": 24,
    "config_data": "{\"bx_dbl_hinge_quantity\": \"1\", \"door_height\": 2500, \"door_width\": 1000}"
}
```

**Expected Output**:
```json
{
    "success": true,
    "price": 95.92,
    "breakdown": [
        {
            "matrix_name": "Mesh Price Matrix",
            "price": 95.92,
            "dimensions": {"height": 2500, "width": 1000}
        }
    ]
}
```

### Test Case 2: Quantity = 2

**Input**:
```json
{
    "template_id": 24,
    "config_data": "{\"bx_dbl_hinge_quantity\": \"2\", \"door_height\": 2500, \"door_width\": 1000}"
}
```

**Expected Output**:
```json
{
    "success": true,
    "price": 191.84,
    "breakdown": [
        {
            "matrix_name": "Mesh Price Matrix",
            "price": 191.84,
            "dimensions": {"height": 2500, "width": 1000}
        }
    ]
}
```

### Test Case 3: Fixed Pricing Fallback

**Input**:
```json
{
    "template_id": 24,
    "config_data": "{\"bx_dbl_hinge_quantity\": \"2\", \"door_height\": 3000, \"door_width\": 1500}"
}
```

**Expected Output**:
```json
{
    "success": true,
    "price": 200.0,
    "breakdown": [
        {
            "matrix_name": "Fixed Pricing - Standard Door",
            "price": 200.0,
            "matrix_type": "plugh_extra_fixed",
            "pricing_source": "fixed_pricing"
        }
    ]
}
```

## Performance Considerations

### Optimization Strategies

1. **Efficient Quantity Detection**: Only processes fields ending with `_quantity`
2. **Early Return**: Returns first valid quantity found
3. **Error Handling**: Graceful handling of invalid values
4. **Logging Control**: Debug logging can be disabled in production

### Memory Usage

- Minimal memory overhead for quantity multiplier calculation
- No persistent caching (calculated on-demand)
- Efficient string-to-float conversion

## Integration with Other Systems

### 1. Operation Costs Integration

Sales prices and operation costs now use the same quantity multiplier logic:

```python
# Both use the same _get_quantity_multiplier method
quantity_multiplier = self._get_quantity_multiplier(configuration_values)

# Operation costs (in controller)
cost_value = base_cost * quantity_multiplier

# Sales prices (in template model)
price = base_price * quantity_multiplier
```

### 2. UI Component Integration

Frontend components send quantity as string values, which are properly parsed:

```javascript
// Frontend sends quantity as string
const configData = JSON.stringify({
    'bx_dbl_hinge_quantity': '2',  // String value
    'door_height': 2500,
    'door_width': 1000
});

// Backend properly converts and applies
// quantity_multiplier = 2.0
```

### 3. Price Matrix Integration

The system integrates with various price matrix types:

- **Mesh Price Matrix**: For mesh component pricing
- **Frame Price Matrix**: For frame component pricing
- **Mullion Mohair Price Matrix**: For mullion mohair pricing
- **Plugh Price Matrix**: For plugh component pricing
- **Fixed Pricing Fallback**: For cases where grid lookup fails

## Special Conditions and Matrix Selection

### Mullion Mohair Special Conditions

The system evaluates special conditions for mullion mohair matrices:

```python
# SPECIAL CONDITION EVALUATION: For mulion_mohair_price_grid_id cases
if self.mulion_mohair_price_grid_id:
    _logger.info(f"[PRICE_MATRIX] Evaluating special conditions for mulion_mohair_price_grid_id case")
    
    # Get all price matrices with is_sale_price_matrix=True
    sale_price_matrices = self.env['config.matrix.price.matrix'].search([
        ('is_sale_price_matrix', '=', True),
        ('active', '=', True)
    ])
    
    # Find the first matrix that meets special conditions
    selected_matrix = None
    for matrix in sale_price_matrices:
        try:
            if matrix.evaluate_special_conditions(
                configuration_values, 
                door_height_field_name=self.door_height_field_id.name if self.door_height_field_id else None,
                door_width_field_name=self.door_width_field_id.name if self.door_width_field_id else None
            ):
                selected_matrix = matrix
                _logger.info(f"[PRICE_MATRIX] Selected matrix '{matrix.name}' (ID: {matrix.id}) based on special conditions")
                break
        except Exception as e:
            _logger.warning(f"[PRICE_MATRIX] Error evaluating special conditions for matrix '{matrix.name}': {str(e)}")
            continue
```

### Plugh Special Conditions

Similar special condition evaluation for plugh components:

```python
# NEW: SPECIAL CONDITION EVALUATION FOR PLUGH COMPONENTS
if self.plugh_price_grid_id:
    _logger.info(f"[PRICE_MATRIX] Evaluating special conditions for plugh components")
    
    # Get all price matrices with is_sale_price_matrix=True
    plugh_sale_price_matrices = self.env['config.matrix.price.matrix'].search([
        ('is_sale_price_matrix', '=', True),
        ('active', '=', True)
    ])
    
    # Find the first matrix that meets special conditions
    selected_plugh_matrix = None
    for matrix in plugh_sale_price_matrices:
        try:
            if matrix.evaluate_special_conditions(
                configuration_values, 
                door_height_field_name=self.door_height_field_id.name if self.door_height_field_id else None,
                door_width_field_name=self.door_width_field_id.name if self.door_width_field_id else None
            ):
                selected_plugh_matrix = matrix
                _logger.info(f"[PRICE_MATRIX] Selected plugh matrix '{matrix.name}' (ID: {matrix.id}) based on special conditions")
                break
        except Exception as e:
            _logger.warning(f"[PRICE_MATRIX] Error evaluating special conditions for plugh matrix '{matrix.name}': {str(e)}")
            continue
```

## Conclusion

The sales prices quantity integration provides:

- ✅ **Consistent quantity handling** with operation costs
- ✅ **String value support** for UI-generated quantities
- ✅ **Comprehensive logging** for debugging
- ✅ **Error handling** for invalid quantities
- ✅ **Performance optimization** with efficient calculations
- ✅ **Integration compatibility** with operation costs and UI components
- ✅ **Special condition support** for complex pricing scenarios
- ✅ **Fixed pricing fallback** for edge cases

The implementation ensures that sales prices scale correctly with the quantity specified in the configuration, providing accurate pricing for customers and consistent behavior across all pricing calculations.
