<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Configuration Wizard Form -->
    <record id="view_mesh_configuration_wizard_form" model="ir.ui.view">
        <field name="name">mesh.configuration.wizard.form</field>
        <field name="model">mesh.configuration.wizard</field>
        <field name="arch" type="xml">
            <form string="Configure Mesh Detection">
                <sheet>
                    <div class="oe_title">
                        <h1>Mesh Configuration Setup</h1>
                        <p>Configure how this template should detect mesh requirements and extract dimensions.</p>
                    </div>
                    
                    <group>
                        <field name="template_id" options="{'no_create': True}"/>
                        <field name="mesh_detection_method"/>
                    </group>
                    
                    <notebook>
                        <page string="Detection Rules" name="detection">
                            <group string="Field Name Detection" invisible="mesh_detection_method != 'field_names'">
                                <field name="mesh_field_patterns" widget="text" 
                                       placeholder="mesh,screen,grill,flyscreen,security"/>
                                <p class="text-muted">
                                    Enter comma-separated patterns. If any field name contains these patterns, 
                                    the configuration will be marked as requiring mesh.
                                </p>
                            </group>
                            
                            <group string="Field Value Detection" invisible="mesh_detection_method != 'field_values'">
                                <field name="mesh_value_patterns" widget="text"
                                       placeholder="mesh,screen,grill,saltwater,diamond,flyscreen"/>
                                <p class="text-muted">
                                    Enter comma-separated patterns. If any field value contains these patterns, 
                                    the configuration will be marked as requiring mesh.
                                </p>
                            </group>
                            
                            <group string="Manual Configuration" invisible="mesh_detection_method != 'manual'">
                                <p class="text-muted">
                                    Manual configuration requires custom code to determine mesh requirements.
                                    This option is for advanced users only.
                                </p>
                            </group>
                        </page>
                        
                        <page string="Dimension Mapping" name="dimensions">
                            <group string="Dimension Fields">
                                <field name="width_field_name" placeholder="width"/>
                                <field name="height_field_name" placeholder="height"/>
                            </group>
                            <p class="text-muted">
                                Specify the field names that contain the width and height dimensions.
                                These will be used to determine the required mesh size.
                            </p>
                        </page>
                        
                        <page string="Series Mapping" name="series">
                            <p class="text-muted mb-3">
                                Configure how field values map to mesh series types.
                            </p>
                            <field name="series_mapping_ids" mode="list">
                                <list string="Series Mappings" editable="bottom">
                                    <field name="field_value_pattern" placeholder="saltwater"/>
                                    <field name="mesh_series"/>
                                </list>
                            </field>
                            <div class="mt-2">
                                <small class="text-muted">
                                    Example: If a field value contains "saltwater", it will be mapped to the Saltwater Series.
                                </small>
                            </div>
                        </page>
                        
                        <page string="Preview" name="preview">
                            <div class="alert alert-info">
                                <h5>Configuration Preview</h5>
                                <field name="preview_text" readonly="1" class="bg-light p-3 border rounded" 
                                       style="font-family: monospace; white-space: pre-line;"/>
                            </div>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button string="Apply Configuration" name="action_apply_configuration" 
                            type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Mesh Configuration Wizard Action -->
    <record id="action_mesh_configuration_wizard" model="ir.actions.act_window">
        <field name="name">Configure Mesh Detection</field>
        <field name="res_model">mesh.configuration.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Mesh Config Management Views -->
    <record id="view_config_matrix_mesh_config_list" model="ir.ui.view">
        <field name="name">config.matrix.mesh.config.list</field>
        <field name="model">config.matrix.mesh.config</field>
        <field name="arch" type="xml">
            <list string="Mesh Configurations">
                <field name="template_id"/>
                <field name="detection_method"/>
                <field name="width_field_name"/>
                <field name="height_field_name"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <record id="view_config_matrix_mesh_config_form" model="ir.ui.view">
        <field name="name">config.matrix.mesh.config.form</field>
        <field name="model">config.matrix.mesh.config</field>
        <field name="arch" type="xml">
            <form string="Mesh Configuration">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_mesh_configuration_wizard)d" type="action"
                                class="oe_stat_button" icon="fa-cog"
                                context="{'default_template_id': template_id}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Configure</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="template_id" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="detection_method"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="width_field_name"/>
                            <field name="height_field_name"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Detection Patterns" name="patterns">
                            <group>
                                <field name="field_name_patterns" widget="text"/>
                                <field name="field_value_patterns" widget="text"/>
                            </group>
                        </page>
                        
                        <page string="Series Mappings" name="mappings">
                            <field name="series_mapping_ids" mode="list">
                                <list string="Series Mappings" editable="bottom">
                                    <field name="field_value_pattern"/>
                                    <field name="mesh_series"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Mesh Config Action -->
    <record id="action_config_matrix_mesh_config" model="ir.actions.act_window">
        <field name="name">Mesh Configurations</field>
        <field name="res_model">config.matrix.mesh.config</field>
        <field name="view_mode">list,form</field>
    </record>


</odoo>
