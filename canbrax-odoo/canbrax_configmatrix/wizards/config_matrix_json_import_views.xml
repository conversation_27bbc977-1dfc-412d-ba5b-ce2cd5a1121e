<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <!-- JSON Import Wizard Form View -->
        <record id="view_config_matrix_json_import_form" model="ir.ui.view">
            <field name="name">config.matrix.json.import.form</field>
            <field name="model">config.matrix.json.import</field>
            <field name="arch" type="xml">
                <form string="Import Configuration Template from JSON">
                    <header>
                        <button name="action_import" string="Import Template" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>Import Configuration Template</h1>
                            <p class="text-muted">Import configuration templates from JSON files to quickly set up product configurations</p>
                        </div>

                        <group>
                            <div class="alert alert-info" role="alert">
                                <h4><i class="fa fa-file-code-o"/> Template Import</h4>
                                <p>Import configuration templates that define product sections, fields, options, and component mappings. Templates can be uploaded as JSON files or pasted directly into the editor.</p>
                            </div>
                        </group>

                        <group>
                            <group string="Template Information">
                                <field name="name" placeholder="Enter a name for the template"/>
                                <field name="product_id" domain="[('product_tmpl_id.is_configurable', '=', True)]"/>
                            </group>
                            <group string="Import Method">
                                <div class="text-muted">
                                    <p><strong>Choose your import method:</strong></p>
                                    <ul>
                                        <li><strong>Upload File:</strong> Select a .json file from your computer</li>
                                        <li><strong>Paste Content:</strong> Copy and paste JSON content directly</li>
                                        <li><strong>View Instructions:</strong> See format requirements and examples</li>
                                    </ul>
                                </div>
                            </group>
                        </group>
                        <notebook>
                            <page string="Upload JSON File" name="upload_file">
                                <group>
                                    <div class="alert alert-success" role="alert">
                                        <h4><i class="fa fa-upload"/> Upload JSON File</h4>
                                        <p>Select a .json file from your computer that contains the configuration template data. This is the easiest method if you have an exported template file.</p>
                                    </div>
                                </group>
                                <group>
                                    <group string="File Selection">
                                        <field name="json_file" filename="json_filename" widget="binary" class="oe_inline"/>
                                        <field name="json_filename" invisible="1"/>
                                    </group>
                                    <group string="File Requirements">
                                        <div class="text-muted">
                                            <p><strong>Supported formats:</strong></p>
                                            <ul>
                                                <li>JSON files (.json extension)</li>
                                                <li>Text files containing valid JSON</li>
                                                <li>UTF-8 encoded files</li>
                                            </ul>
                                            <p><strong>File source:</strong> Templates exported from other Matrix configurations</p>
                                        </div>
                                    </group>
                                </group>
                            </page>
                            <page string="Paste JSON Content" name="paste_content">
                                <group>
                                    <div class="alert alert-warning" role="alert">
                                        <h4><i class="fa fa-paste"/> Paste JSON Content</h4>
                                        <p>Copy and paste JSON content directly into the editor below. This method is useful when you have JSON data from emails, documentation, or other sources.</p>
                                    </div>
                                </group>
                                <group>
                                    <div class="text-muted mb-3">
                                        <p><strong>Editor features:</strong> Syntax highlighting, auto-completion, and error detection</p>
                                    </div>
                                    <field name="json_content"
                                           widget="code"
                                           options="{
                                               'mode': 'javascript',
                                               'theme': 'monokai',
                                               'fontSize': 14,
                                               'showPrintMargin': false,
                                               'enableBasicAutocompletion': true,
                                               'enableLiveAutocompletion': true,
                                               'enableSnippets': true
                                           }"
                                           style="min-height: 400px; width: 100%;"
                                           placeholder="Paste JSON content here..."
                                           nolabel="1"/>
                                </group>
                            </page>
                            <page string="Format Instructions" name="instructions">
                                <group>
                                    <div class="alert alert-info" role="alert">
                                        <h4><i class="fa fa-info-circle"/> JSON Format Requirements</h4>
                                        <p>Your JSON file must follow the specific format below to be imported successfully. Review this structure before uploading or pasting your content.</p>
                                    </div>
                                </group>
                                <group>
                                    <group string="Required Structure">
                                        <div class="text-muted">
                                            <p><strong>The JSON must contain these main sections:</strong></p>
                                            <ul>
                                                <li><strong>template:</strong> Basic template information (name, description)</li>
                                                <li><strong>sections:</strong> Configuration sections that group related fields</li>
                                                <li><strong>fields:</strong> Individual configuration options within sections</li>
                                                <li><strong>options:</strong> Available choices for selection fields</li>
                                                <li><strong>component mappings:</strong> Links between options and product components</li>
                                            </ul>
                                        </div>
                                    </group>
                                    <group string="Example Format">
                                        <div class="alert alert-secondary" role="alert">
                                            <p><strong>Basic JSON Structure:</strong></p>
                                            <pre>{
  "template": {
    "name": "Template Name",
    "description": "Template description"
  },
  "sections": [
    {
      "name": "Section Name",
      "sequence": 10,
      "fields": [
        {
          "name": "Field Name",
          "field_type": "selection",
          "required": true,
          "options": [
            {"name": "Option 1", "value": "opt1"},
            {"name": "Option 2", "value": "opt2"}
          ]
        }
      ]
    }
  ]
}</pre>
                                        </div>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- JSON Import Action moved to menu_views.xml to fix loading order -->

    </data>
</odoo>
