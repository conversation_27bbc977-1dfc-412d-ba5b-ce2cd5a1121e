# -*- coding: utf-8 -*-

import base64
import csv
import io
import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ConfigMatrixImportWizard(models.TransientModel):
    _name = 'config.matrix.import.wizard'
    _description = 'Import Price Matrices from Excel/CSV'

    import_type = fields.Selection([
        ('price', 'Price Matrix')
    ], string="Import Type", required=True, default='price')
    
    file_data = fields.Binary("CSV/Excel File", required=True)
    file_name = fields.Char("File Name")
    
    # For price matrices
    matrix_name = fields.Char("Matrix Name", required=True)
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    

    
    # Preview options
    preview_data = fields.Text("Preview Data", readonly=True)
    has_preview = fields.Boolean("Has Preview", default=False)
    

    
    def action_preview(self):
        """Preview the data to be imported"""
        self.ensure_one()
        
        if not self.file_data:
            raise UserError(_("Please upload a file first"))
        
        try:
            # Read file data
            file_content = base64.b64decode(self.file_data).decode('utf-8')
            
            # Parse CSV
            csv_reader = csv.reader(io.StringIO(file_content))
            rows = list(csv_reader)
            
            if len(rows) < 2:
                raise UserError(_("File must contain at least a header row and one data row"))
            
            # Generate preview
            preview_lines = []
            preview_lines.append(f"File: {self.file_name}")
            preview_lines.append(f"Rows: {len(rows)}")
            preview_lines.append(f"Columns: {len(rows[0]) if rows else 0}")
            preview_lines.append("")
            preview_lines.append("Header:")
            preview_lines.append(", ".join(rows[0]))
            preview_lines.append("")
            preview_lines.append("First 5 data rows:")
            
            for i, row in enumerate(rows[1:6]):  # Show first 5 data rows
                preview_lines.append(f"Row {i+1}: {', '.join(row)}")
            
            if len(rows) > 6:
                preview_lines.append(f"... and {len(rows) - 6} more rows")
            
            self.preview_data = "\n".join(preview_lines)
            self.has_preview = True
            
        except Exception as e:
            raise UserError(_("Error reading file: %s") % str(e))
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context
        }
    
    def action_import(self):
        """Import the matrix data"""
        self.ensure_one()
        
        if not self.file_data:
            raise UserError(_("Please upload a file first"))
        
        try:
            # Read file data
            file_content = base64.b64decode(self.file_data).decode('utf-8')
            
            matrix = self._import_price_matrix(file_content)
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.price.matrix',
                'res_id': matrix.id,
                'view_mode': 'form',
                'target': 'current',
            }
                
        except Exception as e:
            raise UserError(_("Error importing file: %s") % str(e))
    
    def _import_price_matrix(self, csv_content):
        """Import price matrix from CSV content"""
        return self.env['config.matrix.price.matrix'].import_from_csv(
            name=self.matrix_name,
            product_template_id=self.product_template_id.id,
            csv_content=csv_content
        )
    



class ConfigMatrixExcelImportWizard(models.TransientModel):
    _name = 'config.matrix.excel.import.wizard'
    _description = 'Import Multiple Matrices from Excel Workbook'

    file_data = fields.Binary("Excel File", required=True)
    file_name = fields.Char("File Name")
    
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    
    # Sheet selection
    sheet_info = fields.Text("Sheet Information", readonly=True)
    selected_sheets = fields.Text("Selected Sheets (JSON)", default="[]")
    
    def action_analyze_file(self):
        """Analyze Excel file and show available sheets"""
        self.ensure_one()
        
        if not self.file_data:
            raise UserError(_("Please upload a file first"))
        
        try:
            import openpyxl
            
            # Read Excel file
            file_content = base64.b64decode(self.file_data)
            workbook = openpyxl.load_workbook(io.BytesIO(file_content), read_only=True)
            
            # Analyze sheets
            sheet_info = []
            sheet_info.append("Available sheets in the Excel file:")
            sheet_info.append("")
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                rows = list(worksheet.iter_rows(values_only=True))
                
                sheet_info.append(f"Sheet: {sheet_name}")
                sheet_info.append(f"  Rows: {len(rows)}")
                sheet_info.append(f"  Columns: {len(rows[0]) if rows else 0}")
                
                if rows:
                    # Try to detect if this looks like a matrix
                    header = rows[0] if rows else []
                    if len(header) > 2:
                        sheet_info.append(f"  Header: {', '.join(str(h) for h in header[:5])}")
                        
                        # Check if first column looks like heights and first row like widths
                        first_col = [row[0] for row in rows[1:6] if row]
                        sheet_info.append(f"  First column (heights?): {', '.join(str(c) for c in first_col)}")
                
                sheet_info.append("")
            
            self.sheet_info = "\n".join(sheet_info)
            
        except ImportError:
            raise UserError(_("openpyxl library is required to read Excel files. Please install it."))
        except Exception as e:
            raise UserError(_("Error analyzing Excel file: %s") % str(e))
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context
        }
    
    def action_import_sheets(self):
        """Import selected sheets as matrices"""
        self.ensure_one()
        
        if not self.file_data:
            raise UserError(_("Please upload a file first"))
        
        try:
            import openpyxl
            
            # Read Excel file
            file_content = base64.b64decode(self.file_data)
            workbook = openpyxl.load_workbook(io.BytesIO(file_content), read_only=True)
            
            imported_matrices = []
            
            # Import each sheet (for now, import all sheets as price matrices)
            for sheet_name in workbook.sheetnames:
                try:
                    # Convert sheet to CSV format
                    worksheet = workbook[sheet_name]
                    rows = list(worksheet.iter_rows(values_only=True))
                    
                    if len(rows) < 2:
                        continue
                    
                    # Convert to CSV
                    output = io.StringIO()
                    writer = csv.writer(output)
                    for row in rows:
                        # Convert None values to empty strings
                        clean_row = [str(cell) if cell is not None else '' for cell in row]
                        writer.writerow(clean_row)
                    
                    csv_content = output.getvalue()
                    output.close()
                    
                    # Import as price matrix
                    matrix = self.env['config.matrix.price.matrix'].import_from_csv(
                        name=f"{self.product_template_id.name} - {sheet_name}",
                        product_template_id=self.product_template_id.id,
                        csv_content=csv_content
                    )
                    
                    imported_matrices.append(matrix)
                    
                except Exception as e:
                    _logger.warning(f"Failed to import sheet {sheet_name}: {str(e)}")
            
            if not imported_matrices:
                raise UserError(_("No sheets could be imported"))
            
            # Return action to view imported matrices
            return {
                'name': _('Imported Price Matrices'),
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.price.matrix',
                'view_mode': 'list,form',
                'domain': [('id', 'in', [m.id for m in imported_matrices])],
                'target': 'current',
            }
            
        except ImportError:
            raise UserError(_("openpyxl library is required to read Excel files. Please install it."))
        except Exception as e:
            raise UserError(_("Error importing Excel file: %s") % str(e))
