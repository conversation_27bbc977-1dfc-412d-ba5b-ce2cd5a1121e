<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Matrix Import Wizard Views -->
    
    <!-- Wizard Form View -->
    <record id="view_mesh_matrix_import_wizard_form" model="ir.ui.view">
        <field name="name">mesh.matrix.import.wizard.form</field>
        <field name="model">mesh.matrix.import.wizard</field>
        <field name="arch" type="xml">
            <form string="Import Mesh Cutting Matrix">
                <sheet>
                    <div class="oe_title">
                        <h1>Import Mesh Cutting Matrix</h1>
                        <p>Import cutting matrix data from Excel files</p>
                    </div>
                    
                    <group invisible="import_performed">
                        <group string="File Upload">
                            <field name="excel_file" filename="filename"/>
                            <field name="filename" invisible="1"/>
                            <field name="sheet_name"/>
                        </group>
                        <group string="Import Settings">
                            <field name="mesh_series"/>
                            <field name="import_mode"/>
                            <field name="existing_matrix_id" 
                                   invisible="import_mode != 'update_existing'"
                                   required="import_mode == 'update_existing'"/>
                        </group>
                    </group>
                    
                    <!-- Import Results Section -->
                    <group string="Import Results" invisible="not import_performed">
                        <div class="alert alert-success" invisible="not import_performed">
                            <p><i class="fa fa-check-circle"/> <strong>Import Completed Successfully</strong></p>
                        </div>
                        
                        <field name="import_log" widget="text" nolabel="1" readonly="1"/>
                        
                        <field name="created_matrix_id" readonly="1" 
                               invisible="not created_matrix_id"/>
                    </group>
                    
                    <!-- Hidden fields -->
                    <field name="import_performed" invisible="1"/>
                </sheet>
                
                <footer>
                    <button name="action_import_matrix" type="object" 
                            string="Import Matrix" class="btn-primary"
                            invisible="import_performed"/>
                    <button name="action_view_matrix" type="object" 
                            string="View Matrix" class="btn-success"
                            invisible="not import_performed"/>
                    <button string="Close" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    
    <!-- Wizard Action -->
    <record id="action_mesh_matrix_import_wizard" model="ir.actions.act_window">
        <field name="name">Import Mesh Cutting Matrix</field>
        <field name="res_model">mesh.matrix.import.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_mesh_matrix_import_wizard_form"/>
    </record>
    
</odoo>
