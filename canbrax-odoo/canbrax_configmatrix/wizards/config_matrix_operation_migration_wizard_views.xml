<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Operation Migration Wizard Form View -->
    <record id="view_config_matrix_operation_migration_wizard_form" model="ir.ui.view">
        <field name="name">config.matrix.operation.migration.wizard.form</field>
        <field name="model">config.matrix.operation.migration.wizard</field>
        <field name="arch" type="xml">
            <form string="Migrate Operation Templates">
                <div class="alert alert-info" role="alert">
                    <h4>Duration/Cost Formula Migration</h4>
                    <p>This wizard will help migrate your operation templates to separate duration and cost calculations.</p>
                    <p><strong>Current Issue:</strong> The "Duration Formula" field currently contains cost calculations, 
                       but BOMs need actual duration values.</p>
                    <p><strong>Solution:</strong> Split formulas into separate duration (for BOM) and cost (for pricing) formulas.</p>
                </div>
                
                <group>
                    <field name="migration_mode" widget="radio"/>
                </group>
                
                <group string="Templates to Migrate">
                    <field name="template_ids" nolabel="1">
                        <list string="Operation Templates">
                            <field name="name"/>
                            <field name="duration_formula" string="Current Formula"/>
                            <field name="workcenter_id"/>
                            <field name="config_template_id"/>
                        </list>
                    </field>
                </group>
                
                <div class="alert alert-warning" role="alert">
                    <h5>Migration Example:</h5>
                    <p><strong>Current Formula:</strong> <code>get_fixed_price('RecMatlT')*2*get_fixed_price('WageP')</code></p>
                    <p><strong>Will become:</strong></p>
                    <ul>
                        <li><strong>Duration Formula:</strong> <code>get_fixed_price('RecMatlT')</code> (for BOM operations)</li>
                        <li><strong>Cost Formula:</strong> <code>get_fixed_price('RecMatlT')*2*get_fixed_price('WageP')</code> (for pricing)</li>
                    </ul>
                </div>
                
                <footer>
                    <button string="Preview Migration" name="action_preview_migration" type="object" class="btn-secondary"/>
                    <button string="Migrate Templates" name="action_migrate_templates" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>


</odoo>
