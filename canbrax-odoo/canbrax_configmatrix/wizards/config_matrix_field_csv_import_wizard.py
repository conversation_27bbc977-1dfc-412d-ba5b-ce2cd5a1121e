# -*- coding: utf-8 -*-

import base64
import io
import csv
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ConfigMatrixFieldCsvImportWizard(models.TransientModel):
    _name = 'config.matrix.field.csv.import.wizard'
    _description = 'CSV Import Wizard for Config Matrix Field Options'

    field_id = fields.Many2one(
        'config.matrix.field',
        string='Field',
        required=True,
        readonly=True
    )
    
    csv_file = fields.Binary(
        string='CSV File',
        required=True,
        help='Select a CSV file to import options and component mappings'
    )
    
    csv_filename = fields.Char(
        string='Filename'
    )
    
    clear_existing = fields.Boolean(
        string='Clear Existing Options',
        default=False,
        help='If checked, all existing options will be deleted before importing'
    )
    
    preview_data = fields.Text(
        string='Preview',
        readonly=True,
        help='Preview of the CSV data that will be imported'
    )
    
    @api.onchange('csv_file')
    def _onchange_csv_file(self):
        """Preview CSV data when file is selected"""
        if self.csv_file:
            try:
                csv_data = base64.b64decode(self.csv_file).decode('utf-8')
                # Show first few lines as preview
                lines = csv_data.split('\n')[:6]  # First 6 lines
                self.preview_data = '\n'.join(lines)
                if len(csv_data.split('\n')) > 6:
                    self.preview_data += '\n... (and more rows)'
            except Exception as e:
                self.preview_data = f"Error reading file: {str(e)}"
    
    def action_import(self):
        """Import the CSV data"""
        if not self.csv_file:
            raise UserError(_("Please select a CSV file to import."))
        
        try:
            # Decode the CSV file
            csv_data = base64.b64decode(self.csv_file).decode('utf-8')
            
            # Validate CSV structure
            self._validate_csv_structure(csv_data)
            
            # Import the data
            result = self.field_id.import_options_from_csv_data(csv_data, self.clear_existing)
            
            return result
            
        except Exception as e:
            _logger.error("Error in CSV import wizard: %s", str(e))
            raise UserError(_("Import failed: %s") % str(e))
    
    def _validate_csv_structure(self, csv_data):
        """Validate that the CSV has the expected structure"""
        try:
            csv_reader = csv.DictReader(io.StringIO(csv_data))
            
            # Check for required headers
            required_headers = [
                'Field Label',
                'Options/Option Text', 
                'Options/Option Value'
            ]
            
            # Recommended headers for full functionality
            recommended_headers = [
                'Options/Component Mappings/Mapping Type',
                'Options/Component Mappings/Component Product',
                'Options/Component Mappings/Quantity Formula'
            ]
            
            missing_headers = []
            for header in required_headers:
                if header not in csv_reader.fieldnames:
                    missing_headers.append(header)
            
            if missing_headers:
                raise ValidationError(
                    _("Missing required CSV headers: %s") % ', '.join(missing_headers)
                )
            
            # Check for recommended headers and warn if missing
            missing_recommended = []
            for header in recommended_headers:
                if header not in csv_reader.fieldnames:
                    missing_recommended.append(header)
            
            # Try to read at least one valid option row
            valid_rows = 0
            for row in csv_reader:
                option_text = row.get('Options/Option Text', '').strip()
                option_value = row.get('Options/Option Value', '').strip()
                
                # Skip separator rows
                if option_value.startswith('separator_'):
                    continue
                    
                if option_text and option_value:
                    valid_rows += 1
                    break
            
            if valid_rows == 0:
                raise ValidationError(_("No valid option rows found in CSV. Make sure you have rows with both 'Options/Option Text' and 'Options/Option Value' filled."))
                
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            raise ValidationError(_("Invalid CSV format: %s") % str(e))
    
    def action_download_template(self):
        """Download a template CSV file"""
        # Create a sample CSV template
        template_data = [
            {
                'Field Label': 'Frame Colour',
                'Template': 'Subcategory - Hinged Door SWS (IMPORT)',
                'Technical Name': 'sws_hinge_frame_colour',
                'Options/Option Text': 'Black Custom Matt GN248A',
                'Options/Option Value': 'black_custom_matt_gn248a',
                'Options/Field/Quantity Formula': '',
                'Options/Has Multiple Components': 'TRUE',
                'Options/Component Mappings/Mapping Type': 'Static Product',
                'Options/Component Mappings/Base Component': '',
                'Options/Component Mappings/Component Product': 'Commandex Door Frame - Black Custom Matt GN248A',
                'Options/Component Mappings/Match Against': 'Color',
                'Options/Component Mappings/Quantity Formula': '(_CALCULATED_smallest_door_height*2) + _CALCULATED_Top_Width + _CALCULATED_Bottom_Width',
                'Options/Component Mappings/Additional Condition': '',
            },
            {
                'Field Label': '',
                'Template': '',
                'Technical Name': '',
                'Options/Option Text': '',
                'Options/Option Value': '',
                'Options/Field/Quantity Formula': '',
                'Options/Has Multiple Components': '',
                'Options/Component Mappings/Mapping Type': 'Static Product',
                'Options/Component Mappings/Base Component': '',
                'Options/Component Mappings/Component Product': 'Secureview Plugh - Black',
                'Options/Component Mappings/Match Against': 'Color',
                'Options/Component Mappings/Quantity Formula': '(_CALCULATED_smallest_door_height - 144) + (_CALCULATED_smallest_door_height - 144) + (_CALCULATED_Top_Width - 144) + (_CALCULATED_Bottom_Width - 144)',
                'Options/Component Mappings/Additional Condition': '',
            }
        ]
        
        # Generate CSV
        output = io.StringIO()
        fieldnames = template_data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(template_data)
        
        csv_content = output.getvalue()
        output.close()
        
        # Return download action
        attachment = self.env['ir.attachment'].create({
            'name': 'config_matrix_field_import_template.csv',
            'type': 'binary',
            'datas': base64.b64encode(csv_content.encode('utf-8')),
            'mimetype': 'text/csv',
            'res_model': self._name,
            'res_id': self.id,
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }
