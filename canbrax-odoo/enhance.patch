diff --git a/canbrax_configmatrix/static/src/css/pricing/enhanced_excel_matrix.css b/canbrax_configmatrix/static/src/css/pricing/enhanced_excel_matrix.css
index 5cc6eab..3da77ff 100644
--- a/canbrax_configmatrix/static/src/css/pricing/enhanced_excel_matrix.css
+++ b/canbrax_configmatrix/static/src/css/pricing/enhanced_excel_matrix.css
@@ -637,6 +637,40 @@ border-color: #000 !important;
 }
 
 /* Price Matrix Form Specific Styles */
+.matrix-visual-editor-container {
+    max-width: 100%;
+    overflow: hidden;
+    width: 100%;
+}
+
+/* Ensure the field container doesn't overflow */
+.o_field_widget[data-field-name="matrix_data"] {
+    max-width: 100%;
+    overflow: hidden;
+    width: 100%;
+}
+
+.o_field_widget[data-field-name="matrix_data"] .interactive-matrix-editor {
+    max-width: 100%;
+    overflow: hidden;
+    width: 100%;
+}
+
+/* Force the matrix container to have a specific max width */
+.o_field_widget[data-field-name="matrix_data"] .matrix-container {
+    max-width: 100%;
+    width: 100%;
+}
+
+.o_field_widget[data-field-name="matrix_data"] .matrix-scroll-wrapper {
+    max-width: 100%;
+    width: 100%;
+    /* Force horizontal scrolling when content is wider */
+    overflow-x: auto;
+    overflow-y: auto;
+    /* Use viewport width to be responsive but still constrain */
+    max-width: min(100%, 90vw);
+}
 .matrix-visual-editor-container .matrix-toolbar {
     background: transparent !important; /* Remove grey background */
     border: none !important;
@@ -666,3 +700,203 @@ border-color: #000 !important;
 .matrix-visual-editor-container .toolbar-actions .btn i {
     margin-right: 6px;
 }
+
+/* Interactive Matrix Editor Scrolling Styles */
+.interactive-matrix-editor {
+    max-width: 100%;
+    overflow: hidden;
+}
+
+.interactive-matrix-editor .matrix-container {
+    border: 1px solid #dee2e6;
+    border-radius: 0.375rem;
+    background: #ffffff;
+    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
+    overflow: hidden;
+    max-width: 100%;
+}
+
+.interactive-matrix-editor .matrix-scroll-wrapper {
+    position: relative;
+    overflow: auto;
+    max-height: 500px;
+    max-width: 100%;
+    width: 100%;
+    min-width: 0;
+    /* Force the container to not expand beyond its parent */
+    box-sizing: border-box;
+    /* Ensure the container doesn't expand to fit content */
+    flex-shrink: 0;
+    /* Custom scrollbar styling */
+    scrollbar-width: thin;
+    scrollbar-color: #cbd5e0 #f7fafc;
+}
+
+.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar {
+    width: 8px;
+    height: 8px;
+}
+
+.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar-track {
+    background: #f7fafc;
+    border-radius: 4px;
+}
+
+.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar-thumb {
+    background: #cbd5e0;
+    border-radius: 4px;
+}
+
+.interactive-matrix-editor .matrix-scroll-wrapper::-webkit-scrollbar-thumb:hover {
+    background: #a0aec0;
+}
+
+.interactive-matrix-editor .matrix-table {
+    margin: 0;
+    font-size: 12px;
+    border-collapse: separate;
+    border-spacing: 0;
+    min-width: max-content;
+    width: max-content;
+    table-layout: auto;
+}
+
+/* Sticky positioning for headers in interactive matrix */
+.interactive-matrix-editor .matrix-corner-cell {
+    position: sticky !important;
+    left: 0 !important;
+    top: 0 !important;
+    z-index: 15 !important;
+    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
+    min-width: 80px !important;
+    height: 36px !important;
+    text-align: center !important;
+    font-weight: bold !important;
+    border: 1px solid #dee2e6 !important;
+}
+
+.interactive-matrix-editor .matrix-header-cell {
+    position: sticky !important;
+    top: 0 !important;
+    z-index: 10 !important;
+    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
+    height: 36px !important;
+    text-align: center !important;
+    min-width: 90px !important;
+    font-weight: 600 !important;
+    border: 1px solid #dee2e6 !important;
+    color: #495057 !important;
+}
+
+.interactive-matrix-editor .matrix-row-header {
+    position: sticky !important;
+    left: 0 !important;
+    z-index: 5 !important;
+    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
+    text-align: center !important;
+    font-weight: 600 !important;
+    border: 1px solid #dee2e6 !important;
+    color: #495057 !important;
+    border-right: 2px solid #adb5bd !important;
+}
+
+/* Data cell styling for interactive matrix */
+.interactive-matrix-editor .matrix-data-cell {
+    text-align: center;
+    cursor: pointer;
+    padding: 8px;
+    min-width: 90px;
+    border: 1px solid #d1d5db;
+    transition: all 0.15s ease;
+    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
+    position: relative;
+}
+
+.interactive-matrix-editor .matrix-data-cell:hover {
+    background-color: #f8fafc !important;
+    border-color: #3b82f6 !important;
+    box-shadow: inset 0 0 0 1px #3b82f6 !important;
+}
+
+.interactive-matrix-editor .matrix-data-cell.selected {
+    background-color: #dbeafe !important;
+    border-color: #2563eb !important;
+    box-shadow: 0 0 0 1px #2563eb !important;
+}
+
+.interactive-matrix-editor .matrix-data-cell.editing {
+    padding: 0;
+}
+
+.interactive-matrix-editor .matrix-data-cell.has-value {
+    font-weight: 500;
+    color: #374151;
+}
+
+.interactive-matrix-editor .matrix-data-cell.empty-value {
+    color: #9ca3af;
+    font-style: italic;
+}
+
+/* Input styling for editing in interactive matrix */
+.interactive-matrix-editor .matrix-cell-input {
+    width: 100%;
+    border: none;
+    outline: none;
+    text-align: center;
+    background: #ffffff;
+    font-weight: 500;
+    color: #1f2937;
+    font-size: 13px;
+    padding: 8px;
+    box-shadow: inset 0 0 0 2px #059669;
+    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
+}
+
+/* Help text styling for interactive matrix */
+.interactive-matrix-editor .matrix-help {
+    background: #f8f9fa;
+    border-top: 1px solid #dee2e6;
+    padding: 0.75rem;
+    margin: 1rem -1rem -1rem -1rem;
+}
+
+/* Loading state for interactive matrix */
+.interactive-matrix-editor .matrix-loading {
+    display: flex;
+    align-items: center;
+    justify-content: center;
+    height: 200px;
+    color: #6c757d;
+    flex-direction: column;
+}
+
+.interactive-matrix-editor .matrix-loading .spinner-border {
+    width: 2rem;
+    height: 2rem;
+    margin-right: 0.5rem;
+}
+
+/* Responsive adjustments for interactive matrix */
+@media (max-width: 768px) {
+    .interactive-matrix-editor .matrix-scroll-wrapper {
+        max-height: 300px;
+    }
+
+    .interactive-matrix-editor .matrix-data-cell {
+        min-width: 70px;
+        padding: 6px;
+        font-size: 11px;
+    }
+
+    .interactive-matrix-editor .matrix-corner-cell,
+    .interactive-matrix-editor .matrix-row-header {
+        min-width: 60px;
+        font-size: 11px;
+    }
+
+    .interactive-matrix-editor .matrix-header-cell {
+        min-width: 70px;
+        font-size: 11px;
+    }
+}
\ No newline at end of file
diff --git a/canbrax_configmatrix/static/src/js/pricing/price_matrix_widget.js b/canbrax_configmatrix/static/src/js/pricing/price_matrix_widget.js
index b112550..453cda4 100644
--- a/canbrax_configmatrix/static/src/js/pricing/price_matrix_widget.js
+++ b/canbrax_configmatrix/static/src/js/pricing/price_matrix_widget.js
@@ -31,6 +31,8 @@ export class PriceMatrixWidget extends Component {
         onMounted(() => {
             this.loadMatrixData();
             this.setupEventHandlers();
+            this.setupResizeHandler();
+            this.adjustContainerWidth();
 
             // Set up auto-refresh to catch data changes
             this.autoRefreshInterval = setInterval(() => {
@@ -41,7 +43,6 @@ export class PriceMatrixWidget extends Component {
                 if (currentHeightRanges !== this.lastKnownHeightRanges ||
                     currentWidthRanges !== this.lastKnownWidthRanges ||
                     currentMatrixData !== this.lastKnownMatrixData) {
-                    console.log(`[PriceMatrixWidget] Auto-refresh detected change for field ${this.props.name}`);
                     this.lastKnownHeightRanges = currentHeightRanges;
                     this.lastKnownWidthRanges = currentWidthRanges;
                     this.lastKnownMatrixData = currentMatrixData;
@@ -55,7 +56,6 @@ export class PriceMatrixWidget extends Component {
             if (nextProps.record.data[this.props.name] !== this.props.record.data[this.props.name] ||
                 nextProps.record.data.height_ranges !== this.props.record.data.height_ranges ||
                 nextProps.record.data.width_ranges !== this.props.record.data.width_ranges) {
-                console.log(`[PriceMatrixWidget] Field ${this.props.name} or related fields changed, reloading matrix data`);
                 // Update props reference and reload
                 this.props = nextProps;
                 this.loadMatrixData();
@@ -64,6 +64,7 @@ export class PriceMatrixWidget extends Component {
 
         onWillUnmount(() => {
             this.cleanupEventHandlers();
+            this.cleanupResizeHandler();
             if (this.autoRefreshInterval) {
                 clearInterval(this.autoRefreshInterval);
             }
@@ -75,7 +76,6 @@ export class PriceMatrixWidget extends Component {
             // Get data from the price matrix record
             const record = this.props.record;
             if (!record || !record.data) {
-                console.log(`[PriceMatrixWidget] Record or record data not available yet`);
                 this.state.heights = [];
                 this.state.widths = [];
                 this.state.matrixData = {};
@@ -112,7 +112,6 @@ export class PriceMatrixWidget extends Component {
             this.calculateStats();
             this.renderMatrix();
         } catch (error) {
-            console.error('Failed to load matrix data:', error);
             this.state.heights = [];
             this.state.widths = [];
             this.state.matrixData = {};
@@ -134,7 +133,19 @@ export class PriceMatrixWidget extends Component {
         table.innerHTML = '';
 
         if (this.state.heights.length === 0 || this.state.widths.length === 0) {
-            table.innerHTML = '<tr><td colspan="100%" class="text-center text-muted p-4">Configure height and width ranges in the Matrix Configuration tab to see the matrix</td></tr>';
+            table.innerHTML = `
+                <tr>
+                    <td colspan="100%" class="text-center text-muted p-5">
+                        <div class="matrix-loading">
+                            <i class="fa fa-info-circle fa-2x mb-3" style="color: #6c757d;"></i>
+                            <div>
+                                <h5>No Matrix Data</h5>
+                                <p class="mb-0">Configure height and width ranges in the Matrix Configuration tab to see the matrix</p>
+                            </div>
+                        </div>
+                    </td>
+                </tr>
+            `;
             return;
         }
 
@@ -143,14 +154,12 @@ export class PriceMatrixWidget extends Component {
         const cornerCell = document.createElement('th');
         cornerCell.className = 'matrix-corner-cell';
         cornerCell.textContent = 'H\\W';
-        cornerCell.style.cssText = 'background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); position: sticky; left: 0; z-index: 10; min-width: 80px; text-align: center; font-weight: bold; border: 1px solid #dee2e6;';
         headerRow.appendChild(cornerCell);
 
         this.state.widths.forEach(width => {
             const th = document.createElement('th');
             th.className = 'matrix-header-cell';
             th.textContent = width;
-            th.style.cssText = 'background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); text-align: center; min-width: 90px; font-weight: 600; position: sticky; top: 0; z-index: 5; border: 1px solid #dee2e6; color: #495057;';
             headerRow.appendChild(th);
         });
 
@@ -164,7 +173,6 @@ export class PriceMatrixWidget extends Component {
             const rowHeader = document.createElement('th');
             rowHeader.className = 'matrix-row-header';
             rowHeader.textContent = height;
-            rowHeader.style.cssText = 'background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); position: sticky; left: 0; z-index: 9; text-align: center; font-weight: 600; border: 1px solid #dee2e6; color: #495057; border-right: 2px solid #adb5bd;';
             row.appendChild(rowHeader);
 
             // Data cells
@@ -179,57 +187,15 @@ export class PriceMatrixWidget extends Component {
                 cell.dataset.key = cellKey;
 
                 // Apply Excel-like styling
-                let cellStyle = 'text-align: center; cursor: pointer; padding: 8px; min-width: 90px; border: 1px solid #d1d5db; transition: all 0.15s ease; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;';
-
-                // Alternating row colors
-                if (rowIndex % 2 === 1) {
-                    cellStyle += 'background-color: #fafbfc;';
-                } else {
-                    cellStyle += 'background-color: #ffffff;';
-                }
-
                 if (cellValue) {
                     cell.textContent = this.formatCellValue(cellValue);
                     cell.classList.add('has-value');
-                    if (rowIndex % 2 === 1) {
-                        cellStyle += 'background-color: #f8f9fa; font-weight: 500; color: #374151;';
-                    } else {
-                        cellStyle += 'background-color: #f9fafb; font-weight: 500; color: #374151;';
-                    }
                 } else {
                     cell.textContent = '-';
                     cell.classList.add('empty-value');
-                    cellStyle += 'color: #9ca3af; font-style: italic;';
                 }
 
-                cell.style.cssText = cellStyle;
-
-                // Add hover effect
-                cell.addEventListener('mouseenter', () => {
-                    cell.style.backgroundColor = '#f8fafc';
-                    cell.style.borderColor = '#3b82f6';
-                    cell.style.boxShadow = 'inset 0 0 0 1px #3b82f6';
-                });
-
-                cell.addEventListener('mouseleave', () => {
-                    if (!cell.classList.contains('selected')) {
-                        if (cellValue) {
-                            if (rowIndex % 2 === 1) {
-                                cell.style.backgroundColor = '#f8f9fa';
-                            } else {
-                                cell.style.backgroundColor = '#f9fafb';
-                            }
-                        } else {
-                            if (rowIndex % 2 === 1) {
-                                cell.style.backgroundColor = '#fafbfc';
-                            } else {
-                                cell.style.backgroundColor = '#ffffff';
-                            }
-                        }
-                        cell.style.borderColor = '#d1d5db';
-                        cell.style.boxShadow = 'none';
-                    }
-                });
+                // Hover effects are now handled by CSS
 
                 // Add click handlers
                 cell.addEventListener('click', () => this.onCellClick(height, width, cellKey));
@@ -260,9 +226,6 @@ export class PriceMatrixWidget extends Component {
         const cell = this.matrixRef.el.querySelector(`[data-key="${cellKey}"]`);
         if (cell) {
             cell.classList.add('selected');
-            cell.style.backgroundColor = '#dbeafe';
-            cell.style.borderColor = '#2563eb';
-            cell.style.boxShadow = '0 0 0 1px #2563eb';
         }
     }
 
@@ -284,7 +247,6 @@ export class PriceMatrixWidget extends Component {
             input.step = '0.01';
             input.className = 'matrix-cell-input';
             input.value = this.state.editValue;
-            input.style.cssText = 'width: 100%; border: none; outline: none; text-align: center; background: #ffffff; font-weight: 500; color: #1f2937; font-size: 13px; padding: 8px; box-shadow: inset 0 0 0 2px #059669;';
 
             // Replace cell content with input
             cell.innerHTML = '';
@@ -472,6 +434,67 @@ export class PriceMatrixWidget extends Component {
         // No notification - silent clear
     }
 
+    adjustContainerWidth() {
+        // Adjust container width based on available space
+        setTimeout(() => {
+            const container = this.matrixRef.el?.parentElement?.parentElement;
+            const table = this.matrixRef.el;
+
+            if (container && table) {
+                // Calculate optimal container width based on available space
+                const calculateOptimalWidth = () => {
+                    // Find the form container or sheet element
+                    const formContainer = container.closest('.o_form_sheet') ||
+                        container.closest('.o_form_view') ||
+                        container.closest('.o_form_editable');
+
+                    if (formContainer) {
+                        const formWidth = formContainer.clientWidth;
+                        // Use 95% of form width, but not less than 600px
+                        return Math.max(600, formWidth * 0.95);
+                    }
+
+                    // Fallback to viewport-based calculation
+                    const parentContainer = container.parentElement;
+                    const parentWidth = parentContainer ? parentContainer.clientWidth : 0;
+                    return Math.min(parentWidth * 0.9, window.innerWidth * 0.9);
+                };
+
+                const tableWidth = table.scrollWidth;
+                const maxAllowedWidth = calculateOptimalWidth();
+
+                if (tableWidth > maxAllowedWidth) {
+                    container.style.maxWidth = `${maxAllowedWidth}px`;
+                } else {
+                    // Reset to CSS value if table fits
+                    container.style.maxWidth = '';
+                }
+            }
+        }, 1000);
+    }
+
+    setupResizeHandler() {
+        // Handle window resize to recalculate container width
+        this.resizeHandler = () => {
+            // Debounce resize events
+            clearTimeout(this.resizeTimeout);
+            this.resizeTimeout = setTimeout(() => {
+                this.adjustContainerWidth();
+            }, 250);
+        };
+
+        window.addEventListener('resize', this.resizeHandler);
+    }
+
+    cleanupResizeHandler() {
+        if (this.resizeHandler) {
+            window.removeEventListener('resize', this.resizeHandler);
+        }
+        if (this.resizeTimeout) {
+            clearTimeout(this.resizeTimeout);
+        }
+    }
+
 }
 
 // Register the component for price matrix
diff --git a/canbrax_configmatrix/static/src/xml/interactive_matrix_templates.xml b/canbrax_configmatrix/static/src/xml/interactive_matrix_templates.xml
index 0823f7a..3ddfa72 100644
--- a/canbrax_configmatrix/static/src/xml/interactive_matrix_templates.xml
+++ b/canbrax_configmatrix/static/src/xml/interactive_matrix_templates.xml
@@ -23,12 +23,14 @@
                 </div>
             </div>
 
-            <div class="matrix-container" style="max-height: 500px; overflow: auto;">
-                <table class="table table-sm table-bordered matrix-table" style="margin: 0; font-size: 12px; border-collapse: separate; border-spacing: 0;">
-                    <tbody t-ref="matrixGrid">
-                        <!-- Matrix will be rendered here by JavaScript -->
-                    </tbody>
-                </table>
+            <div class="matrix-container">
+                <div class="matrix-scroll-wrapper">
+                    <table class="matrix-table">
+                        <tbody t-ref="matrixGrid">
+                            <!-- Matrix will be rendered here by JavaScript -->
+                        </tbody>
+                    </table>
+                </div>
             </div>
 
             <div class="matrix-help mt-2">
