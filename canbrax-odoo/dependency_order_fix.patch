diff --git a/canbrax_configmatrix/static/src/js/configurator.js b/canbrax_configmatrix/static/src/js/configurator.js
index f9e4e8f..e69f74c 100644
--- a/canbrax_configmatrix/static/src/js/configurator.js
+++ b/canbrax_configmatrix/static/src/js/configurator.js
@@ -457,12 +457,48 @@ class ConfigMatrixConfigurator extends Component {
             }
         }
 
+        // CRITICAL FIX: Add calculated field dependencies to the graph
+        this.buildCalculatedFieldDependencies();
+
         console.log('Dependency graph built:', {
             forward: Object.fromEntries(this.dependencyGraph),
             reverse: Object.fromEntries(this.reverseDependencyGraph)
         });
     }
 
+    // NEW: Build dependencies for calculated fields
+    buildCalculatedFieldDependencies() {
+        if (!window.calculatedFieldsDefinitions || !Array.isArray(window.calculatedFieldsDefinitions)) {
+            console.log('No calculated field definitions available for dependency tracking');
+            return;
+        }
+
+        console.log('Building calculated field dependencies...');
+
+        for (const calcField of window.calculatedFieldsDefinitions) {
+            if (!calcField.name || !calcField.formula) continue;
+
+            // Extract dependencies from the formula
+            const dependencies = this.extractFieldDependencies(calcField.formula);
+            
+            console.log(`Calculated field ${calcField.name} depends on:`, Array.from(dependencies));
+
+            for (const dependency of dependencies) {
+                // dependency -> calcField.name (dependency affects calculated field)
+                if (!this.dependencyGraph.has(dependency)) {
+                    this.dependencyGraph.set(dependency, new Set());
+                }
+                this.dependencyGraph.get(dependency).add(calcField.name);
+
+                // calcField.name depends on dependency
+                if (!this.reverseDependencyGraph.has(calcField.name)) {
+                    this.reverseDependencyGraph.set(calcField.name, new Set());
+                }
+                this.reverseDependencyGraph.get(calcField.name).add(dependency);
+            }
+        }
+    }
+
     // NEW: Extract field dependencies from visibility conditions
     extractFieldDependencies(condition) {
         const dependencies = new Set();
@@ -1018,6 +1054,10 @@ class ConfigMatrixConfigurator extends Component {
                 console.log(`[PERF] No dependent fields found for ${field.technical_name}`);
             }
 
+            // CRITICAL FIX: Force recalculation of calculated fields when a field changes
+            // This ensures calculated fields are updated when their dependencies change
+            this.forceCalculatedFieldsRecalculation();
+
             // Validate range conditions for changed field
             this.validateRangeConditions(field, value);
 
@@ -1046,6 +1086,38 @@ class ConfigMatrixConfigurator extends Component {
         return dependents ? Array.from(dependents) : [];
     }
 
+    // NEW: Force recalculation of calculated fields
+    forceCalculatedFieldsRecalculation() {
+        try {
+            
+            // Invalidate the calculated fields cache to force recalculation
+            if (window.invalidateCalculatedFieldsCache) {
+                window.invalidateCalculatedFieldsCache();
+            }
+            
+            // Force recalculation by calling calculateDynamicFields with current field values
+            if (window.calculateDynamicFields) {
+                const currentFieldValues = { ...this.state.values };
+                const calculatedFields = window.calculateDynamicFields(currentFieldValues);
+                
+                // Update the state with new calculated field values
+                for (const [fieldName, value] of Object.entries(calculatedFields)) {
+                    if (fieldName.startsWith('_CALCULATED_')) {
+                        this.state.values[fieldName] = value;
+                    }
+                }
+                
+                // Update the calculated fields panel if it exists
+                if (window.updateCalculatedFieldsPanel) {
+                    window.updateCalculatedFieldsPanel();
+                }
+            }
+            
+        } catch (error) {
+            console.error('[CALC] Error forcing calculated fields recalculation:', error);
+        }
+    }
+
     // PERFORMANCE: Progressive visibility update for large field sets
     async updateVisibilityProgressive(fieldNames) {
         if (!fieldNames || fieldNames.length === 0) return;
@@ -1764,12 +1836,22 @@ class ConfigMatrixConfigurator extends Component {
                     }))
                     .sort((a, b) => a.sequence - b.sequence);
 
+                // CRITICAL FIX: Make calculated field definitions available globally
+                // This allows the dependency graph to access them
+                window.calculatedFieldsDefinitions = this.calculatedFieldsDefinitions;
+
             } else {
                 this.calculatedFieldsDefinitions = [];
+                window.calculatedFieldsDefinitions = [];
             }
+
+            // CRITICAL FIX: Rebuild dependency graph to include calculated field dependencies
+            this.buildDependencyGraph();
+
         } catch (error) {
             console.error(`Error loading calculated fields:`, error);
             this.calculatedFieldsDefinitions = [];
+            window.calculatedFieldsDefinitions = [];
         }
     }
 
diff --git a/canbrax_configmatrix/static/src/js/svg_conditional_layers.js b/canbrax_configmatrix/static/src/js/svg_conditional_layers.js
index d3ea9a0..629e2ae 100644
--- a/canbrax_configmatrix/static/src/js/svg_conditional_layers.js
+++ b/canbrax_configmatrix/static/src/js/svg_conditional_layers.js
@@ -47,6 +47,9 @@ document.addEventListener('DOMContentLoaded', function() {
 
         // Add event listeners to all configuration fields
         addFieldEventListeners();
+
+        // Set up mutation observer to watch for dynamic field changes
+        setupMutationObserver();
     }
 
     // Load SVG components from the server with performance optimization
@@ -132,23 +135,127 @@ document.addEventListener('DOMContentLoaded', function() {
 
             // Add change event listener with debouncing
             field.addEventListener('change', function() {
-                // Clear any existing timeout
+                handleFieldChange(field, technicalName, fieldType);
+            });
+
+            // For text and number fields, also listen for input events for real-time updates
+            if (fieldType === 'text' || fieldType === 'number') {
+                field.addEventListener('input', function () {
+                    handleFieldChange(field, technicalName, fieldType);
+                });
+            }
+
+            // For boolean fields, also listen for click events
+            if (fieldType === 'boolean') {
+                field.addEventListener('click', function () {
+                    handleFieldChange(field, technicalName, fieldType);
+                });
+            }
+        });
+    }
+
+    // Handle field change events with proper debouncing and value tracking
+    function handleFieldChange(field, technicalName, fieldType) {
+        // Clear any existing timeout
+        if (updateTimeout) {
+            clearTimeout(updateTimeout);
+        }
+
+        // Get the field value
+        let value = getFieldValue(field, fieldType);
+
+        // Update the field value in the global object
+        fieldValues[technicalName] = value;
+
+        console.log(`[SVG-FIX] Field ${technicalName} changed to:`, value);
+
+        // Debounce the SVG update to prevent excessive renders
+        updateTimeout = setTimeout(() => {
+            renderSvg().catch(error => console.error('[SVG-FIX] Error in field change render:', error));
+        }, 300); // 300ms delay
+    }
+
+    // Set up mutation observer to watch for dynamic field changes
+    function setupMutationObserver() {
+        // Watch for changes in field values that happen programmatically
+        const observer = new MutationObserver(function (mutations) {
+            let shouldUpdate = false;
+
+            mutations.forEach(function (mutation) {
+                if (mutation.type === 'attributes' &&
+                    (mutation.attributeName === 'value' || mutation.attributeName === 'checked')) {
+                    const field = mutation.target;
+                    if (field.classList.contains('config-field')) {
+                        const technicalName = field.getAttribute('data-technical-name');
+                        if (technicalName) {
+                            const fieldType = field.getAttribute('data-field-type');
+                            const newValue = getFieldValue(field, fieldType);
+
+                            // Check if value actually changed
+                            if (fieldValues[technicalName] !== newValue) {
+                                fieldValues[technicalName] = newValue;
+                                console.log(`[SVG-FIX] Dynamic field change detected: ${technicalName} = ${newValue}`);
+                                shouldUpdate = true;
+                            }
+                        }
+                    }
+                }
+            });
+
+            if (shouldUpdate) {
+                // Debounce the update
                 if (updateTimeout) {
                     clearTimeout(updateTimeout);
                 }
+                updateTimeout = setTimeout(() => {
+                    renderSvg().catch(error => console.error('[SVG-FIX] Error in mutation observer render:', error));
+                }, 300);
+            }
+        });
 
-                // Get the field value
-                let value = getFieldValue(field, fieldType);
+        // Start observing all config fields for attribute changes
+        const configFields = document.querySelectorAll('.config-field');
+        configFields.forEach(field => {
+            observer.observe(field, {
+                attributes: true,
+                attributeFilter: ['value', 'checked']
+            });
+        });
 
-                // Update the field value in the global object
-                fieldValues[technicalName] = value;
+        // Also set up periodic checking for field value changes
+        setInterval(checkForFieldValueChanges, 1000); // Check every second
+    }
 
-                // Debounce the SVG update to prevent excessive renders
-                updateTimeout = setTimeout(() => {
-                    renderSvg().catch(error => console.error('[SVG-FIX] Error in field change render:', error));
-                }, 300); // 300ms delay
-            });
+    // Check for field value changes that might not trigger events
+    function checkForFieldValueChanges() {
+        const configFields = document.querySelectorAll('.config-field');
+        let hasChanges = false;
+
+        configFields.forEach(field => {
+            const technicalName = field.getAttribute('data-technical-name');
+            const fieldType = field.getAttribute('data-field-type');
+
+            if (!technicalName) return;
+
+            const currentValue = getFieldValue(field, fieldType);
+            const storedValue = fieldValues[technicalName];
+
+            if (currentValue !== storedValue) {
+                fieldValues[technicalName] = currentValue;
+                console.log(`[SVG-FIX] Periodic check detected change: ${technicalName} = ${currentValue}`);
+                hasChanges = true;
+            }
         });
+
+        if (hasChanges) {
+            // Debounce the update
+            if (updateTimeout) {
+                clearTimeout(updateTimeout);
+            }
+            updateTimeout = setTimeout(() => {
+                renderSvg().catch(error => console.error('[SVG-FIX] Error in periodic check render:', error));
+            }, 300);
+        }
     }
 
     // Get the value of a field based on its type
@@ -163,6 +270,12 @@ document.addEventListener('DOMContentLoaded', function() {
                 if (!field.value || field.value === '-- Select an option --' || field.selectedIndex === 0) {
                     return '';
                 }
+                // Get the actual option value from data-option-value attribute
+                const selectedOption = field.options[field.selectedIndex];
+                return selectedOption ? (selectedOption.getAttribute('data-option-value') || selectedOption.value) : field.value;
+            case 'date':
+                return field.value; // Date in YYYY-MM-DD format
+            case 'text':
                 return field.value;
             default:
                 return field.value;
@@ -508,12 +621,30 @@ document.addEventListener('DOMContentLoaded', function() {
     // Initialize after a short delay to ensure DOM is ready
     setTimeout(initialize, 1000);
 
+    // Re-scan for new fields that might be added dynamically
+    function rescanFields() {
+        console.log('[SVG-FIX] Re-scanning for new fields...');
+        addFieldEventListeners();
+        setupMutationObserver();
+    }
+
+    // Force update SVG with current field values
+    function forceUpdateSvg() {
+        console.log('[SVG-FIX] Force updating SVG...');
+        collectFieldValues();
+        renderSvg().catch(error => console.error('[SVG-FIX] Error in force update:', error));
+    }
+
     // Make functions available globally
     window.svgRendererFix = {
         initialize,
         loadSvgComponents,
         renderSvg,
         evaluateCondition,
-        fieldValues
+        fieldValues,
+        rescanFields,
+        forceUpdateSvg,
+        handleFieldChange,
+        getFieldValue
     };
 });
diff --git a/canbrax_configmatrix/static/src/js/visibility_conditions.js b/canbrax_configmatrix/static/src/js/visibility_conditions.js
index a51c6cf..c6fcaf2 100644
--- a/canbrax_configmatrix/static/src/js/visibility_conditions.js
+++ b/canbrax_configmatrix/static/src/js/visibility_conditions.js
@@ -106,6 +106,51 @@
         lastFieldValuesHash = null;
     }
 
+    // NEW: Extract field dependencies from formulas (same as in configurator.js)
+    function extractFieldDependencies(formula) {
+        const dependencies = new Set();
+
+        try {
+            // Handle JSON conditions
+            if (formula.startsWith('__JSON__')) {
+                const jsonStr = formula.substring(8);
+                const conditions = JSON.parse(jsonStr);
+
+                if (Array.isArray(conditions)) {
+                    conditions.forEach(condition => {
+                        if (condition.field) {
+                            dependencies.add(condition.field);
+                        }
+                    });
+                }
+                return Array.from(dependencies);
+            }
+
+            // Extract field names from the formula using regex
+            // Look for field names that are not in quotes or function calls
+            const fieldPattern = /\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g;
+            let match;
+
+            while ((match = fieldPattern.exec(formula)) !== null) {
+                const fieldName = match[1];
+
+                // Skip built-in functions and constants
+                const builtIns = ['min', 'max', 'abs', 'round', 'ceil', 'floor', 'sqrt', 'pow',
+                    'log', 'log10', 'exp', 'sin', 'cos', 'tan', 'pi', 'e',
+                    'parseFloat', 'parseInt', 'Number', 'Math', 'find_midrail_height',
+                    'true', 'false', 'null', 'undefined'];
+
+                if (!builtIns.includes(fieldName)) {
+                    dependencies.add(fieldName);
+                }
+            }
+        } catch (error) {
+            console.error('Error extracting field dependencies:', error);
+        }
+
+        return Array.from(dependencies);
+    }
+
     // CRITICAL FIX: Add missing loadCalculatedFields function
     async function loadCalculatedFields() {
         try {
@@ -223,7 +268,9 @@
 
         // PRIORITY 1: Try to get results from backend first
         // This avoids the ReferenceError issues with missing dependencies
-        if (window.backendCalculatedFields) {
+        // CRITICAL FIX: Only use backend fields if we're not forcing recalculation
+        // Backend fields might be outdated when field values have changed
+        if (!forceRecalculate && window.backendCalculatedFields) {
             Object.assign(results, window.backendCalculatedFields);
         }
 
@@ -254,9 +301,9 @@
                 }
             };
 
-            // Calculate server-defined fields in sequence order with multiple passes
-            // to handle dependencies (only if no backend results available)
-            const maxPasses = window.backendCalculatedFields ? 0 : 5; // Skip if we have backend results
+            // CRITICAL FIX: Always calculate frontend fields, even if backend fields exist
+            // Backend fields might be outdated or missing some calculations
+            const maxPasses = 10; // Increased passes to handle complex dependencies
             let hasChanges = true;
             let pass = 0;
 
@@ -266,14 +313,29 @@
 
                 calculatedFieldsDefinitions.forEach((calcField, index) => {
                     try {
-                        // Skip if we already have this field from backend
-                        if (results[calcField.name] !== undefined) {
+                        // Skip if we already have this field from backend with a non-null value
+                        // Only skip if the value is not null/undefined (meaning it was actually calculated)
+                        if (results[calcField.name] !== undefined && results[calcField.name] !== null) {
+                            return;
+                        }
+
+                        // CRITICAL FIX: Check if all dependencies are available before calculating
+                        const calcFieldDependencies = extractFieldDependencies(calcField.formula);
+                        const missingDependencies = calcFieldDependencies.filter(dep =>
+                            dep.startsWith('_CALCULATED_') &&
+                            (results[dep] === undefined || results[dep] === null)
+                        );
+
+                        if (missingDependencies.length > 0) {
                             return;
                         }
 
                         // Update context with latest results
                         Object.assign(context, results);
 
+                        // Check if the field depends on the changed field
+                        const fieldDependencies = extractFieldDependencies(calcField.formula);
+
                         // Create a safe context that returns null for undefined variables
                         const safeContext = new Proxy(context, {
                             get: (target, prop) => {
@@ -314,6 +376,7 @@
                         const func = new Function(...argNames, safeFormula);
                         const result = func(...argValues);
                         
+
                         // Only update if the result has changed
                         if (results[calcField.name] !== result) {
                             results[calcField.name] = result;
@@ -1984,6 +2047,26 @@
                     // Immediate UI updates (synchronous for good UX)
                     updateImmediateUI();
 
+                    // CRITICAL FIX: Force recalculation of calculated fields immediately
+                    // This ensures calculated fields are updated when their dependencies change
+                    try {
+
+                        // Invalidate cache to force fresh calculation
+                        invalidateCalculatedFieldsCache();
+
+                        const calculatedFields = calculateDynamicFields(fieldValues, true);
+
+                        // Update fieldValues with new calculated field values
+                        for (const [fieldName, value] of Object.entries(calculatedFields)) {
+                            if (fieldName.startsWith('_CALCULATED_')) {
+                                fieldValues[fieldName] = value;
+                            }
+                        }
+
+                    } catch (error) {
+                        console.error('[CALC] Error recalculating calculated fields:', error);
+                    }
+
                     // Heavy operations (asynchronous to prevent blocking)
                     // Note: Pass event to exclude triggering field from dynamic defaults
                     updateHeavyOperationsAsync().then(() => {
@@ -2400,10 +2483,14 @@
         // Clear existing content
         tableBody.innerHTML = '';
 
+        // CRITICAL FIX: Force recalculation by invalidating cache first
+        // This ensures we get the most up-to-date calculated field values
+        invalidateCalculatedFieldsCache();
+
         // Calculate dynamic fields to get current values
         let calculatedFields = {};
         try {
-            calculatedFields = calculateDynamicFields(fieldValues);
+            calculatedFields = calculateDynamicFields(fieldValues, true);
         } catch (error) {
             console.error('Error calculating fields for panel:', error);
         }
@@ -2413,6 +2500,8 @@
             .filter(([key, value]) => key.startsWith('_CALCULATED_'))
             .sort(([a], [b]) => a.localeCompare(b)); // Sort alphabetically
 
+        console.log('[CALC-PANEL] Filtered calculated fields:', calculatedFieldsOnly);
+
         if (calculatedFieldsOnly.length === 0) {
             // Show empty state
             const row = document.createElement('tr');
