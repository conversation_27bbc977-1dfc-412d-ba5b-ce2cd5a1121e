#!/usr/bin/env python3
"""
Test operation costs to trigger the logging and see if midrail operations are included
"""
import requests
import json

def test_operation_costs():
    """Test operation costs calculation to see if midrail operations are included"""
    
    # Test field values (same as in our test case)
    field_values = {
        "bx_dbl_hinge_location": "brisbane",
        "bx_dbl_hinge_jamb_reveal_gt_20mm": "yes_gt_20mm",
        "bx_dbl_hinge_new_door_clear_handle": "yes",
        "bx_dbl_hinge_handle_clear_existing": "yes",
        "bx_dbl_hinge_swing_path_clear": "yes",
        "bx_dbl_hinge_quantity": 1,
        "bx_dbl_hinge_frame_colour": "black_anodise_15mm",
        "bx_dbl_hinge_mesh_type": "pet_mesh",
        "bx_dbl_hinge_door_height_mm": 2100,
        "bx_dbl_hinge_door_width_mm": 1200,
        "bx_dbl_hinge_num_sec_hinges_pickup": 0,
        "bx_dbl_hinge_num_sec_hinges_deliver": "3_per_door_loose_drilled",
        "bx_dbl_hinge_sec_hinge_packers_qty": 0
    }
    
    print("=== TESTING OPERATION COSTS WITH MIDRAIL FIX ===")
    print(f"Input: height={field_values.get('bx_dbl_hinge_door_height_mm')}, width={field_values.get('bx_dbl_hinge_door_width_mm')}")
    
    # Test operation costs endpoint
    try:
        response = requests.post('http://localhost:8069/config_matrix/calculate_operation_costs', 
                               json={
                                   "jsonrpc": "2.0",
                                   "method": "call",
                                   "params": {
                                       "template_id": 24,
                                       "field_values": field_values
                                   }
                               },
                               headers={'Content-Type': 'application/json'})
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response keys: {list(result.keys())}")
            
            if 'result' in result:
                operation_result = result['result']
                print(f"Operation result keys: {list(operation_result.keys())}")
                
                if operation_result.get('success'):
                    operations = operation_result.get('operations', [])
                    total_cost = operation_result.get('total_cost', 0)
                    print(f"✅ Operation costs calculated: {len(operations)} operations, ${total_cost}")
                    
                    # Check for midrail operations
                    midrail_operations = [op for op in operations if 'midrail' in op.get('name', '').lower()]
                    print(f"Midrail operations found: {len(midrail_operations)}")
                    for op in midrail_operations:
                        print(f"  - {op.get('name', 'Unknown')}: ${op.get('cost', 0)}")
                        
                    if midrail_operations:
                        print(f"🎉 SUCCESS: Midrail operations are now being included!")
                    else:
                        print(f"❌ No midrail operations found. Let's check all operations:")
                        for op in operations[:10]:  # Show first 10 operations
                            print(f"  - {op.get('name', 'Unknown')}: ${op.get('cost', 0)}")
                else:
                    print(f"❌ Operation costs failed: {operation_result.get('error')}")
            else:
                print(f"❌ Unexpected response format: {result}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Error testing operation costs: {e}")

if __name__ == "__main__":
    test_operation_costs()
