description: Comprehensive AI development context and guidelines for the Canbrax ConfigMatrix Odoo 18 product configuration system
globs: ["**/*.py", "**/*.xml", "**/*.js", "**/*.css"]
alwaysApply: true
---

# Canbrax ConfigMatrix Project - AI Development Context & Guidelines

## 🎯 Project Overview

**Canbrax ConfigMatrix** is a comprehensive Odoo 18 product configuration system that provides advanced dynamic configuration capabilities with automatic BOM generation, pricing matrices, visual previews, and seamless integration with sales and manufacturing workflows.

### Core Purpose
- **Dynamic Product Configuration**: Intuitive Q&A format with conditional visibility
- **Automatic BOM Generation**: Real-time Bill of Materials creation based on configuration choices
- **Multi-Use Case Support**: Check Measure, Sales/Quoting, and Online Sales configurations
- **Visual Product Previews**: SVG-based visual representations with conditional layers
- **Advanced Pricing System**: Multi-dimensional pricing matrices with labor time calculations
- **Dynamic Content System**: Context-sensitive help, error messages, and default values
- **Component Mapping**: Flexible 1:many component relationships with dynamic conditions
- **Performance Optimization**: Caching, debouncing, and memory management for large configurations

## 🏗️ Architecture Context

### Module Structure
```
canbrax_configmatrix/
├── models/                    # Core business logic and data models
├── controllers/              # Portal and API controllers
├── views/                    # Backend UI views and templates
├── wizards/                  # Administrative wizards and tools
├── static/                   # Frontend assets (JS, CSS, XML)
├── security/                 # Access rights and record rules
├── data/                     # Initial data and configurations
├── tests/                    # Unit and integration tests
└── docs_extend/              # Comprehensive documentation
```

### Key Models (Core Data Structure)

#### Core Configuration Models
- **`config.matrix.template`**: Central configuration template for products with lifecycle management (Draft → Testing → Active → Archived)
- **`config.matrix.section`**: Logical grouping of configuration fields (e.g., Door, Hardware, Extrusions)
- **`config.matrix.field`**: Individual configuration questions with advanced features (text, number, selection, boolean, date)
- **`config.matrix.option`**: Selection choices for fields with individual component mappings
- **`config.matrix.configuration`**: Saved configuration instances with field values and generated BOMs

#### Advanced Feature Models
- **`config.matrix.visibility.condition`**: Complex visibility rules for fields and options
- **`config.matrix.calculated.field`**: Formula-based computed fields with dependency tracking
- **`config.matrix.component.mapping`**: Component relationships and BOM generation
- **`config.matrix.field.component.mapping`**: 1:many component mappings for individual fields

#### Pricing & Manufacturing Models
- **`config.matrix.price.matrix`**: Multi-dimensional pricing calculations based on height/width
- **`config.matrix.labor.time.matrix`**: Labor time and cost calculations for manufacturing
- **`config.matrix.category`**: Organizes pricing and labor matrices by type and purpose

#### Visual & Integration Models
- **`config.matrix.svg.component`**: Vector-based visual representations for product previews
- **Sale Order Line Extension**: Configuration support in sales workflow
- **Product Template Extension**: Configuration flags and matrix associations

## 🔧 Development Standards

### Odoo 18 Compliance (CRITICAL)
- **Use `<list>` instead of `<tree>`** for list views
- **Use direct attributes** instead of `attrs` (e.g., `invisible="condition"`)
- **Use `self.env._()`** for translations (not `_()`)
- **Follow Odoo 18 XML view structure** with modern syntax
- **Escape comparison operators** in XML templates (`&lt;`, `&gt;`, `&amp;`)
- **Use proper label tags** with `for` attribute or `class="o_form_label"`
- **Quote widget options** with single quotes: `options="{'mode': 'json'}"`
- **Use proper button attributes**: `type="object"`, `string="Button Text"`

### Python Code Standards
```python
# ✅ CORRECT - Odoo 18 patterns
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Use computed fields with proper dependencies
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(len(section.field_ids) for section in template.section_ids)
    
    # Use selection_add for extending existing selections
    state = fields.Selection(selection_add=[
        ('archived', 'Archived')
    ], ondelete={'archived': 'set draft'})
    
    # Use constraints for data integrity
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
```

### Frontend Development (OWL 2.0)
```javascript
/** @odoo-module **/
import { Component, useState, onMounted, useRef } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class ConfigMatrixConfigurator extends Component {
    static template = "canbrax_configmatrix.Configurator";
    
    setup() {
        this.state = useState({
            loading: false,
            error: false,
            fieldValues: {},
            calculatedValues: {},
        });
        
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        
        // Use debouncing for performance
        this.debouncedUpdate = this.debounce(this.updateCalculatedFields, 300);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
```

### Widget Registration (CRITICAL)
```javascript
// ✅ CORRECT - Use object with component property
registry.category("fields").add("config_matrix_widget", {
    component: ConfigMatrixWidget,
});

// ❌ INCORRECT - Direct class registration causes OWL errors
registry.category("fields").add("config_matrix_widget", ConfigMatrixWidget);
```

## 🎨 UI/UX Patterns

### Configuration Interface
- **Q&A Format**: Intuitive question-based configuration flow
- **Conditional Visibility**: Fields appear/disappear based on previous answers
- **Real-time Validation**: Immediate feedback on field values
- **Visual Preview**: SVG-based product previews that update dynamically
- **Progress Indicators**: Show configuration completion status

### Administrative Interface
- **Template Management**: Comprehensive template creation and editing
- **Matrix Editors**: Visual Excel-like matrix editing for pricing and labor
- **Component Mapping**: Drag-and-drop component relationship management
- **Import/Export Tools**: Excel and JSON data import/export capabilities

## 🔐 Security & Access Control

### User Groups
- **ConfigMatrix User**: Basic configuration access
- **ConfigMatrix Administrator**: Full administrative access
- **ConfigMatrix Builder**: Builder portal access
- **Portal Users**: Customer-facing configuration access

### Record Rules
```xml
<!-- Example: Portal users can only access their configurations -->
<record id="config_matrix_rule_portal" model="ir.rule">
    <field name="name">ConfigMatrix: portal users can only access their configurations</field>
    <field name="model_id" ref="model_config_matrix_configuration"/>
    <field name="domain_force">[('create_uid', '=', user.id)]</field>
    <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
</record>
```

## 🔄 Integration Patterns

### Sales Integration
- **Sales Order Lines**: "Configure" button appears for configurable products
- **Configuration Flow**: Seamless integration with sales workflow
- **Pricing Integration**: Real-time pricing based on configuration choices
- **BOM Generation**: Automatic component list generation

### Manufacturing Integration
- **Manufacturing Orders**: Configured BOMs flow directly to manufacturing
- **Component Tracking**: Full traceability of configuration to components
- **Labor Calculations**: Automatic labor time and cost calculations
- **Quality Control**: Configuration details available for quality checks

### Portal Integration
- **Customer Portal**: Customer-facing configuration interface
- **Builder Portal**: Specialized interface for professional builders
- **Configuration History**: Save and retrieve previous configurations
- **Real-time Pricing**: Live pricing updates during configuration

## 📊 Advanced Features Context

### Dynamic Content System
- **Dynamic Help Text**: Context-sensitive help using field placeholders (e.g., `"Width should be {door_height} + 100mm"`)
- **Dynamic Error Messages**: Custom error messages based on field values with conditions (e.g., `width > height`)
- **Dynamic Default Values**: Intelligent defaults based on other fields using expressions (e.g., `door_height * 0.8`)
- **Dynamic Range Validation**: Expression-based min/max values with complex conditions

### Calculated Fields
- **Formula-based Computations**: Complex calculations using field expressions with dependency tracking
- **Dependency Tracking**: Automatic updates when dependencies change with efficient caching
- **Performance Optimization**: Caching and efficient computation using `@tools.ormcache`

### Component Mapping
- **1:Many Relationships**: Flexible component relationships with multiple products per field
- **Dynamic Conditions**: Include components based on configuration values using expressions
- **Quantity Formulas**: Calculate component quantities using expressions (e.g., `width / 1000`)
- **Product Filtering**: Dynamic product selection based on reference values and filter domains

### Pricing & Labor System
- **Multi-dimensional Matrices**: Complex pricing based on height/width dimensions with range-based lookup
- **Labor Time Calculations**: Automatic labor cost and time calculations for manufacturing operations
- **Matrix Categories**: Organized pricing by type (Door Frame, Hardware, Glass & Panel, Labor Operations)
- **Special Conditions**: Handle complex pricing scenarios (mid-rails, cross-braces, etc.)

### Visibility Conditions
- **Simple Conditions**: Show/hide based on field values (e.g., `door_type == "sliding"`)
- **Range Conditions**: Show/hide based on value ranges (e.g., `width >= 800 and width <= 1200`)
- **Complex Conditions**: Multiple field dependencies with logical operators
- **Use Case Specific**: Different visibility rules for Check Measure, Sales, and Online use cases

## 🚀 Performance Considerations

### Optimization Patterns
- **Caching**: Use computed fields with proper dependencies and `@tools.ormcache` for expensive operations
- **Debouncing**: Implement debouncing for expensive operations (300ms for calculated fields, 200ms for visibility)
- **Memory Management**: Efficient state management in OWL components with proper cleanup
- **Database Optimization**: Proper indexing and query optimization with prefetching

### Large Configuration Handling
- **Lazy Loading**: Load configuration data on demand with progressive enhancement
- **Pagination**: Handle large numbers of fields and options efficiently
- **Progressive Enhancement**: Load advanced features progressively
- **Query Optimization**: Use `with_context(prefetch_fields=True)` for related record access

### Performance Monitoring
- **Query Analysis**: Monitor database queries and response times
- **Memory Usage**: Monitor memory consumption for large configurations
- **Caching Efficiency**: Track cache hit rates and performance metrics
- **User Experience**: Monitor response times and user interaction patterns

## 🧪 Testing & Quality Assurance

### Testing Requirements
- **Unit Tests**: Test all model methods and business logic with proper test cases
- **Integration Tests**: Test complete configuration workflows from sales to manufacturing
- **Performance Tests**: Validate performance with large configurations (100+ fields)
- **User Acceptance Tests**: Validate user workflows and scenarios across all use cases
- **Load Testing**: Test system performance under heavy configuration loads

### Code Quality
- **PEP 8 Compliance**: Follow Python coding standards strictly
- **Documentation**: Comprehensive docstrings and comments for all public APIs
- **Error Handling**: Proper exception handling and user feedback with logging
- **Security**: Validate all user inputs and access controls with expression validation
- **Input Validation**: Check for dangerous patterns in dynamic expressions

### Testing Patterns
```python
class TestConfigMatrixTemplate(TransactionCase):
    def setUp(self):
        super().setUp()
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'product_template_id': self.env['product.template'].create({
                'name': 'Test Product'
            }).id,
        })
    
    def test_field_visibility(self):
        """Test field visibility conditions"""
        field = self.env['config.matrix.field'].create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.env['config.matrix.section'].create({
                'name': 'Test Section',
                'matrix_id': self.template.id,
            }).id,
            'visibility_condition': 'test_value == "visible"',
        })
        
        self.assertTrue(field.evaluate_visibility({'test_value': 'visible'}))
        self.assertFalse(field.evaluate_visibility({'test_value': 'hidden'}))
```

## 📚 Documentation Standards

### Code Documentation
- **Model Documentation**: Clear docstrings for all models and methods
- **API Documentation**: Document all public APIs and interfaces
- **Configuration Documentation**: Document configuration options and settings
- **User Documentation**: Comprehensive user guides and tutorials

### Technical Documentation
- **Architecture Documentation**: System design and component relationships
- **Integration Documentation**: Integration patterns and workflows
- **Performance Documentation**: Performance characteristics and optimization
- **Security Documentation**: Security model and access controls

## 🔧 Development Workflow

### Feature Development
1. **Requirements Analysis**: Understand business requirements and user needs
2. **Design**: Design data models, UI components, and integration points
3. **Implementation**: Implement backend models, frontend components, and integrations
4. **Testing**: Comprehensive testing across all use cases
5. **Documentation**: Update documentation and user guides
6. **Deployment**: Deploy with appropriate user training

### Code Review Process
- **Odoo 18 Compliance**: Ensure all code follows Odoo 18 standards
- **Security Review**: Validate security controls and access rights
- **Performance Review**: Check for performance implications
- **User Experience Review**: Validate UI/UX and user workflows
- **Documentation Review**: Ensure documentation is complete and accurate

## 🎯 Key Development Principles

### User-Centric Design
- **Intuitive Interface**: Easy-to-use configuration interface
- **Progressive Disclosure**: Show complexity only when needed
- **Real-time Feedback**: Immediate validation and preview updates
- **Error Prevention**: Prevent errors through smart defaults and validation

### Scalability & Maintainability
- **Modular Architecture**: Clear separation of concerns
- **Extensible Design**: Easy to extend with new features
- **Performance Optimization**: Efficient handling of large configurations
- **Code Quality**: Maintainable and well-documented code

### Integration & Compatibility
- **Odoo Integration**: Seamless integration with Odoo ecosystem
- **API Design**: Clean and consistent API design
- **Backward Compatibility**: Maintain compatibility with existing data
- **Future-Proofing**: Design for future Odoo versions

## 🚨 Critical Development Rules

### NEVER Do These
- ❌ Use deprecated `<tree>` tags (use `<list>` instead)
- ❌ Use `attrs` attributes in XML views (use direct attributes)
- ❌ Use `_()` for translations (use `self.env._()`)
- ❌ Put business logic in `@api.onchange` methods
- ❌ Skip error handling and validation
- ❌ Ignore performance implications for large configurations
- ❌ Use unescaped comparison operators in XML templates (`<`, `>`, `&`)
- ❌ Create label tags without `for` attribute or `class="o_form_label"`
- ❌ Register widgets directly without component property
- ❌ Use inline templates in OWL components (use separate XML files)
- ❌ Skip input validation for dynamic expressions

### ALWAYS Do These
- ✅ Follow Odoo 18 syntax and patterns strictly
- ✅ Implement proper security and access controls
- ✅ Use computed fields with proper dependencies
- ✅ Implement comprehensive error handling
- ✅ Document all public APIs and interfaces
- ✅ Test across all use cases and scenarios
- ✅ Consider performance implications
- ✅ Follow user-centric design principles
- ✅ Escape comparison operators in XML templates (`&lt;`, `&gt;`, `&amp;`)
- ✅ Use proper label tags with `for` attribute or `class="o_form_label"`
- ✅ Register widgets with component property: `{component: WidgetClass}`
- ✅ Use separate XML template files for OWL components
- ✅ Validate all dynamic expressions for security
- ✅ Use proper button attributes: `type="object"`, `string="Button Text"`
- ✅ Quote widget options with single quotes: `options="{'mode': 'json'}"`

## 📖 Reference Documentation

### Core Documentation Files
- **`00_OVERVIEW.md`**: System architecture and core concepts
- **`01_DATA_MODELS.md`**: Complete data model reference with all models and relationships
- **`06_DEVELOPER_GUIDE.md`**: Technical implementation details, patterns, and best practices
- **`07_USER_GUIDE.md`**: Complete user workflows and instructions for all user types
- **`ODOO_18_GUIDELINES.md`**: Odoo 18 specific standards, syntax changes, and compliance requirements

### Feature-Specific Documentation
- **`03_BOM_GENERATION.md`**: BOM creation and manufacturing integration
- **`04_MANUFACTURING_ORDER_CREATION.md`**: Manufacturing workflow and order creation
- **`10_WEBSITE_PORTAL.md`**: Portal integration and customer interface
- **`calcuated_fields.md`**: Formula-based computed fields and dependency tracking
- **`MANAGE_PRICING_COMPLETE.md`**: Pricing matrix management and calculations
- **`12_DYNAMIC_FIELD_MATCHING.md`**: Dynamic field behavior and conditional logic
- **`svg_component_guide.md`**: Visual component rendering and SVG handling

### Technical Reference
- **`python_dictionary.md`**: Python code patterns, data structures, and implementation examples
- **`PERFORMANCE_TESTING_GUIDE.md`**: Performance testing procedures and benchmarks
- **`LOGGING_IMPLEMENTATION_GUIDE.md`**: Logging and debugging implementation
- **`codebase_analysis_cleanup_plan.md`**: Code quality analysis and optimization recommendations
- **`dynamic_error_messages_examples.md`**: Error handling patterns and user feedback examples

### Development & Project Management
- **`22june_tasks.md`**: Project tasks and development milestones
- **`22june_solutions.js`**: JavaScript solutions and implementation examples
- **`PERFORMANCE_ANALYSIS_AUTO_QUESTIONS.md`**: Automated performance analysis questions
- **`PERFORMANCE_FIX_IMPLEMENTATION_PLAN.md`**: Performance improvement strategies

## 🔧 Implementation Examples

### Dynamic Content Implementation
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    def generate_dynamic_help(self, values=None, use_case='check_measure'):
        """Generate dynamic help text using field placeholders"""
        if not values:
            values = {}
        
        template_field = f'{use_case}_dynamic_help_template'
        template = getattr(self, template_field, '')
        
        if not template:
            return ''
        
        # Replace placeholders with actual values
        def replace_expression(match):
            field_name = match.group(1)
            if field_name in values:
                return str(values[field_name])
            return f'{{{field_name}}}'
        
        import re
        return re.sub(r'\{([^}]+)\}', replace_expression, template)
```

### Visibility Condition Evaluation
```python
@tools.ormcache('self.id', 'values_hash')
def evaluate_visibility_cached(self, values_hash, field_values):
    """Cached visibility evaluation for performance"""
    return self._evaluate_visibility_uncached(field_values)

def _evaluate_visibility_uncached(self, field_values):
    """Evaluate field visibility based on conditions"""
    if not self.visibility_condition:
        return True
    
    try:
        # Create safe context for evaluation
        safe_context = {
            'values': field_values,
            'field_values': field_values,
            'config_values': field_values,
        }
        
        # Add field values as individual variables
        for key, value in field_values.items():
            safe_context[key] = value
        
        # Evaluate the condition
        result = safe_eval(self.visibility_condition, safe_context)
        return bool(result)
    except Exception as e:
        _logger.error(f"Error evaluating visibility condition: {e}")
        return True
```

### Component Mapping with Dynamic Selection
```python
class ConfigMatrixFieldComponentMapping(models.Model):
    _name = 'config.matrix.field.component.mapping'
    
    def get_filtered_products(self, reference_value=None, field_values=None):
        """Get filtered products based on dynamic conditions"""
        if not self.is_dynamic:
            return self.component_product_id
        
        try:
            # Build domain from filter_domain
            domain = []
            if self.filter_domain:
                domain = safe_eval(self.filter_domain, {
                    'reference_value': reference_value,
                    'field_values': field_values or {},
                })
            
            # Add reference value condition if specified
            if self.reference_value and reference_value:
                domain.append(('name', 'ilike', reference_value))
            
            # Search for products
            products = self.env['product.product'].search(domain)
            return products
        except Exception as e:
            _logger.error(f"Error filtering products: {e}")
            return self.component_product_id
```

### Pricing Matrix Lookup
```python
class ConfigMatrixPriceMatrix(models.Model):
    _name = 'config.matrix.price.matrix'
    
    def get_price_for_dimensions(self, height, width):
        """Get price for specific dimensions"""
        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            matrix_data = json.loads(self.matrix_data) if self.matrix_data else {}
            
            # Find matching height range
            height_range = None
            for hr in height_ranges:
                if hr['min'] <= height <= hr['max']:
                    height_range = hr
                    break
            
            # Find matching width range
            width_range = None
            for wr in width_ranges:
                if wr['min'] <= width <= wr['max']:
                    width_range = wr
                    break
            
            if not height_range or not width_range:
                return 0.0
            
            # Get price from matrix
            key = f"height_{height_range['label']}_width_{width_range['label']}"
            price = matrix_data.get(key, 0.0)
            
            return float(price)
        except Exception as e:
            _logger.error(f"Error getting price for dimensions: {e}")
            return 0.0
```

---

**Remember**: This is a sophisticated product configuration system used in production. Always prioritize stability, performance, and user experience. When in doubt, refer to the comprehensive documentation in the `docs_extend/` folder.

## 🚨 Quick Reference: Common XML Validation Errors

| Error Message | Solution |
|---------------|----------|
| `Label tag must contain a "for"` | Add `for="field_name"` to label and `id="field_name"` to field |
| `Unescaped '<' not allowed` | Replace `<` with `&lt;` and `>` with `&gt;` |
| `Field "field_name" does not exist` | Add missing field to model or remove from view |
| `action_method_name is not a valid action` | Add missing method to model |
| `Invalid widget options` | Quote widget option values with single quotes |
| `'tree' is not a valid view type` | Replace `<tree>` with `<list>` |
| `'attrs' attribute is deprecated` | Use direct attributes instead of attrs |
| `Missing template for component` | Remove custom widget or create proper template |
| `a mandatory field is not set` | Make field non-required or provide default values |
