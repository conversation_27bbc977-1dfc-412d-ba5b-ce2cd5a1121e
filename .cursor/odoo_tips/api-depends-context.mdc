# Odoo 18 @api.depends_context Best Practices

## Context
This rule enforces best practices for using `@api.depends_context` decorator in Odoo 18 development, ensuring proper implementation of context-aware computed fields and optimal performance.

## Input
- Python model files with computed fields
- Methods that depend on context values
- Computed fields that need context awareness

## Output
- Properly implemented context-dependent computed fields
- Optimized field computation based on context changes
- Maintainable and performant code

## Core Standards

### 1. When to Use @api.depends_context

#### ✅ Use @api.depends_context when:
```python
# ✅ CORRECT - Context-dependent computed fields
class ProductTemplate(models.Model):
    _name = 'product.template'
    
    # Field depends on user language context
    @api.depends_context('lang')
    def _compute_translated_name(self):
        for product in self:
            product.translated_name = product.with_context(
                lang=self.env.context.get('lang')
            ).name
    
    # Field depends on custom context keys
    @api.depends_context('show_price', 'currency_id')
    def _compute_display_price(self):
        for product in self:
            show_price = self.env.context.get('show_price', True)
            currency_id = self.env.context.get('currency_id')
            if show_price and currency_id:
                product.display_price = product.list_price
            else:
                product.display_price = 0.0
```

#### ❌ Don't use @api.depends_context when:
```python
# ❌ WRONG - Field doesn't depend on context
@api.depends_context('lang')  # Unnecessary
def _compute_total_amount(self):
    for record in self:
        record.total_amount = record.quantity * record.unit_price

# ❌ WRONG - Using for stored fields without context dependency
@api.depends_context('lang')  # Won't work for stored fields
def _compute_stored_field(self):
    for record in self:
        record.stored_field = record.name  # No context dependency
```

### 2. Proper Implementation Patterns

#### Basic Context Dependency
```python
# ✅ CORRECT - Basic context dependency
class SaleOrder(models.Model):
    _name = 'sale.order'
    
    @api.depends_context('lang')
    def _compute_translated_notes(self):
        for order in self:
            order.translated_notes = order.with_context(
                lang=self.env.context.get('lang')
            ).notes
```

#### Multiple Context Dependencies
```python
# ✅ CORRECT - Multiple context dependencies
class ProductTemplate(models.Model):
    _name = 'product.template'
    
    @api.depends_context('lang', 'currency_id', 'show_price')
    def _compute_localized_info(self):
        for product in self:
            # Language context
            lang = self.env.context.get('lang')
            # Currency context
            currency_id = self.env.context.get('currency_id')
            # Display context
            show_price = self.env.context.get('show_price', True)
            
            # Compute based on all context values
            product.localized_name = product.with_context(lang=lang).name
            if show_price and currency_id:
                product.localized_price = product.with_context(
                    currency_id=currency_id
                ).list_price
```

#### Combined with @api.depends
```python
# ✅ CORRECT - Combining with field dependencies
class SaleOrderLine(models.Model):
    _name = 'sale.order.line'
    
    @api.depends('product_id', 'quantity', 'price_unit')
    @api.depends_context('currency_id', 'lang')
    def _compute_total_amount(self):
        for line in self:
            # Field dependencies
            base_amount = line.quantity * line.price_unit
            
            # Context dependencies
            currency_id = self.env.context.get('currency_id')
            if currency_id:
                line.total_amount = line.order_id.currency_id.compute(
                    base_amount, currency_id
                )
            else:
                line.total_amount = base_amount
```

### 3. Common Use Cases

#### Language-Dependent Fields
```python
# ✅ CORRECT - Language-dependent computed fields
class ProductTemplate(models.Model):
    _name = 'product.template'
    
    @api.depends_context('lang')
    def _compute_localized_description(self):
        for product in self:
            lang = self.env.context.get('lang')
            if lang:
                product.localized_description = product.with_context(
                    lang=lang
                ).description
            else:
                product.localized_description = product.description
```

#### Currency-Dependent Fields
```python
# ✅ CORRECT - Currency-dependent computed fields
class SaleOrder(models.Model):
    _name = 'sale.order'
    
    @api.depends_context('currency_id')
    def _compute_currency_amount(self):
        for order in self:
            currency_id = self.env.context.get('currency_id')
            if currency_id and currency_id != order.currency_id.id:
                order.context_currency_amount = order.currency_id.compute(
                    order.amount_total, currency_id
                )
            else:
                order.context_currency_amount = order.amount_total
```

#### User Role-Dependent Fields
```python
# ✅ CORRECT - User role-dependent computed fields
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    @api.depends_context('user_role', 'show_internal')
    def _compute_display_fields(self):
        for template in self:
            user_role = self.env.context.get('user_role')
            show_internal = self.env.context.get('show_internal', False)
            
            if user_role == 'manager' and show_internal:
                template.display_notes = template.internal_notes
            else:
                template.display_notes = template.public_notes
```

### 4. Performance Optimization

#### Efficient Context Usage
```python
# ✅ CORRECT - Efficient context handling
class ProductTemplate(models.Model):
    _name = 'product.template'
    
    @api.depends_context('lang', 'currency_id')
    def _compute_optimized_info(self):
        # Get context values once
        lang = self.env.context.get('lang')
        currency_id = self.env.context.get('currency_id')
        
        for product in self:
            # Use context values efficiently
            if lang:
                product.localized_name = product.with_context(lang=lang).name
            
            if currency_id:
                product.localized_price = product.with_context(
                    currency_id=currency_id
                ).list_price
```

#### Context Caching
```python
# ✅ CORRECT - Context value caching
class SaleOrder(models.Model):
    _name = 'sale.order'
    
    @api.depends_context('lang', 'currency_id')
    def _compute_cached_values(self):
        # Cache context values to avoid repeated lookups
        context_lang = self.env.context.get('lang')
        context_currency = self.env.context.get('currency_id')
        
        for order in self:
            # Use cached values
            if context_lang:
                order.localized_name = order.with_context(
                    lang=context_lang
                ).name
            
            if context_currency:
                order.localized_amount = order.currency_id.compute(
                    order.amount_total, context_currency
                )
```

### 5. Error Handling and Validation

#### Context Validation
```python
# ✅ CORRECT - Context validation
class ProductTemplate(models.Model):
    _name = 'product.template'
    
    @api.depends_context('lang', 'currency_id')
    def _compute_validated_field(self):
        for product in self:
            try:
                lang = self.env.context.get('lang')
                currency_id = self.env.context.get('currency_id')
                
                # Validate context values
                if lang and lang not in ['en_US', 'es_ES', 'fr_FR']:
                    _logger.warning(f"Unsupported language: {lang}")
                    lang = 'en_US'
                
                if currency_id:
                    currency = self.env['res.currency'].browse(currency_id)
                    if not currency.exists():
                        _logger.warning(f"Invalid currency: {currency_id}")
                        continue
                
                # Compute with validated context
                product.validated_field = self._compute_with_context(lang, currency_id)
                
            except Exception as e:
                _logger.error(f"Error computing field for product {product.id}: {e}")
                product.validated_field = False
```

### 6. Testing and Debugging

#### Context Testing
```python
# ✅ CORRECT - Testing context dependencies
def test_context_dependencies(self):
    """Test context-dependent computed fields"""
    product = self.env['product.template'].create({
        'name': 'Test Product',
        'list_price': 100.0,
    })
    
    # Test with different contexts
    with self.env.context(lang='en_US'):
        product.invalidate_recordset(['localized_name'])
        self.assertEqual(product.localized_name, 'Test Product')
    
    with self.env.context(lang='es_ES'):
        product.invalidate_recordset(['localized_name'])
        # Should reflect Spanish context
        self.assertIsNotNone(product.localized_name)
```

#### Debug Context Values
```python
# ✅ CORRECT - Debugging context values
class ProductTemplate(models.Model):
    _name = 'product.template'
    
    @api.depends_context('lang', 'currency_id')
    def _compute_debug_field(self):
        for product in self:
            # Debug context values
            _logger.debug(f"Context for product {product.id}: {self.env.context}")
            
            lang = self.env.context.get('lang')
            currency_id = self.env.context.get('currency_id')
            
            _logger.debug(f"Lang: {lang}, Currency: {currency_id}")
            
            # Compute field
            product.debug_field = f"Lang: {lang}, Currency: {currency_id}"
```

### 7. Best Practices Summary

#### ✅ DO:
- Use `@api.depends_context` for context-dependent computed fields
- Combine with `@api.depends` when both field and context dependencies exist
- Cache context values for performance
- Validate context values before use
- Handle missing context values gracefully
- Test with different context combinations
- Use for non-stored fields or dynamic computations

#### ❌ DON'T:
- Use for fields that don't depend on context
- Use for stored fields without proper context handling
- Access context values repeatedly in loops
- Ignore context validation
- Mix context and field dependencies incorrectly
- Use without understanding the performance implications

## Examples

### Complete Implementation Example
```python
from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)

class ProductTemplate(models.Model):
    _name = 'product.template'
    _description = 'Product Template with Context Awareness'
    
    # Basic fields
    name = fields.Char("Product Name", required=True)
    list_price = fields.Float("List Price", default=0.0)
    description = fields.Text("Description")
    
    # Computed fields with context dependencies
    localized_name = fields.Char("Localized Name", compute='_compute_localized_name', store=False)
    localized_price = fields.Float("Localized Price", compute='_compute_localized_price', store=False)
    display_info = fields.Text("Display Information", compute='_compute_display_info', store=False)
    
    # Context-dependent computed methods
    @api.depends_context('lang')
    def _compute_localized_name(self):
        """Compute localized name based on language context"""
        for product in self:
            try:
                lang = self.env.context.get('lang')
                if lang:
                    product.localized_name = product.with_context(lang=lang).name
                else:
                    product.localized_name = product.name
            except Exception as e:
                _logger.error(f"Error computing localized name: {e}")
                product.localized_name = product.name
    
    @api.depends_context('currency_id')
    def _compute_localized_price(self):
        """Compute localized price based on currency context"""
        for product in self:
            try:
                currency_id = self.env.context.get('currency_id')
                if currency_id:
                    currency = self.env['res.currency'].browse(currency_id)
                    if currency.exists():
                        product.localized_price = currency.compute(
                            product.list_price, self.env.company.currency_id
                        )
                    else:
                        product.localized_price = product.list_price
                else:
                    product.localized_price = product.list_price
            except Exception as e:
                _logger.error(f"Error computing localized price: {e}")
                product.localized_price = product.list_price
    
    @api.depends('name', 'description')
    @api.depends_context('lang', 'show_details')
    def _compute_display_info(self):
        """Compute display information combining field and context dependencies"""
        for product in self:
            try:
                # Field dependencies
                base_info = f"{product.name}"
                if product.description:
                    base_info += f" - {product.description}"
                
                # Context dependencies
                lang = self.env.context.get('lang')
                show_details = self.env.context.get('show_details', False)
                
                if lang and show_details:
                    # Enhanced display with language context
                    product.display_info = f"[{lang.upper()}] {base_info}"
                else:
                    product.display_info = base_info
                    
            except Exception as e:
                _logger.error(f"Error computing display info: {e}")
                product.display_info = product.name
```

## Testing Guidelines

### Context Testing Scenarios
```python
def test_context_dependencies(self):
    """Test all context-dependent computed fields"""
    product = self.env['product.template'].create({
        'name': 'Test Product',
        'list_price': 100.0,
        'description': 'Test Description',
    })
    
    # Test language context
    with self.env.context(lang='en_US'):
        product.invalidate_recordset(['localized_name', 'display_info'])
        self.assertEqual(product.localized_name, 'Test Product')
        self.assertIn('Test Product', product.display_info)
    
    # Test currency context
    usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
    if usd_currency:
        with self.env.context(currency_id=usd_currency.id):
            product.invalidate_recordset(['localized_price'])
            self.assertIsNotNone(product.localized_price)
    
    # Test combined context
    with self.env.context(lang='es_ES', show_details=True):
        product.invalidate_recordset(['display_info'])
        self.assertIn('[ES_ES]', product.display_info)
```

## Performance Considerations

### Context Optimization
- **Cache context values** to avoid repeated lookups
- **Use efficient context access** patterns
- **Avoid unnecessary context changes** in loops
- **Monitor performance** when using multiple context dependencies

### Memory Management
- **Limit context dependencies** to essential values
- **Clean up context** when no longer needed
- **Use appropriate field storage** (store=False for dynamic context)

## Summary

`@api.depends_context` is a powerful tool for creating context-aware computed fields in Odoo 18. Use it when:

1. **Fields depend on context values** (language, currency, user role, etc.)
2. **Dynamic computation** is needed based on context
3. **Performance optimization** through context-aware caching
4. **Multi-lingual or multi-currency** applications

Remember to:
- ✅ Validate context values before use
- ✅ Cache context values for performance
- ✅ Handle missing context gracefully
- ✅ Test with different context combinations
- ✅ Use appropriate field storage settings
- ✅ Monitor performance impact

This approach ensures maintainable, performant, and context-aware Odoo applications.
description:
globs:
alwaysApply: false
---
