---
alwaysApply: false
---
# Enhanced Odoo Module Analysis and Documentation Cursor Rule
## Analysis Workflow
### 1. Comprehensive Dependency Analysis
- Read `__manifest__.py` to extract all `depends` modules and understand module purpose
- For core Odoo modules (in `/odoo/addons/`):
  - Analyze all Python files (in `models/`, `controllers/`, etc.): model definitions, business logic, API endpoints, inheritance patterns
  - Analyze all JavaScript files (in `static/`): components, event handlers, UI logic, custom widgets, OWL framework usage
  - Analyze all XML files (in `views/`, `security/`, etc.): view definitions, field attributes, actions, inherited views, Odoo 18 syntax
  - Review `__manifest__.py`, `static/`, `controllers/`, `security/` for integration points and data flow
- For custom modules: follow the same analysis pattern as above
- Document each dependency's purpose, integration points, and how it enhances the target module
- Always use `should_read_entire_file=True` when reading files
### 2. Deep Target Module Analysis
- Explore complete directory structure (`models/`, `views/`, `static/`, `controllers/`, `security/`, `data/`, `i18n/`, `wizards/`)
- Analyze all Python files: model definitions, inheritance patterns, business logic, workflows, API endpoints, computed fields
- Analyze all JavaScript files: OWL components, event handlers, API calls, UI state management, custom widgets, frontend-backend communication
- Analyze all XML files: view definitions, field attributes, button actions, inherited views, Odoo 18 structure
- Identify complex business logic, validation rules, and integration patterns
### 3. Advanced Documentation Generation
Create/update these files in `docs/` with comprehensive content:
#### Core Documentation Files:
- `00_OVERVIEW.md`: High-level overview, core concepts, user flow, implementation approach
- `01_DATA_MODELS.md`: Complete database structure, model relationships, field definitions, constraints
- `02_USER_INTERFACE.md`: Frontend interface details, component architecture, user experience flow
- `03_BOM_GENERATION.md`: Business logic for BOM creation, component mapping, quantity calculations
- `04_ADMIN_INTERFACE.md`: Administrative setup, configuration management, template creation
- `05_SALES_INTEGRATION.md`: Sales workflow integration, order processing, configuration handling
- `06_DEVELOPER_GUIDE.md`: Technical architecture, extension points, custom development patterns
- `07_USER_GUIDE.md`: Step-by-step user instructions, screenshots, troubleshooting, best practices
#### Specialized Documentation:
- `08_MATRIX_USE.md`: Specific use cases and scenarios
- `10_WEBSITE_PORTAL.md`: Frontend integration and portal functionality
- `ODOO_18_GUIDELINES.md`: Odoo 18 specific syntax, best practices, migration notes
- `ENHANCED_JSON_IMPORT.md`: Data import/export functionality
- `MANAGE_PRICING_COMPLETE.md`: Pricing matrix and calculation systems
- `DYNAMIC_FIELD_MATCHING.md`: Dynamic field behavior and conditional logic
- `CALCULATED_FIELDS.md`: Computed field logic and formulas
- `BOM_GENERATION.md`: Detailed BOM creation process
- `SVG_COMPONENT_GUIDE.md`: Visual component rendering and SVG handling
#### Technical Documentation:
- `PYTHON_DICTIONARY.md`: Python code patterns and data structures
- `CODEBASE_ANALYSIS_CLEANUP_PLAN.md`: Code quality and optimization recommendations
- `DYNAMIC_ERROR_MESSAGES_EXAMPLES.md`: Error handling and user feedback
- `LOCK_VISIBILITY_PROJECT_NOTES.md`: UI state management and visibility controls
### 4. Enhanced Quality Assurance
- Verify documentation completeness and accuracy with real-world testing scenarios
- Ensure Python files follow PEP8 standards and Odoo conventions
- Verify proper module integration with dependencies and core Odoo functionality
- Check security compliance, performance considerations, and scalability
- Validate Odoo 18 syntax compliance and modern development patterns
- Include comprehensive code examples and troubleshooting guides
## Implementation Guidelines
### Documentation Structure Best Practices:
- Use clear, descriptive headings and subheadings
- Include code examples with proper syntax highlighting
- Provide step-by-step instructions with expected outcomes
- Include troubleshooting sections for common issues
- Use screenshots and diagrams where helpful
- Maintain consistent formatting and clear, concise language
- Include both user-facing and developer-facing content
### Analysis Techniques:
- Use `read_file` for detailed file examination
- Use `codebase_search` for semantic understanding and pattern recognition
- Use `grep_search` for finding specific patterns and references
- Use `list_dir` for understanding module structure
- Focus on inheritance patterns, business logic flows, and integration points
- Identify complex workflows and document them thoroughly
### Content Quality Standards:
- Include practical examples and use cases
- Document both simple and complex scenarios
- Provide clear explanations of technical concepts
- Include performance considerations and best practices
- Document extension points and customization options
- Include migration guides for version updates
## Success Criteria
- All dependencies thoroughly understood and documented with integration details
- Complete technical implementation analyzed with architectural patterns
- Comprehensive user testing procedures with real-world scenarios
- Seamless AI handoff documentation with complete technical context
- Consistent documentation standards across all files
- All integration points, business logic, and user workflows documented
- Odoo 18 compliance and modern development patterns included
- Performance considerations and scalability factors addressed
## Example Application for Complex Modules
### 1. Comprehensive Dependency Analysis
```bash
# Read manifest to understand module purpose and dependencies
read_file target_file="module_name/__manifest__.py" should_read_entire_file=true
# Analyze core Odoo modules - Python files with inheritance patterns
read_file target_file="odoo/addons/web/__manifest__.py" start_line_one_indexed=1 end_line_one_indexed=50
codebase_search query="web core JavaScript components OWL framework"
read_file target_file="odoo/addons/sale_management/models/sale_order.py" start_line_one_indexed=1 end_line_one_indexed=100
read_file target_file="odoo/addons/mrp/models/mrp_bom.py" start_line_one_indexed=1 end_line_one_indexed=100
# Analyze JavaScript architecture and components
codebase_search query="OWL components event handlers UI state management"
codebase_search query="frontend backend communication API calls"
codebase_search query="custom widgets JavaScript patterns"
# Analyze XML views and Odoo 18 syntax
read_file target_file="odoo/addons/sale_management/views/sale_views.xml" start_line_one_indexed=1 end_line_one_indexed=100
codebase_search query="Odoo 18 view syntax list form invisible attributes"
```
### 2. Deep Module Analysis
```bash
# Explore complete structure including subdirectories
list_dir relative_workspace_path="module_name"
list_dir relative_workspace_path="module_name/models"
list_dir relative_workspace_path="module_name/static/src/js"
list_dir relative_workspace_path="module_name/views"
list_dir relative_workspace_path="module_name/wizards"
# Analyze Python models with business logic
read_file target_file="module_name/models/main_model.py" should_read_entire_file=true
codebase_search query="business logic workflows computed fields"
codebase_search query="inheritance patterns model relationships"
codebase_search query="validation rules constraints"
# Analyze JavaScript components and UI logic
read_file target_file="module_name/static/src/js/main_component.js" should_read_entire_file=true
codebase_search query="OWL component setup event handling"
codebase_search query="UI state management user interactions"
codebase_search query="custom widgets visual components"
# Analyze XML views and user interface
read_file target_file="module_name/views/main_views.xml" should_read_entire_file=true
codebase_search query="view inheritance field attributes"
codebase_search query="button actions workflow integration"
codebase_search query="Odoo 18 syntax modern patterns"
```
### 3. Comprehensive Documentation Output
- **Overview**: Complete module purpose, core concepts, user flow, technical architecture
- **Data Models**: Detailed database structure, relationships, constraints, computed fields
- **User Interface**: Frontend architecture, component design, user experience patterns
- **Business Logic**: Complex workflows, validation rules, integration points
- **Developer Guide**: Technical architecture, extension points, customization patterns
- **User Guide**: Step-by-step instructions, screenshots, troubleshooting, best practices
- **Specialized Guides**: Use case specific documentation, technical deep-dives
- **README**: Quick start, installation, configuration, usage examples
## Key Insights for Advanced Odoo Modules
- Always analyze inheritance patterns and model relationships comprehensively
- Understand frontend-backend integration points and communication patterns
- Document security, validation workflows, and user experience considerations
- Include performance considerations, scalability factors, and optimization strategies
- Consider Odoo 18 specific syntax and modern development patterns
- Document complex business logic and workflow automation
- Include both user-facing and developer-facing documentation
- Provide comprehensive troubleshooting and extension guidance
- Consider visual components, SVG handling, and advanced UI patterns
- Document data import/export functionality and integration capabilities
- Understand frontend-backend integration points
- Document security and validation workflows
- Include user experience considerations
- Consider performance and scalability implications
- Include user experience considerations
- Consider performance and scalability implications
