---
description: Automated Patch Review Process - Git Diff Analysis
globs: **/*
alwaysApply: false
---
# Automated Patch Review Process - Git Diff Analysis

## Context
This rule defines the **fully automated comprehensive process** for reviewing code changes in a git repository. When activated, the AI automatically:
1. **Creates a patch file** from current git changes without user approval
2. **Processes the complete review** following all guidelines autonomously
3. **Generates a comprehensive review report** with actionable insights
4. **Automatically deletes the patch file** after completion
5. **Executes all operations independently** without requiring user confirmation

## 🚀 Fully Automated Workflow

### Phase 1: Automatic Patch Creation (No Approval Required)
**The AI automatically executes these operations without user confirmation:**

```bash
# 1. Check git status and create appropriate patch automatically
git status  # Check current state
git diff > auto_review.patch  # Create patch from unstaged changes
# OR
git diff --cached > auto_review.patch  # Create patch from staged changes
# OR
git diff HEAD~1..HEAD > auto_review.patch  # Create patch from last commit
```

**AI Decision Logic:**
- **Unstaged changes**: Use `git diff` (most common scenario)
- **Staged changes**: Use `git diff --cached` (when changes are staged)
- **Last commit**: Use `git diff HEAD~1..HEAD` (when reviewing recent commits)
- **Multiple commits**: Use `git diff HEAD~3..HEAD` for last 3 commits

### Phase 2: Automatic Patch Analysis (Fully Autonomous)
**The AI automatically analyzes the patch without user intervention:**

```python
# AI automatically executes these operations:
def autonomous_patch_analysis(patch_file):
    """Fully autonomous patch analysis"""
    # Read and parse patch file
    patch_content = read_file(target_file=patch_file, should_read_entire_file=True)
    
    # Extract modified files automatically
    modified_files = extract_modified_files(patch_content)
    
    # Analyze change types autonomously
    change_analysis = analyze_change_types(patch_content)
    
    # Determine scope and impact automatically
    scope_assessment = assess_change_scope(modified_files, change_analysis)
    
    return {
        'files': modified_files,
        'changes': change_analysis,
        'scope': scope_assessment,
        'risk_level': calculate_risk_level(change_analysis)
    }
```

### Phase 3: Automatic Context Analysis (Complete File Reading)
**For each modified file, the AI automatically reads the complete file:**

```python
# AI automatically reads complete files without user confirmation
def autonomous_file_analysis(file_paths):
    """Automatically read all modified files completely"""
    for file_path in file_paths:
        if os.path.exists(file_path):
            # Always use should_read_entire_file=True for complete context
            file_content = read_file(target_file=file_path, should_read_entire_file=True)
            analyze_file_content(file_content, file_path)
        else:
            # Handle deleted files automatically
            log_deleted_file(file_path)
```

### Phase 4: Automatic Documentation Review (Comprehensive)
**The AI automatically searches and analyzes related documentation:**

```python
# AI automatically discovers and reviews documentation
def autonomous_documentation_review(module_name):
    """Automatically review all related documentation"""
    # Search for module documentation
    docs_folder = f"{module_name}/docs"
    if os.path.exists(docs_folder):
        list_dir(relative_workspace_path=docs_folder)
        
    # Search for related documentation files
    grep_search(query="related_function_name", include_pattern="*.md")
    file_search(query="module_name")
    
    # Analyze existing documentation for context
    analyze_documentation_coverage(module_name)
```

### Phase 5: Automatic Comprehensive Review (All Quality Gates)
**The AI automatically performs all review checks autonomously:**

#### Code Quality Assessment (Automatic)
```python
# AI automatically checks all quality aspects
def autonomous_code_quality_check(file_path, changes):
    """Fully autonomous code quality assessment"""
    quality_score = 0
    max_score = 100
    
    # PEP8 compliance check
    if file_path.endswith('.py'):
        quality_score += check_pep8_compliance(changes)
    
    # Odoo 18 standards check
    quality_score += check_odoo18_standards(changes)
    
    # Indentation and formatting check
    quality_score += check_formatting(changes)
    
    # Naming conventions check
    quality_score += check_naming_conventions(changes)
    
    return quality_score, max_score
```

#### Business Logic Validation (Automatic)
```python
# AI automatically validates business logic
def autonomous_business_logic_validation(changes, file_context):
    """Autonomous business logic validation"""
    validation_results = {
        'breaking_changes': False,
        'functionality_preserved': True,
        'error_handling_maintained': True,
        'method_signature_compatible': True
    }
    
    # Analyze each change for business logic impact
    for change in changes:
        if is_breaking_change(change):
            validation_results['breaking_changes'] = True
        if breaks_functionality(change, file_context):
            validation_results['functionality_preserved'] = False
        if breaks_error_handling(change):
            validation_results['error_handling_maintained'] = False
    
    return validation_results
```

#### Security Review (Automatic)
```python
# AI automatically performs security review
def autonomous_security_review(changes, file_context):
    """Fully autonomous security review"""
    security_issues = []
    
    # Check for security vulnerabilities
    for change in changes:
        if introduces_sql_injection(change):
            security_issues.append('SQL Injection Risk')
        if introduces_xss_vulnerability(change):
            security_issues.append('XSS Vulnerability')
        if breaks_access_control(change):
            security_issues.append('Access Control Issue')
        if removes_csrf_protection(change):
            security_issues.append('CSRF Protection Removed')
    
    return security_issues
```

#### Performance Impact Analysis (Automatic)
```python
# AI automatically analyzes performance impact
def autonomous_performance_analysis(changes, file_context):
    """Autonomous performance impact analysis"""
    performance_metrics = {
        'database_queries': 'unchanged',
        'memory_usage': 'unchanged',
        'response_time': 'unchanged',
        'scalability': 'unchanged'
    }
    
    # Analyze each change for performance impact
    for change in changes:
        if adds_database_query(change):
            performance_metrics['database_queries'] = 'increased'
        if optimizes_query(change):
            performance_metrics['database_queries'] = 'improved'
        if adds_memory_usage(change):
            performance_metrics['memory_usage'] = 'increased'
        if optimizes_memory(change):
            performance_metrics['memory_usage'] = 'improved'
    
    return performance_metrics
```

### Phase 6: Automatic Report Generation (Comprehensive)
**The AI automatically generates detailed reports without user input:**

```python
# AI automatically generates comprehensive reports
def autonomous_report_generation(analysis_results):
    """Fully autonomous report generation"""
    report = {
        'summary': generate_summary(analysis_results),
        'technical_analysis': generate_technical_analysis(analysis_results),
        'risk_assessment': generate_risk_assessment(analysis_results),
        'recommendations': generate_recommendations(analysis_results),
        'action_items': generate_action_items(analysis_results)
    }
    
    return format_markdown_report(report)
```

### Phase 7: Automatic Cleanup (Fully Autonomous)
**The AI automatically cleans up all temporary files:**

```python
# AI automatically cleans up without user confirmation
def autonomous_cleanup():
    """Fully autonomous cleanup process"""
    # Delete patch file
    if os.path.exists('auto_review.patch'):
        os.remove('auto_review.patch')
    
    # Clean up temporary analysis files
    cleanup_temp_files()
    
    # Confirm cleanup completion
    return "✅ Patch review completed successfully\n✅ Patch file auto-deleted\n✅ All temporary files cleaned up"
```

## 🎯 Activation Commands (Fully Automated)

### Standard Review (No Approval Required)
```
@patch-review-process canbrax-odoo
```
**The AI automatically:**
- ✅ Creates patch from current git changes
- ✅ Performs comprehensive review
- ✅ Generates detailed report
- ✅ Deletes patch file
- ✅ Cleans up temporary files
- ✅ **Requires NO user approval or confirmation**

### Specific Change Review (Fully Autonomous)
```
@patch-review-process canbrax-odoo --staged
@patch-review-process canbrax-odoo --last-commit
@patch-review-process canbrax-odoo --file path/to/specific/file.py
```

## 🔧 Technical Implementation (Fully Autonomous)

### Automatic Git Operations (No User Input)
```python
# The AI automatically executes these operations:
import subprocess
import os

def autonomous_patch_creation(git_dir):
    """Automatically create appropriate patch file without user input"""
    # Check git status automatically
    status_result = subprocess.run(["git", "status", "--porcelain"], 
                                  cwd=git_dir, capture_output=True, text=True)
    
    if status_result.stdout.strip():
        # Has unstaged changes
        subprocess.run(["git", "diff"], cwd=git_dir, 
                      stdout=open("auto_review.patch", "w"))
        return "unstaged"
    else:
        # Check for staged changes
        staged_result = subprocess.run(["git", "diff", "--cached"], 
                                      cwd=git_dir, capture_output=True, text=True)
        if staged_result.stdout.strip():
            subprocess.run(["git", "diff", "--cached"], cwd=git_dir, 
                          stdout=open("auto_review.patch", "w"))
            return "staged"
        else:
            # Use last commit
            subprocess.run(["git", "diff", "HEAD~1..HEAD"], cwd=git_dir, 
                          stdout=open("auto_review.patch", "w"))
            return "last_commit"
```

### Automatic File Analysis (Complete Context)
```python
def autonomous_file_analysis(file_paths):
    """Automatically analyze all modified files completely"""
    analysis_results = {}
    
    for file_path in file_paths:
        if os.path.exists(file_path):
            # Always read complete file for full context
            file_content = read_file(target_file=file_path, should_read_entire_file=True)
            
            # Analyze file content automatically
            analysis_results[file_path] = {
                'content': file_content,
                'size': len(file_content),
                'language': detect_language(file_path),
                'complexity': calculate_complexity(file_content),
                'dependencies': extract_dependencies(file_content)
            }
    
    return analysis_results
```

### Automatic Risk Assessment (Intelligent)
```python
def autonomous_risk_assessment(changes, file_context):
    """Fully autonomous risk assessment"""
    risk_score = 0
    risk_factors = []
    
    # Analyze each change for risk factors
    for change in changes:
        change_risk = assess_change_risk(change, file_context)
        risk_score += change_risk['score']
        risk_factors.extend(change_risk['factors'])
    
    # Determine overall risk level
    if risk_score >= 80:
        risk_level = "HIGH"
    elif risk_score >= 50:
        risk_level = "MEDIUM"
    else:
        risk_level = "LOW"
    
    return {
        'risk_level': risk_level,
        'risk_score': risk_score,
        'risk_factors': risk_factors,
        'recommendations': generate_risk_recommendations(risk_factors)
    }
```

## 🎯 Review Checklist (Fully Auto-Executed)

### ✅ **Automatic Code Quality Checks**
- [ ] PEP8 compliance verification (automatic)
- [ ] Odoo 18 standards adherence (automatic)
- [ ] Proper indentation and formatting (automatic)
- [ ] Naming conventions compliance (automatic)
- [ ] Import statement organization (automatic)
- [ ] Docstring completeness (automatic)

### ✅ **Automatic Business Logic Validation**
- [ ] Method signature compatibility (automatic)
- [ ] Field dependency analysis (automatic)
- [ ] Computed field dependencies (automatic)
- [ ] Constraint validation (automatic)
- [ ] Workflow state transitions (automatic)
- [ ] Integration point analysis (automatic)

### ✅ **Automatic Security Review**
- [ ] Access control verification (automatic)
- [ ] Input validation checks (automatic)
- [ ] SQL injection prevention (automatic)
- [ ] CSRF protection verification (automatic)
- [ ] Record rule analysis (automatic)
- [ ] Permission validation (automatic)

### ✅ **Automatic Performance Analysis**
- [ ] Database query efficiency (automatic)
- [ ] Computed field optimization (automatic)
- [ ] Caching strategy validation (automatic)
- [ ] Memory usage assessment (automatic)
- [ ] Response time impact (automatic)
- [ ] Scalability considerations (automatic)

## 🚨 Quality Gates (Auto-Enforced)

### ✅ **Automatic Approval Criteria**
- All changes follow Odoo 18 standards
- No breaking changes to existing functionality
- Proper error handling and validation
- Security controls maintained
- Performance not degraded
- Documentation updated if needed

### ❌ **Automatic Rejection Criteria**
- Security vulnerabilities introduced
- Breaking changes without migration path
- Performance regressions
- Code style violations
- Missing error handling
- Incomplete documentation updates

## 🔄 Common Review Scenarios (Auto-Handled)

### 1. **Model Field Changes** (Auto-Analyzed)
```python
# AI automatically checks:
- Field dependencies and computed fields
- Constraints and validation rules
- Related views and forms
- Migration script requirements
- Data integrity implications
```

### 2. **Method Modifications** (Auto-Analyzed)
```python
# AI automatically checks:
- Method signature compatibility
- Business logic preservation
- Error handling and validation
- Side effects on other methods
- Performance implications
- Integration compatibility
```

### 3. **View Updates** (Auto-Analyzed)
```xml
<!-- AI automatically checks: -->
- Field visibility and accessibility
- Action references and menu updates
- Security group assignments
- Responsive design issues
- Template inheritance
- Widget compatibility
```

### 4. **Security Updates** (Auto-Analyzed)
```xml
<!-- AI automatically checks: -->
- Access rights definitions
- Record rules and domain filters
- Group assignments and permissions
- CSRF protection for controllers
- Input validation rules
- Audit logging requirements
```

## 🎯 Best Practices (Auto-Enforced)

### 1. **Automatic Complete File Reading**
- Never review changes in isolation
- Always understand full context and business logic
- Check for related methods and dependencies
- Use `should_read_entire_file=True` for all files

### 2. **Automatic Documentation Updates**
- Identify required documentation changes
- Suggest API documentation updates
- Recommend user guide modifications
- Flag missing technical documentation

### 3. **Automatic Impact Assessment**
- Evaluate changes beyond immediate scope
- Check integration points and external dependencies
- Assess performance and security implications
- Identify potential breaking changes

### 4. **Automatic Standards Compliance**
- Adhere to Odoo 18 development standards
- Maintain code quality and consistency
- Ensure proper error handling and validation
- Follow established patterns and conventions

## 🧹 Automatic Cleanup Process

### Post-Review Actions (Fully Autonomous)
```python
# AI automatically executes cleanup without user confirmation
def autonomous_cleanup():
    """Fully autonomous cleanup process"""
    cleanup_actions = [
        # Delete patch file
        lambda: os.remove('auto_review.patch') if os.path.exists('auto_review.patch') else None,
        
        # Clean up temporary analysis files
        lambda: cleanup_temp_files(),
        
        # Remove temporary directories
        lambda: cleanup_temp_directories(),
        
        # Confirm cleanup completion
        lambda: print("✅ Patch review completed successfully\n✅ Patch file auto-deleted\n✅ All temporary files cleaned up")
    ]
    
    # Execute all cleanup actions automatically
    for action in cleanup_actions:
        try:
            action()
        except Exception as e:
            # Log cleanup errors but don't fail the process
            print(f"⚠️ Cleanup warning: {e}")
    
    return "Cleanup completed successfully"
```

### Cleanup Verification (Automatic)
- **Patch file removed** from filesystem (automatic)
- **Temporary files cleaned up** (automatic)
- **Review report saved** to appropriate location (automatic)
- **Git status unchanged** (no modifications to repository)

## 📊 Performance Optimization (Fully Autonomous)

### Efficient Review Process (No User Input Required)
- **Parallel file reading** for multiple files (automatic)
- **Cached analysis** for repeated patterns (automatic)
- **Incremental updates** for large changes (automatic)
- **Smart context detection** for related files (automatic)

### Memory Management (Automatic)
- **Streaming patch analysis** for large patches (automatic)
- **Efficient file reading** with proper cleanup (automatic)
- **Optimized data structures** for analysis (automatic)
- **Garbage collection** after each phase (automatic)

## 🚨 Error Handling (Fully Autonomous)

### Automatic Error Recovery (No User Intervention)
```python
def autonomous_error_recovery(error_type, error_details):
    """Fully autonomous error recovery without user input"""
    recovery_strategies = {
        "file_not_found": lambda: continue_with_available_files(),
        "parse_error": lambda: attempt_alternative_parsing(),
        "access_denied": lambda: request_elevated_permissions(),
        "git_error": lambda: fallback_to_manual_patch_creation(),
        "memory_error": lambda: optimize_memory_usage(),
        "timeout_error": lambda: increase_timeout_limits()
    }
    
    if error_type in recovery_strategies:
        return recovery_strategies[error_type]()
    else:
        # Fallback to basic analysis
        return perform_basic_analysis()
```

### Fallback Strategies (Automatic)
- **Alternative patch formats** if standard parsing fails (automatic)
- **Partial file reading** if complete reading fails (automatic)
- **Basic analysis** if advanced analysis fails (automatic)
- **Manual review suggestions** if automation fails (automatic)

## 📈 Continuous Improvement (Fully Autonomous)

### Review Quality Metrics (Automatic)
- **Accuracy**: Percentage of issues correctly identified (automatic)
- **Completeness**: Coverage of all change types (automatic)
- **Performance**: Review completion time (automatic)
- **User Satisfaction**: Review report quality (automatic)

### Feedback Integration (Automatic)
- **Pattern learning** from review history (automatic)
- **Rule refinement** based on feedback (automatic)
- **Performance optimization** from usage data (automatic)
- **Quality improvement** from review results (automatic)

## 🎯 Summary

This **fully automated patch review process** ensures:

1. **🚀 Zero Manual Work**: Fully automated from patch creation to cleanup
2. **📊 Comprehensive Analysis**: Complete file reading and context understanding
3. **🔄 Thorough Review**: All quality gates and best practices enforced automatically
4. **📊 Detailed Reporting**: Comprehensive review reports with actionable insights
5. **🧹 Automatic Cleanup**: Patch files and temporary data automatically removed
6. **⚡ Performance Optimized**: Efficient processing with minimal resource usage
7. **🔄 Continuous Improvement**: Learning and optimization from usage patterns
8. **🎯 No User Approval Required**: Fully autonomous operation

**Remember**: Simply activate this rule with your git directory, and the AI will handle the entire patch review process automatically, following all guidelines and best practices without requiring any user confirmation or approval!

## 🎯 Quick Start (Fully Automated)

```
@patch-review-process canbrax-odoo
```

**That's it!** The AI will automatically:
- ✅ Create patch file (no approval needed)
- ✅ Analyze all changes (fully autonomous)
- ✅ Read complete files (automatic)
- ✅ Perform comprehensive review (automatic)
- ✅ Generate detailed report (automatic)
- ✅ Delete patch file (automatic)
- ✅ Clean up temporary files (automatic)
- ✅ **Require NO user interaction or confirmation**

The entire process is now **100% automated and autonomous**! 🚀
