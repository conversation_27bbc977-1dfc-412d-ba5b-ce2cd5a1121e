---
description: Convert business requirements to pseudo technical requirements by mapping concepts to technical objects
alwaysApply: false
---
# Requirements to Pseudo Technical Requirements Converter

## Purpose
This cursor rule helps convert business requirements into pseudo technical requirements by mapping business concepts to technical objects in the Odoo codebase. This ensures AI models have a clear understanding of requirements in the technical context.

## Workflow

### Step 1: Extract Concepts and Request Mapping
After receiving requirements, extract all concepts and ask the user to map them to technical objects:

**Extract these concepts:**
- **Actions**: Verbs like "add", "modify", "delete", "view"
- **Entities**: Business objects like "Charge", "Sale Order", "Product"
- **Attributes**: Properties like "sale price", "floor cost", "category"
- **Users/Roles**: "customer", "user", "admin"
- **Relationships**: "belongs to", "managed by", "not visible to"

**Request mapping for each concept:**
```
Please map each concept to its technical equivalent:
* <action> "add" - [method/button/field]
* "Charge" - [model name]
* "Sale Order" - [model name]
* "sale price" - [field name on model]
* "user" - [model/field reference]
* "Floor Cost" - [field name on model]
* "Category" - [model name]
* "customer" - [field/model reference]
```

### Step 2: Check Existing Documentation
Search for existing concept relationship documentation:

```python
# Check docs folder
docs_folder = "docs"
docs_extend_folder = "docs_extend"

# Look for CONCEPTS_RELATIONSHIP.md
concept_file = "CONCEPTS_RELATIONSHIP.md"

# If exists, use existing mappings and confirm with user
if file_exists(f"{docs_folder}/{concept_file}"):
    existing_mappings = read_file(f"{docs_folder}/{concept_file}")
    print("Found existing concept mappings. Please confirm:")
    print(existing_mappings)
    user_confirmation = input("Are these mappings correct? (y/n): ")
```

### Step 3: Convert to Pseudo Technical Requirements
Based on user mappings, convert requirements to pseudo technical format:

**Input Example:**
```
- Ability to add Charges to Sales Order
- Charge to have $0 sale price
- Charge to allow user to modify Floor Cost
- Charge products managed by Category
- Not to be seen by customer (Web view, PDF)
- Negative Floor Cost not allowed
```

**User Mappings:**
```
* <action> "add" - create button or on `write` method
* "Charge" - models `sale.order.line`
* "Sale Order" - models `sale.order`
* "sale price" - field `list_price` on model `product.product`
* "user" - model `res.user`
* "Floor Cost" - field `floor_cost` on model `product.product`
* "Category" - model `product.category`
* "customer" - field `partner_id` on model `sale.order`
```

**Output - Pseudo Technical Requirements:**
```
- Ability to add Charges (model `sale.order.line`) to Sales Order (model `sale.order`)
- Charge to have $0 sale price (field `list_price` on model `product.product`)
- Charge to allow user (self.env.user) to modify Floor Cost (field `floor_cost` on model `product.product`)
- Charge products (field `product_id` on model `sale.order.line`) managed by Category (model `product.category`)
- Not to be seen by customer (field `partner_id` on model `sale.order`) (Web view, PDF)
- Negative Floor Cost (field `floor_cost` on model `product.product`) not allowed
```

### Step 4: Create/Update CONCEPTS_RELATIONSHIP.md
Store the concept mappings in the appropriate documentation folder:

```markdown
# Concept Relationships

## Business Concepts to Technical Objects Mapping

### Actions
- **add**: create button or `write` method
- **modify**: `write` method or field update
- **delete**: `unlink` method
- **view**: form/tree/list view or `read` method

### Business Entities
- **Charge**: model `sale.order.line`
- **Sale Order**: model `sale.order`
- **Product**: model `product.product`
- **Category**: model `product.category`
- **User**: model `res.user`

### Fields and Attributes
- **sale price**: field `list_price` on model `product.product`
- **floor cost**: field `floor_cost` on model `product.product`
- **customer**: field `partner_id` on model `sale.order`
- **product**: field `product_id` on model `sale.order.line`

### Relationships
- **managed by**: Many2one relationship field
- **belongs to**: Many2one relationship field
- **not visible to**: domain filter or access rights
- **web view**: website controller or public views
- **PDF**: report template or QWeb rendering
```

## Implementation Guidelines

### 1. Always Extract All Concepts
- Don't skip any business terms
- Include actions, entities, attributes, and relationships
- Ask for clarification on ambiguous terms

### 2. Request Specific Technical Mappings
- Ask for exact model names (`sale.order`, not just "sale")
- Request field names with model context
- Get method names for actions
- Clarify view references

### 3. Validate Technical Objects
- Confirm model names exist in codebase
- Verify field names on specified models
- Check method availability
- Validate view references

### 4. Create Comprehensive Documentation
- Store mappings in `docs` or `docs_extend` folder
- Use consistent naming conventions
- Include examples and usage notes
- Update when new concepts are added

### 5. Confirm with User
- Always show existing mappings if found
- Request confirmation of accuracy
- Allow updates and corrections
- Maintain mapping history

## Benefits

1. **Clear Technical Context**: AI models understand exact technical objects
2. **Consistent Mapping**: Standardized concept-to-object relationships
3. **Documentation**: Maintained concept relationship documentation
4. **Reduced Ambiguity**: Clear technical requirements for development
5. **Reusability**: Mappings can be reused across similar requirements

## Usage Examples

### Example 1: Simple Field Addition
**Requirement**: "Add discount field to sale order lines"
**Concept Extraction**: 
- Action: "Add" - `create` method or field definition
- Entity: "discount field" - new field on model
- Target: "sale order lines" - model `sale.order.line`

**Pseudo Technical**: "Add discount field (new field `discount` on model `sale.order.line`)"

### Example 2: Complex Business Rule
**Requirement**: "Only managers can approve orders above $1000"
**Concept Extraction**:
- Action: "approve" - method or state change
- Condition: "above $1000" - field comparison
- Restriction: "only managers" - user group or role
- Target: "orders" - model reference

**Pseudo Technical**: "Only managers (group `sales_team.group_sale_manager`) can approve orders (model `sale.order`) above $1000 (field `amount_total` comparison)"

## Error Prevention

### Common Mistakes to Avoid
1. **Vague Mappings**: "sale" instead of `sale.order`
2. **Missing Context**: field name without model
3. **Incorrect References**: wrong model or field names
4. **Incomplete Mappings**: missing concepts or relationships
5. **Outdated Documentation**: not updating existing mappings

### Validation Checklist
- [ ] All concepts extracted and mapped
- [ ] Technical object names verified
- [ ] Model and field references accurate
- [ ] Documentation updated
- [ ] User confirmed mappings
- [ ] Requirements converted to pseudo technical format
```

This cursor rule provides a comprehensive framework for converting business requirements to pseudo technical requirements. It ensures that:

1. **All concepts are properly extracted** from requirements
2. **Technical mappings are requested** for each concept
3. **Existing documentation is checked** and reused when possible
4. **Requirements are converted** to clear technical format
5. **Concept relationships are documented** for future use
6. **AI models have clear technical context** for understanding requirements

The rule follows the established cursor rules format and integrates with the existing documentation structure in your project.

description:
globs:
alwaysApply: true
---
