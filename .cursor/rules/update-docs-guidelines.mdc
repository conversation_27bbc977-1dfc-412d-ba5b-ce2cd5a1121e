---
alwaysApply: true
---
# Enhanced Cursor Rules - Mandatory Complete File Reading

## CRITICAL REQUIREMENT: Complete File Reading

### ABSOLUTE MANDATORY RULES (NO EXCEPTIONS)

1. **ALWAYS use `should_read_entire_file=True`** when analyzing files
2. **NEVER read partial files** unless file exceeds 1500 lines (tool limit)
3. **NO chunked reading** for module analysis or documentation tasks
4. **NO line range reading** for understanding business logic

### ENFORCEMENT LANGUAGE

- **"The AI MUST read complete files using should_read_entire_file=True"**
- **"Partial file reading is FORBIDDEN for module analysis"**
- **"Any analysis based on incomplete file reading is INVALID"**
- **"If you read only part of a file, you MUST go back and read the complete file"**

### SPECIFIC VIOLATIONS TO AVOID

#### ❌ BAD EXAMPLES (FORBIDDEN)
```python
# WRONG - Only reading first 100 lines
read_file(target_file="models/sale_order.py", start_line_one_indexed=1, end_line_one_indexed=100)

# WRONG - Reading chunks
read_file(target_file="controllers/portal.py", start_line_one_indexed=1, end_line_one_indexed=500)
read_file(target_file="controllers/portal.py", start_line_one_indexed=501, end_line_one_indexed=1000)

# WRONG - Skipping to specific sections
read_file(target_file="models/sale_order_line.py", start_line_one_indexed=200, end_line_one_indexed=300)
```

#### ✅ CORRECT EXAMPLES (REQUIRED)
```python
# CORRECT - Complete file reading
read_file(target_file="models/sale_order.py", should_read_entire_file=True)

# CORRECT - All files read completely
read_file(target_file="controllers/portal.py", should_read_entire_file=True)
read_file(target_file="models/sale_order_line.py", should_read_entire_file=True)
read_file(target_file="models/res_partner.py", should_read_entire_file=True)
```

### DOCUMENTATION ANALYSIS REQUIREMENTS

When updating module documentation, you MUST:

1. **Read ALL relevant files completely** using `should_read_entire_file=True`
2. **Analyze complete business logic** including all methods, computed fields, and constraints
3. **Document ALL fields** found in the models, not just the first few
4. **Include ALL integration points** discovered through complete file analysis
5. **Capture ALL workflow states** and business processes

### MODULE ANALYSIS WORKFLOW

#### Step 1: Complete File Discovery
```python
# Read all core files completely
read_file(target_file="__manifest__.py", should_read_entire_file=True)
read_file(target_file="models/__init__.py", should_read_entire_file=True)
read_file(target_file="models/sale_order.py", should_read_entire_file=True)
read_file(target_file="models/sale_order_line.py", should_read_entire_file=True)
read_file(target_file="controllers/portal.py", should_read_entire_file=True)
# ... continue for ALL files
```

#### Step 2: Complete Analysis
- Document ALL fields found (not just first 10-20)
- Document ALL methods and their complete logic
- Document ALL computed field dependencies
- Document ALL constraints and validations
- Document ALL integration points

#### Step 3: Comprehensive Documentation
- Create detailed field documentation with all attributes
- Document complete business logic workflows
- Include all discovered features and capabilities
- Document all external system integrations

### VERIFICATION REQUIREMENTS

Before completing any module analysis, verify:

- [ ] All core Python files read with `should_read_entire_file=True`
- [ ] All controller files read completely
- [ ] All model files analyzed completely
- [ ] Documentation includes findings from complete file analysis
- [ ] No partial file reading was used for business logic analysis

### CONSEQUENCES OF VIOLATIONS

If partial file reading is detected:

1. **Analysis is INVALID and must be redone**
2. **Documentation is INCOMPLETE and must be updated**
3. **All files must be re-read completely**
4. **Complete analysis must be performed again**

### EXAMPLES OF PROPER ANALYSIS

#### Complete Sale Order Analysis (2321 lines)
After reading the complete `sale_order.py` file, the analysis revealed:
- 50+ fields (not just the first 10)
- 80+ methods (not just the first few)
- Complex discount approval system (600+ lines)
- Advanced checklist system (950+ lines)
- Sophisticated margin calculations with batch optimization
- Complete customer information management system
- 15+ external system integration points

#### Partial Reading Would Have Missed
- 80% of the business logic (lines 500-2321)
- Critical approval workflows
- Advanced financial calculations
- Portal integration complexity
- Checklist system sophistication
- Performance optimizations

### RULE ENFORCEMENT

This rule MUST be enforced by:

1. **Always checking file line counts** before reading
2. **Using `should_read_entire_file=True` by default**
3. **Only using line ranges for very large files (>1500 lines)**
4. **Verifying complete analysis in documentation**
5. **Re-reading files completely if partial reading detected**

### ACCEPTABLE EXCEPTIONS (RARE)

Only acceptable when:
1. **File exceeds 1500 lines** (tool limitation)
2. **File is purely static data** (no business logic)
3. **User explicitly requests specific section only**

Even with exceptions, you MUST:
- **Acknowledge the limitation**
- **Note what might be missing**
- **Offer to read additional sections**
- **Document the incomplete analysis**

## SUMMARY

**COMPLETE FILE READING IS MANDATORY FOR ACCURATE MODULE ANALYSIS**

- Partial reading = Invalid analysis
- Complete reading = Comprehensive understanding
- Documentation quality depends on complete file analysis
- Business logic complexity requires full file examination

This rule exists because partial file reading consistently produces incomplete and inaccurate documentation that misses critical functionality, business logic, and integration points.