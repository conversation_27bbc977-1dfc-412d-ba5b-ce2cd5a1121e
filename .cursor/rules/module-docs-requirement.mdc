---
description: Require reading all documents in module's docs folder for AI to understand the purpose, architecture, and technical structure of module
globs: 
alwaysApply: false
---
# Module Documentation Requirement Rule

## Overview
Before making any changes, providing assistance, or answering questions about an Odoo module, the AI MUST read and understand all documentation in the module's `docs` folder to ensure proper context and alignment with the module's purpose, architecture, and technical structure.

## When This Rule Applies
This rule applies when:
- Working with any Odoo module that has a `docs` folder
- Making changes to module functionality
- Providing code suggestions or implementations
- Answering questions about module behavior
- Debugging module issues
- Extending module functionality

## Required Actions

### 1. Locate Module Documentation
Always check for a `docs` folder in the module directory:
```
module_name/
├── docs/
│   ├── README.md
│   ├── architecture.md
│   ├── technical-specs.md
│   ├── api-reference.md
│   ├── user-guide.md
│   └── ...
├── models/
├── views/
└── ...
```

### 2. Read All Documentation Files
Use the `list_dir` and `read_file` tools to:
- List all files in the `docs` folder
- Read each documentation file completely. Read the full document from start to finish, including all sections
- Understand the module's:
  - Purpose and business objectives
  - Architecture and design patterns
  - Technical structure and dependencies
  - API specifications
  - User workflows and requirements

### 3. Document Understanding
Before proceeding with any work, summarize:
- Module's primary purpose and functionality
- Key architectural decisions and patterns
- Technical constraints and dependencies
- Integration points with other modules
- Security and access control requirements

### 4. Validate Against Documentation
When making changes or suggestions:
- Ensure alignment with documented architecture
- Follow established patterns and conventions
- Respect technical constraints
- Maintain consistency with existing implementations
- Consider impact on documented workflows

## Implementation Guidelines

### Before Any Code Changes
1. **Read Module Documentation**: Use tools to read all files in `docs/` folder
2. **Understand Context**: Comprehend the module's purpose and architecture
3. **Identify Dependencies**: Note any external dependencies or integrations
4. **Review Patterns**: Understand established coding patterns and conventions
5. **Check Constraints**: Note any technical or business constraints

### When Providing Assistance
1. **Reference Documentation**: Base responses on documented architecture
2. **Follow Patterns**: Use established patterns from the documentation
3. **Consider Impact**: Evaluate how suggestions affect documented workflows
4. **Maintain Consistency**: Ensure suggestions align with module design
5. **Document Decisions**: Note any deviations from documented patterns

### When Debugging Issues
1. **Check Documentation**: Verify if issue is documented or expected
2. **Follow Troubleshooting**: Use documented troubleshooting procedures
3. **Understand Context**: Apply knowledge of module architecture
4. **Consider Dependencies**: Check documented integration points
5. **Validate Solutions**: Ensure solutions align with module design

## Examples

### Good Practice
```
User: "I need help with the delivery module"
AI: "Let me first read the module documentation to understand its purpose and architecture..."
[AI reads all docs/ files]
AI: "Based on the documentation, this module handles freight carrier delivery management. 
The architecture uses a state-based workflow with portal integration. 
For your specific issue, I need to consider the documented security model..."
```

### Bad Practice
```
User: "I need help with the delivery module"
AI: "I'll help you with that. Let me suggest some code changes..."
[AI proceeds without reading documentation]
```

## Documentation File Types to Read

### Required Files (if present)
- `README.md` - Module overview and setup instructions
- `architecture.md` - System design and component relationships
- `technical-specs.md` - Technical implementation details
- `api-reference.md` - API endpoints and data structures
- `user-guide.md` - User workflows and business processes

### Optional Files (if present)
- `deployment.md` - Deployment and configuration instructions
- `testing.md` - Testing strategies and procedures
- `security.md` - Security model and access controls
- `performance.md` - Performance considerations and optimizations
- `troubleshooting.md` - Common issues and solutions

## Tool Usage Pattern

### Step 1: List Documentation Files
```python
list_dir(relative_workspace_path="module_name/docs")
```

### Step 2: Read Each File
```python
read_file(target_file="module_name/docs/README.md", should_read_entire_file=True)
read_file(target_file="module_name/docs/architecture.md", should_read_entire_file=True)
# ... continue for all files
```

### Step 3: Summarize Understanding
Before proceeding, provide a brief summary of:
- Module purpose and scope
- Key architectural components
- Technical constraints
- Integration points
- Security considerations

## Compliance Checklist

Before making any changes or providing assistance:
- [ ] Located and listed all files in module's `docs/` folder
- [ ] Read and understood all documentation files
- [ ] Identified module's primary purpose and functionality
- [ ] Understood architectural patterns and design decisions
- [ ] Recognized technical constraints and dependencies
- [ ] Identified integration points with other modules
- [ ] Understood security and access control requirements
- [ ] Documented any questions or clarifications needed

## Exception Handling

If documentation is missing or incomplete:
1. **Note the Gap**: Clearly identify what documentation is missing
2. **Request Clarification**: Ask user for missing information
3. **Make Assumptions Explicit**: State any assumptions made due to missing docs
4. **Suggest Documentation**: Recommend creating missing documentation
5. **Proceed Cautiously**: Make conservative changes with clear documentation

## Benefits

Following this rule ensures:
- **Consistency**: Changes align with documented architecture
- **Quality**: Solutions follow established patterns and conventions
- **Maintainability**: Code remains consistent with module design
- **Efficiency**: Reduces rework due to architectural misalignment
- **Reliability**: Solutions work within documented constraints
- **Knowledge Transfer**: Maintains institutional knowledge

Remember: Always read the documentation first. It's better to spend time understanding the module's design than to make changes that don't align with its architecture and purpose.
