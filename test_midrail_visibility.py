#!/usr/bin/env python3
"""
Test script to debug midrail visibility condition evaluation
"""
import sys
import os
import json

# Add the Odoo path
sys.path.append('/home/<USER>/odoo/itms/canbrax18')

# Set up Odoo environment
import odoo
from odoo import api, SUPERUSER_ID

def test_midrail_visibility():
    """Test midrail visibility condition evaluation"""
    
    # Initialize Odoo
    odoo.tools.config.parse_config(['-c', '/home/<USER>/odoo/itms/canbrax18/.vscode/canbrax.conf'])
    
    with api.Environment.manage():
        env = api.Environment(odoo.registry('canbrax_sb_2'), SUPERUSER_ID, {})
        
        # Test field values (same as in our test case)
        field_values = {
            "bx_dbl_hinge_location": "brisbane",
            "bx_dbl_hinge_jamb_reveal_gt_20mm": "yes_gt_20mm",
            "bx_dbl_hinge_new_door_clear_handle": "yes",
            "bx_dbl_hinge_handle_clear_existing": "yes",
            "bx_dbl_hinge_swing_path_clear": "yes",
            "bx_dbl_hinge_quantity": 1,
            "bx_dbl_hinge_frame_colour": "black_anodise_15mm",
            "bx_dbl_hinge_mesh_type": "pet_mesh",
            "bx_dbl_hinge_door_height_mm": 2100,
            "bx_dbl_hinge_door_width_mm": 1200,
            "bx_dbl_hinge_num_sec_hinges_pickup": 0,
            "bx_dbl_hinge_num_sec_hinges_deliver": "3_per_door_loose_drilled",
            "bx_dbl_hinge_sec_hinge_packers_qty": 0,
            "_CALCULATED_largest_door_height": 2100,
            "_CALCULATED_largest_door_width": 1200
        }
        
        print("=== MIDRAIL VISIBILITY TEST ===")
        print(f"Test field values: height={field_values.get('_CALCULATED_largest_door_height')}, width={field_values.get('_CALCULATED_largest_door_width')}")
        
        # Find the Midrail (Conditional Case 2) field
        midrail_field = env['config.matrix.field'].search([
            ('name', '=', 'Midrail (Conditional Case 2)'),
            ('technical_name', '=', 'bx_dbl_hinge_midrail_case2')
        ], limit=1)
        
        if not midrail_field:
            print("❌ Midrail (Conditional Case 2) field not found!")
            return
        
        print(f"✅ Found field: {midrail_field.name} (ID: {midrail_field.id})")
        print(f"Visibility condition: {midrail_field.visibility_condition}")
        
        # Test visibility evaluation
        try:
            is_visible = midrail_field.evaluate_visibility(field_values)
            print(f"✅ Field visibility result: {is_visible}")
            
            # Test the condition manually
            condition = midrail_field.visibility_condition
            if condition.startswith('__JSON__'):
                print("✅ Condition is JSON format")
                json_str = condition[8:]  # Remove '__JSON__' prefix
                condition_data = json.loads(json_str)
                print(f"Condition data: {condition_data}")
                
                # Evaluate each condition
                for i, item in enumerate(condition_data):
                    cond_expr = item['condition']
                    logic_op = item['logic']
                    print(f"Condition {i+1}: {cond_expr} (logic: {logic_op})")
                    
                    # Manual evaluation
                    height = field_values.get('_CALCULATED_largest_door_height', 0)
                    width = field_values.get('_CALCULATED_largest_door_width', 0)
                    
                    if cond_expr == "_CALCULATED_largest_door_height <= 2510 && _CALCULATED_largest_door_width <= 1310":
                        result = height <= 2510 and width <= 1310
                        print(f"  Manual eval: {height} <= 2510 && {width} <= 1310 = {result}")
                    elif cond_expr == "_CALCULATED_largest_door_height <= 1310":
                        result = height <= 1310
                        print(f"  Manual eval: {height} <= 1310 = {result}")
                    else:
                        print(f"  Unknown condition: {cond_expr}")
                        
        except Exception as e:
            print(f"❌ Error evaluating visibility: {e}")
            import traceback
            traceback.print_exc()
        
        # Test formula helper directly
        print("\n=== FORMULA HELPER TEST ===")
        try:
            formula_helper = env['config.matrix.formula.helper'].sudo()
            condition = midrail_field.visibility_condition
            result = formula_helper.evaluate_visibility_condition(condition, field_values, default_result=True)
            print(f"✅ Formula helper result: {result}")
        except Exception as e:
            print(f"❌ Formula helper error: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_midrail_visibility()
