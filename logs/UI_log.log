visibility_conditions.js?v=1757407876:1995 [PERF] Captured original value for bx_dbl_hinge_num_sec_hinges_pickup: 3_per_door_attached
components_list.js?v=1757407876:91 [COMPONENTS] Captured original value for bx_dbl_hinge_num_sec_hinges_pickup: 3_per_door_attached
visibility_conditions.js?v=1757407876:2018 [MANUAL] Field bx_dbl_hinge_num_sec_hinges_pickup marked as manually edited with value: 0
visibility_conditions.js?v=1757407876:2034 [PERF] Change event triggered for field: bx_dbl_hinge_num_sec_hinges_pickup
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
price_matrix_handler.js?v=1757407876:217 [PRICE_MATRIX] Getting price from matrix with configuration data
svg_conditional_layers.js?v=1757407876:170 [SVG-FIX] Field bx_dbl_hinge_num_sec_hinges_pickup changed to: 0
visibility_conditions.js?v=1757407876:68 [PERF] Starting heavy operations async...
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:85 [PERF] Heavy operations completed
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
components_list.js?v=1757407876:120 [COMPONENTS] Value changed for bx_dbl_hinge_num_sec_hinges_pickup: 3_per_door_attached → 0
visibility_conditions.js?v=1757407876:50 [PERF] Debounced visibility update triggered
visibility_conditions.js?v=1757407876:1278 [08:53:49.984] updateFieldVisibility >> (called from: at http://localhost:8069/canbrax_configmatrix/static/src/js/visibility_conditions.js?v=1757407876:51:13 )
visibility_conditions.js?v=1757407876:1328 Field bx_dbl_hinge_spec_hinge_loc_3_pickup_dd became hidden but has value: no
visibility_conditions.js?v=1757407876:1400 Auto-clearing 1 fields that became hidden: ['bx_dbl_hinge_spec_hinge_loc_3_pickup_dd']
visibility_conditions.js?v=1757407876:1483 [PERF] Scheduling cascade update for 1 field changes
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:49 [Violation] 'setTimeout' handler took 997ms
svg_conditional_layers.js?v=1757407876:245 [SVG-FIX] Periodic check detected change: bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out = single
svg_conditional_layers.js?v=1757407876:245 [SVG-FIX] Periodic check detected change: bx_dbl_hinge_spec_hinge_loc_3_pickup_dd = no
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
operation_costs_handler.js?v=1757407876:389 [OperationCosts] Added calculated fields: (71) ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category']
operation_costs_handler.js?v=1757407876:216 [OperationCosts] Refreshing operation costs with values: {bx_dbl_hinge_location: '', bx_dbl_hinge_location_other: '', bx_dbl_hinge_jamb_reveal_gt_20mm: '', bx_dbl_hinge_screen_clears_handle: '', bx_dbl_hinge_new_handle_clears_existing: '', …}
operation_costs_handler.js?v=1757407876:217 [OperationCosts] Template ID: 24
operation_costs_handler.js?v=1757407876:245 [OperationCosts] Calculation state: {useBomCalculation: false, configId: null}
operation_costs_handler.js?v=1757407876:259 [OperationCosts] 🔄 Using field/option mapping for live calculation
operation_costs_handler.js?v=1757407876:260 [OperationCosts] Reason: Form has been modified
components_list.js?v=1757407876:201 [COMPONENTS] Debounced BOM update triggered
price_matrix_handler.js?v=1757407876:225 [PRICE_MATRIX] Updated price to: $751.60
price_matrix_handler.js?v=1757407876:232 [PRICE_MATRIX] Updated field-price-matrix input: 751.6045160007982
visibility_conditions.js?v=1757407876:50 [PERF] Debounced visibility update triggered
visibility_conditions.js?v=1757407876:1278 [08:53:51.187] updateFieldVisibility >> (called from: at http://localhost:8069/canbrax_configmatrix/static/src/js/visibility_conditions.js?v=1757407876:51:13 )
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:475 [MIDRAIL-JS] Calculating midrail height...
visibility_conditions.js?v=1757407876:513 [MIDRAIL-JS] No midrail height field found
visibility_conditions.js?v=1757407876:477 [MIDRAIL-JS] Result: 0
visibility_conditions.js?v=1757407876:49 [Violation] 'setTimeout' handler took 1252ms
components_list.js?v=1757407876:236 [COMPONENTS] Starting BOM generation...
components_list.js?v=1757407876:243 [COMPONENTS] BOM generation completed in 54.30ms
svg_conditional_layers.js?v=1757407876:388 [SVG-FIX] 🚀 renderSvg() called
svg_conditional_layers.js?v=1757407876:389 [SVG-FIX] Current fieldValues before render: {bx_dbl_hinge_location: '', bx_dbl_hinge_location_other: '', bx_dbl_hinge_jamb_reveal_gt_20mm: '', bx_dbl_hinge_screen_clears_handle: '', bx_dbl_hinge_new_handle_clears_existing: '', …}
svg_conditional_layers.js?v=1757407876:397 [SVG-FIX] Reloading SVG components with current field values...
svg_conditional_layers.js?v=1757407876:245 [SVG-FIX] Periodic check detected change: bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out = single
svg_conditional_layers.js?v=1757407876:388 [SVG-FIX] 🚀 renderSvg() called
svg_conditional_layers.js?v=1757407876:389 [SVG-FIX] Current fieldValues before render: {bx_dbl_hinge_location: '', bx_dbl_hinge_location_other: '', bx_dbl_hinge_jamb_reveal_gt_20mm: '', bx_dbl_hinge_screen_clears_handle: '', bx_dbl_hinge_new_handle_clears_existing: '', …}
svg_conditional_layers.js?v=1757407876:397 [SVG-FIX] Reloading SVG components with current field values...
svg_conditional_layers.js?v=1757407876:420 [SVG-FIX] Reloaded 0 SVG components
svg_conditional_layers.js?v=1757407876:428 [SVG-FIX] About to fetch calculated fields...
svg_conditional_layers.js?v=1757407876:420 [SVG-FIX] Reloaded 0 SVG components
svg_conditional_layers.js?v=1757407876:428 [SVG-FIX] About to fetch calculated fields...
operation_costs_handler.js?v=1757407876:282 [OperationCosts] Response status: 200
operation_costs_handler.js?v=1757407876:283 [OperationCosts] Response ok: true
operation_costs_handler.js?v=1757407876:290 [OperationCosts] Raw response (first 500 chars): {"jsonrpc": "2.0", "id": 1757408031169, "result": {"success": true, "operations": [{"name": "Receive Material Time", "workcenter": "MATERIAL RECEIVING", "cost": 5.6000000000000005, "duration": 0.07, "formula": "60.0", "question_number": 9, "field_name": "Frame Colour", "source_type": "field"}, {"name": "CNC Load / Unload Extrusion & Set Up", "workcenter": "DOORS: LOCK CUT OUT, CNC HOLES & BENDER", "cost": 2.64, "duration": 0.033, "formula": "60.0", "question_number": 9, "field_name": "Frame Colo
operation_costs_handler.js?v=1757407876:309 [OperationCosts] Success! Result: {success: true, operations: Array(53), total_cost: 260.75153600000004, quantity_multiplier: 1}
operation_costs_handler.js?v=1757407876:310 [OperationCosts] Operations data: (53) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 0: {name: 'Receive Material Time', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 1: {name: 'CNC Load / Unload Extrusion & Set Up', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 2: {name: 'QC & Colour Consitency CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 3: {name: 'Cut CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 4: {name: 'Cut Plugh for CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 5: {name: 'CNC Centre Lock Cut Out', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 6: {name: 'Insert Plugh into CommandeX Door Frame', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 7: {name: 'Assemble CommandeX Door with Crimped Corners', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 8: {name: 'Plugh CommandeX Door with Assembly Press', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 9: {name: 'Clamp Product (even when midrail/brace is used) (per Panel)', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 10: {name: 'QC CommandeX Door', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 11: {name: 'Cut Mesh or Sheet', question_number: 9, field_name: 'Frame Colour', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 12: {name: 'Install Centre Lock', question_number: 79, field_name: 'CommandeX Hinged - Lock Type (Top of Cut Out)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 13: {name: 'Pick Centre Lock Site Kit', question_number: 79, field_name: 'CommandeX Hinged - Lock Type (Top of Cut Out)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 14: {name: 'QC Picked Hardware (Lock Site Kit)', question_number: 79, field_name: 'CommandeX Hinged - Lock Type (Top of Cut Out)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 15: {name: 'Wrap Hardware (Lock Site Kit)', question_number: 79, field_name: 'CommandeX Hinged - Lock Type (Top of Cut Out)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 16: {name: 'Load Door (Delivery) / Customer Collect (Pick Up)', question_number: 171, field_name: 'Number of Security Hinges (Pick Up)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 17: {name: 'Delivery Time - Time allocated to CD1 even for pick up orders', question_number: 171, field_name: 'Number of Security Hinges (Pick Up)', source_type: 'field', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 18: {name: 'Cut Extrusions on Upcut Saw', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 19: {name: 'CNC Load / Unload Extrusion & Set Up 2', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 20: {name: 'Extra Time to Cut French Door Mullion', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 21: {name: 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 22: {name: 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 23: {name: 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 24: {name: 'Insert Top & Bottom French Door Mullion Caps', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 25: {name: 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door', question_number: 62, field_name: 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 26: {name: 'Pick Hardware', question_number: 76, field_name: 'CommandeX Hinged - Cylinder', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 27: {name: 'QC Picked Hardware (Cylinder)', question_number: 76, field_name: 'CommandeX Hinged - Cylinder', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 28: {name: 'Wrap Hardware (Cylinder)', question_number: 76, field_name: 'CommandeX Hinged - Cylinder', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 29: {name: 'CNC Non-Lock Door Centre Striker Cut Out', question_number: 160, field_name: 'Non-Lock Door Striker Cut Outs', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 30: {name: 'CNC Load / Unload Extrusion & Set Up', question_number: 160, field_name: 'Non-Lock Door Striker Cut Outs', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 31: {name: 'CNC Flush Bolt Cut Out', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 32: {name: 'Rivet Flush Bolts into Non-Lock Door', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 33: {name: 'CNC Load / Unload Extrusion & Set Up', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 34: {name: 'CNC Load / Unload Extrusion & Set Up', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 35: {name: 'CNC Flush Bolt Hole in Top or Btm', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 36: {name: 'CNC Flush Bolt Hole in Top or Btm', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 37: {name: 'CNC Flush Bolt Cut Out', question_number: 161, field_name: 'Flush Bolts & Patio Bolts (Non-Lock Door)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 38: {name: 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion', question_number: 167, field_name: 'Midrail (Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 39: {name: 'Cut Extrusions on Upcut Saw (Midrail)', question_number: 167, field_name: 'Midrail (Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 40: {name: 'Cut Plugh for CommandeX Midrail', question_number: 167, field_name: 'Midrail (Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 41: {name: 'Insert Plugh into CommandeX Midrail', question_number: 167, field_name: 'Midrail (Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 42: {name: 'Extra Time to Assemble Product with CommandeX Midrail', question_number: 167, field_name: 'Midrail (Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 43: {name: 'Plugh CommandeX Midrail by Hand', question_number: 167, field_name: 'Midrail (Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 44: {name: 'QC Mill Finish Extrusions for Powder Coat or QC Buy A Length Extrusions Received', question_number: 169, field_name: 'Midrail Colour (Standard Frame, Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 45: {name: 'Extra Time to Cut Powder Coat Extrusions', question_number: 169, field_name: 'Midrail Colour (Standard Frame, Conditional Case 2)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 46: {name: 'CNC Hinge Holes (Lock Door)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 47: {name: 'CNC Hinge Holes (Non-Lock Door)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 48: {name: 'Pick Hinges', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 49: {name: 'QC Picked Hardware (x Qty of Hinges)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 50: {name: 'Wrap Hardware (x Qty of Hinges)', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 51: {name: 'CNC Load / Unload Extrusion & Set Up', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
operation_costs_handler.js?v=1757407876:315 [OperationCosts] Operation 52: {name: 'CNC Load / Unload Extrusion & Set Up', question_number: 172, field_name: 'Number of Security Hinges (Deliver/Site)', source_type: 'option', formula: '60.0', …}
price_matrix_handler.js?v=1757407876:217 [PRICE_MATRIX] Getting price from matrix with configuration data
configurator?product_id=14733&order_line_id=229&matrix_id=24&template_id=24:17789 [TOTAL PRICE UPDATER] updateTotalPriceField called
configurator?product_id=14733&order_line_id=229&matrix_id=24&template_id=24:17817 [TOTAL PRICE UPDATER] Set component price field to: 122.68
configurator?product_id=14733&order_line_id=229&matrix_id=24&template_id=24:17822 [TOTAL PRICE UPDATER] Set matrix price field to: 751.6
configurator?product_id=14733&order_line_id=229&matrix_id=24&template_id=24:17825 [TOTAL PRICE UPDATER] Updated fields - Components: 122.68 Operations: 260.75 Matrix: 751.6 Total: 1135.03
svg_conditional_layers.js?v=1757407876:430 [SVG-FIX] Raw calculated fields response: {_CALCULATED_deduction_assistance: 'no', _CALCULATED_door_split_type: 'even', _CALCULATED_lock_height: 0, _CALCULATED_manual_left_height: 2110, _CALCULATED_manual_right_height: 2110, …}
svg_conditional_layers.js?v=1757407876:432 [SVG-FIX] Merged calculated fields: (71) ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category']
svg_conditional_layers.js?v=1757407876:433 [SVG-FIX] Final fieldValues after merge: {bx_dbl_hinge_location: '', bx_dbl_hinge_location_other: '', bx_dbl_hinge_jamb_reveal_gt_20mm: '', bx_dbl_hinge_screen_clears_handle: '', bx_dbl_hinge_new_handle_clears_existing: '', …}
svg_conditional_layers.js?v=1757407876:439 [SVG-FIX] No SVG components available
renderSvg @ svg_conditional_layers.js?v=1757407876:439
await in renderSvg
(anonymous) @ svg_conditional_layers.js?v=1757407876:256
setTimeout
checkForFieldValueChanges @ svg_conditional_layers.js?v=1757407876:255
svg_conditional_layers.js?v=1757407876:430 [SVG-FIX] Raw calculated fields response: {_CALCULATED_deduction_assistance: 'no', _CALCULATED_door_split_type: 'even', _CALCULATED_lock_height: 0, _CALCULATED_manual_left_height: 2110, _CALCULATED_manual_right_height: 2110, …}
svg_conditional_layers.js?v=1757407876:432 [SVG-FIX] Merged calculated fields: (71) ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category']
svg_conditional_layers.js?v=1757407876:433 [SVG-FIX] Final fieldValues after merge: {bx_dbl_hinge_location: '', bx_dbl_hinge_location_other: '', bx_dbl_hinge_jamb_reveal_gt_20mm: '', bx_dbl_hinge_screen_clears_handle: '', bx_dbl_hinge_new_handle_clears_existing: '', …}
svg_conditional_layers.js?v=1757407876:439 [SVG-FIX] No SVG components available
renderSvg @ svg_conditional_layers.js?v=1757407876:439
await in renderSvg
(anonymous) @ svg_conditional_layers.js?v=1757407876:256
setTimeout
checkForFieldValueChanges @ svg_conditional_layers.js?v=1757407876:255
price_matrix_handler.js?v=1757407876:225 [PRICE_MATRIX] Updated price to: $751.60
price_matrix_handler.js?v=1757407876:232 [PRICE_MATRIX] Updated field-price-matrix input: 751.6045160007982
