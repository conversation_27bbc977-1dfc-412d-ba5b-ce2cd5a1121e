2025-09-09 07:14:28,820 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Dependency graph: {'_CALCULATED_deduction_assistance': [], '_CALCULATED_door_split_type': [], '_CALCULATED_lock_height': [], '_CALCULATED_manual_left_height': [], '_CALCULATED_manual_right_height': [], '_CALCULATED_height_calculation_method': [], '_CALCULATED_is_even_split': ['_CALCULATED_door_split_type'], '_CALCULATED_is_manual_mode': ['_CALCULATED_height_calculation_method'], '_CALCULATED_is_uneven_split': ['_CALCULATED_door_split_type'], '_CALCULATED_largest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_smallest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_halfway_point': ['_CALCULATED_smallest_door_height'], '_CALCULATED_halfway_minus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_16': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_32': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_height_minus_1000': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1003': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1019': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1090': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1098': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1137': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1153': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1169': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1248': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_270': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_317': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_330': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_333': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_349': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_428': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_740': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_787': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_800': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_803': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_819': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_898': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_940': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_987': ['_CALCULATED_smallest_door_height'], '_CALCULATED_Largest_Sum_Door_Width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_even_bottom_width': [], '_CALCULATED_even_middle_width': [], '_CALCULATED_even_top_width': [], '_CALCULATED_jamb_adaptor_left_length_mm': [], '_CALCULATED_jamb_adaptor_top_length_mm': [], '_CALCULATED_largest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_left_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_right_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_Clamp_Product': ['_CALCULATED_left_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width'], '_CALCULATED_left_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_manual_formula': ['_CALCULATED_smallest_door_height', '_CALCULATED_manual_right_height', '_CALCULATED_manual_left_height'], '_CALCULATED_right_Clamp_Product': ['_CALCULATED_largest_right_door_width', '_CALCULATED_right_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_right_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_right_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_smallest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_mesh_required': ['_CALCULATED_largest_door_width', '_CALCULATED_largest_door_height'], '_CALCULATED_mesh_width': ['_CALCULATED_largest_door_width', '_CALCULATED_mesh_required'], '_CALCULATED_mesh_height': ['_CALCULATED_largest_door_height', '_CALCULATED_mesh_required'], '_CALCULATED_midrail_case1': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_mesh_series': ['_CALCULATED_mesh_required'], '_CALCULATED_midrail_case2': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_midrail_height': [], '_CALCULATED_mesh_area': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_required': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_type': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required'], '_CALCULATED_mesh_area_m2': ['_CALCULATED_mesh_area'], '_CALCULATED_mesh_perimeter': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_aspect_ratio': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_size_category': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_required']} 
2025-09-09 07:14:28,820 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Improved topological sort successful: ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
2025-09-09 07:14:28,820 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Starting calculation with 71 fields in dependency order 
2025-09-09 07:14:30,340 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Maximum calculation passes (10) reached. Some fields may not be calculated. 
2025-09-09 07:14:30,341 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_operation_type could not be calculated after 10 passes 
2025-09-09 07:14:30,341 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_size_category could not be calculated after 10 passes 
2025-09-09 07:14:30,341 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Calculation completed in 10 passes. 69/71 fields calculated successfully. 
2025-09-09 07:14:33,163 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ========== STARTING OPERATION COST CALCULATION ========== 
2025-09-09 07:14:33,163 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template ID: 24 
2025-09-09 07:14:33,163 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Calculation context: field_option_mapping 
2025-09-09 07:14:33,163 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Config ID: None 
2025-09-09 07:14:33,163 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Method: Field/Option Mapping 
2025-09-09 07:14:33,164 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Total field values received: 300 
2025-09-09 07:14:33,164 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
2025-09-09 07:14:33,164 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Key field bx_dbl_hinge_num_sec_hinges_pickup: 0 
2025-09-09 07:14:33,164 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Key field bx_dbl_hinge_num_sec_hinges_deliver: 3_per_door_loose_drilled 
2025-09-09 07:14:33,164 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Key field bx_dbl_hinge_quantity: 1 
2025-09-09 07:14:33,164 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Key field _CALCULATED_mesh_operation_required: True 
2025-09-09 07:14:33,164 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Key field _CALCULATED_mesh_operation_type: precision 
2025-09-09 07:14:33,174 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Dependency graph: {'_CALCULATED_deduction_assistance': [], '_CALCULATED_door_split_type': [], '_CALCULATED_lock_height': [], '_CALCULATED_manual_left_height': [], '_CALCULATED_manual_right_height': [], '_CALCULATED_height_calculation_method': [], '_CALCULATED_is_even_split': ['_CALCULATED_door_split_type'], '_CALCULATED_is_manual_mode': ['_CALCULATED_height_calculation_method'], '_CALCULATED_is_uneven_split': ['_CALCULATED_door_split_type'], '_CALCULATED_largest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_smallest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_halfway_point': ['_CALCULATED_smallest_door_height'], '_CALCULATED_halfway_minus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_16': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_32': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_height_minus_1000': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1003': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1019': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1090': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1098': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1137': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1153': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1169': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1248': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_270': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_317': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_330': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_333': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_349': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_428': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_740': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_787': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_800': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_803': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_819': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_898': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_940': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_987': ['_CALCULATED_smallest_door_height'], '_CALCULATED_Largest_Sum_Door_Width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_even_bottom_width': [], '_CALCULATED_even_middle_width': [], '_CALCULATED_even_top_width': [], '_CALCULATED_jamb_adaptor_left_length_mm': [], '_CALCULATED_jamb_adaptor_top_length_mm': [], '_CALCULATED_largest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_left_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_right_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_Clamp_Product': ['_CALCULATED_left_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width'], '_CALCULATED_left_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_manual_formula': ['_CALCULATED_smallest_door_height', '_CALCULATED_manual_right_height', '_CALCULATED_manual_left_height'], '_CALCULATED_right_Clamp_Product': ['_CALCULATED_largest_right_door_width', '_CALCULATED_right_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_right_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_right_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_smallest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_mesh_required': ['_CALCULATED_largest_door_width', '_CALCULATED_largest_door_height'], '_CALCULATED_mesh_width': ['_CALCULATED_largest_door_width', '_CALCULATED_mesh_required'], '_CALCULATED_mesh_height': ['_CALCULATED_largest_door_height', '_CALCULATED_mesh_required'], '_CALCULATED_midrail_case1': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_mesh_series': ['_CALCULATED_mesh_required'], '_CALCULATED_midrail_case2': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_midrail_height': [], '_CALCULATED_mesh_area': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_required': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_type': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required'], '_CALCULATED_mesh_area_m2': ['_CALCULATED_mesh_area'], '_CALCULATED_mesh_perimeter': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_aspect_ratio': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_size_category': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_required']} 
2025-09-09 07:14:33,174 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Improved topological sort successful: ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
2025-09-09 07:14:33,174 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Starting calculation with 71 fields in dependency order 
2025-09-09 07:14:34,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Dependency graph: {'_CALCULATED_deduction_assistance': [], '_CALCULATED_door_split_type': [], '_CALCULATED_lock_height': [], '_CALCULATED_manual_left_height': [], '_CALCULATED_manual_right_height': [], '_CALCULATED_height_calculation_method': [], '_CALCULATED_is_even_split': ['_CALCULATED_door_split_type'], '_CALCULATED_is_manual_mode': ['_CALCULATED_height_calculation_method'], '_CALCULATED_is_uneven_split': ['_CALCULATED_door_split_type'], '_CALCULATED_largest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_smallest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_halfway_point': ['_CALCULATED_smallest_door_height'], '_CALCULATED_halfway_minus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_16': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_32': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_height_minus_1000': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1003': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1019': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1090': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1098': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1137': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1153': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1169': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1248': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_270': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_317': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_330': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_333': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_349': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_428': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_740': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_787': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_800': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_803': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_819': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_898': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_940': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_987': ['_CALCULATED_smallest_door_height'], '_CALCULATED_Largest_Sum_Door_Width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_even_bottom_width': [], '_CALCULATED_even_middle_width': [], '_CALCULATED_even_top_width': [], '_CALCULATED_jamb_adaptor_left_length_mm': [], '_CALCULATED_jamb_adaptor_top_length_mm': [], '_CALCULATED_largest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_left_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_right_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_Clamp_Product': ['_CALCULATED_left_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width'], '_CALCULATED_left_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_manual_formula': ['_CALCULATED_smallest_door_height', '_CALCULATED_manual_right_height', '_CALCULATED_manual_left_height'], '_CALCULATED_right_Clamp_Product': ['_CALCULATED_largest_right_door_width', '_CALCULATED_right_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_right_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_right_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_smallest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_mesh_required': ['_CALCULATED_largest_door_width', '_CALCULATED_largest_door_height'], '_CALCULATED_mesh_width': ['_CALCULATED_largest_door_width', '_CALCULATED_mesh_required'], '_CALCULATED_mesh_height': ['_CALCULATED_largest_door_height', '_CALCULATED_mesh_required'], '_CALCULATED_midrail_case1': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_mesh_series': ['_CALCULATED_mesh_required'], '_CALCULATED_midrail_case2': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_midrail_height': [], '_CALCULATED_mesh_area': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_required': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_type': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required'], '_CALCULATED_mesh_area_m2': ['_CALCULATED_mesh_area'], '_CALCULATED_mesh_perimeter': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_aspect_ratio': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_size_category': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_required']} 
2025-09-09 07:14:34,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Improved topological sort successful: ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
2025-09-09 07:14:34,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Starting calculation with 71 fields in dependency order 
2025-09-09 07:14:35,207 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Maximum calculation passes (10) reached. Some fields may not be calculated. 
2025-09-09 07:14:35,207 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_operation_type could not be calculated after 10 passes 
2025-09-09 07:14:35,207 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_size_category could not be calculated after 10 passes 
2025-09-09 07:14:35,207 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Calculation completed in 10 passes. 69/71 fields calculated successfully. 
2025-09-09 07:14:35,207 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Added 71 calculated fields 
2025-09-09 07:14:35,207 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Quantity multiplier: 1.0 
2025-09-09 07:14:35,207 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ========== USING FIELD/OPTION MAPPING CALCULATION ========== 
2025-09-09 07:14:35,207 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Reason: Context=field_option_mapping, Config ID=None 
2025-09-09 07:14:35,208 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ========== PROCESSING FIELD OPERATIONS ========== 
2025-09-09 07:14:35,208 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template has 5 sections 
2025-09-09 07:14:35,208 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing section: Location (2 fields) 
2025-09-09 07:14:35,209 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Location' (Q1): visible=True 
2025-09-09 07:14:35,210 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Location' has 0 operation mappings 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Location (Other)' (Q2): visible=False 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing section: Pre-Measure Checks (5 fields) 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Door Jamb Reveal is Greater than 20mm or No Reveal?' (Q3): visible=True 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Door Jamb Reveal is Greater than 20mm or No Reveal?' has 0 operation mappings 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Will New Screen Door Clear Handle on Existing External Door?' (Q4): visible=True 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Will New Screen Door Clear Handle on Existing External Door?' has 0 operation mappings 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Will Handle on New Screen Door Clear Existing External Door or Existing Pull/Push Bar Handle?' (Q5): visible=True 
2025-09-09 07:14:35,211 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Will Handle on New Screen Door Clear Existing External Door or Existing Pull/Push Bar Handle?' has 0 operation mappings 
2025-09-09 07:14:35,212 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'New Screen Door Swing Path is Clear from any Obstructions?' (Q6): visible=True 
2025-09-09 07:14:35,212 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'New Screen Door Swing Path is Clear from any Obstructions?' has 0 operation mappings 
2025-09-09 07:14:35,212 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Description of Rectification Required or Desctiption of Obstruction Preventing Install' (Q7): visible=False 
2025-09-09 07:14:35,212 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing section: Door Specifications (193 fields) 
2025-09-09 07:14:35,212 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Quantity' (Q8): visible=True 
2025-09-09 07:14:35,212 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Quantity' has 0 operation mappings 
2025-09-09 07:14:35,212 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Frame Colour' (Q9): visible=True 
2025-09-09 07:14:35,213 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Frame Colour' has 17 operation mappings 
2025-09-09 07:14:35,216 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Receive Material Time' for field 'Frame Colour' 
2025-09-09 07:14:35,219 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.07, Cost: 5.6000000000000005 
2025-09-09 07:14:35,222 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #1: Receive Material Time = $5.6000000000000005 
2025-09-09 07:14:35,222 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'CNC Load / Unload Extrusion & Set Up' for field 'Frame Colour' 
2025-09-09 07:14:35,224 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,225 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #2: CNC Load / Unload Extrusion & Set Up = $2.64 
2025-09-09 07:14:35,225 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'QC & Colour Consitency CommandeX Door Frame' for field 'Frame Colour' 
2025-09-09 07:14:35,226 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.04864878078, Cost: 3.8919024624 
2025-09-09 07:14:35,226 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #3: QC & Colour Consitency CommandeX Door Frame = $3.8919024624 
2025-09-09 07:14:35,226 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Cut CommandeX Door Frame' for field 'Frame Colour' 
2025-09-09 07:14:35,227 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.1928632895, Cost: 15.42906316 
2025-09-09 07:14:35,227 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #4: Cut CommandeX Door Frame = $15.42906316 
2025-09-09 07:14:35,227 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Cut Plugh for CommandeX Door Frame' for field 'Frame Colour' 
2025-09-09 07:14:35,228 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.07604323986, Cost: 6.0834591888 
2025-09-09 07:14:35,228 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #5: Cut Plugh for CommandeX Door Frame = $6.0834591888 
2025-09-09 07:14:35,228 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'CNC Centre Lock Cut Out' for field 'Frame Colour' 
2025-09-09 07:14:35,232 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,232 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #6: CNC Centre Lock Cut Out = $2.64 
2025-09-09 07:14:35,233 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Insert Plugh into CommandeX Door Frame' for field 'Frame Colour' 
2025-09-09 07:14:35,234 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.2007352605, Cost: 16.058820840000003 
2025-09-09 07:14:35,235 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #7: Insert Plugh into CommandeX Door Frame = $16.058820840000003 
2025-09-09 07:14:35,235 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Assemble CommandeX Door with Crimped Corners' for field 'Frame Colour' 
2025-09-09 07:14:35,236 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.41705702358, Cost: 33.364561886400004 
2025-09-09 07:14:35,236 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #8: Assemble CommandeX Door with Crimped Corners = $33.364561886400004 
2025-09-09 07:14:35,236 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Plugh CommandeX Door with Assembly Press' for field 'Frame Colour' 
2025-09-09 07:14:35,237 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.23190826566, Cost: 18.5526612528 
2025-09-09 07:14:35,237 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #9: Plugh CommandeX Door with Assembly Press = $18.5526612528 
2025-09-09 07:14:35,238 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Clamp Product (even when midrail/brace is used) (per Panel)' for field 'Frame Colour' 
2025-09-09 07:14:35,239 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition '_CALCULATED_left_Clamp_Product == 'yes' and _CALCULATED_right_Clamp_Product == 'no'' result: False 
2025-09-09 07:14:35,240 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation 'Clamp Product (even when midrail/brace is used) (per Panel)' condition failed, skipping 
2025-09-09 07:14:35,240 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Clamp Product (even when midrail/brace is used) (per Panel)' for field 'Frame Colour' 
2025-09-09 07:14:35,240 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition '_CALCULATED_left_Clamp_Product == 'no' and _CALCULATED_right_Clamp_Product == 'yes'' result: False 
2025-09-09 07:14:35,241 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation 'Clamp Product (even when midrail/brace is used) (per Panel)' condition failed, skipping 
2025-09-09 07:14:35,241 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Clamp Product (even when midrail/brace is used) (per Panel)' for field 'Frame Colour' 
2025-09-09 07:14:35,242 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition '_CALCULATED_left_Clamp_Product == 'yes' and _CALCULATED_right_Clamp_Product == 'yes'' result: True 
2025-09-09 07:14:35,242 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Field operation 'Clamp Product (even when midrail/brace is used) (per Panel)' condition passed 
2025-09-09 07:14:35,244 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.056, Cost: 4.48 
2025-09-09 07:14:35,244 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #10: Clamp Product (even when midrail/brace is used) (per Panel) = $4.48 
2025-09-09 07:14:35,244 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: '2nd Man to Help Move Product & Plugh (per Panel)' for field 'Frame Colour' 
2025-09-09 07:14:35,245 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition '(1610 <_CALCULATED_left_height_door and 1630 < _CALCULATED_largest_left_door_width) or (1610 <_CALCULATED_right_height_door and 1630 < _CALCULATED_largest_right_door_width)' result: False 
2025-09-09 07:14:35,245 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation '2nd Man to Help Move Product & Plugh (per Panel)' condition failed, skipping 
2025-09-09 07:14:35,245 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Clamp Product (even when midrail/brace is used) (per Panel) (Clamp Width)' for field 'Frame Colour' 
2025-09-09 07:14:35,246 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition '((1611 <= _CALCULATED_left_height_door and 1111 <= _CALCULATED_largest_left_door_width) or (1631 <= _CALCULATED_largest_left_door_width)) and bx_dbl_hinge_deduction_assistance == 'no'' result: False 
2025-09-09 07:14:35,246 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation 'Clamp Product (even when midrail/brace is used) (per Panel) (Clamp Width)' condition failed, skipping 
2025-09-09 07:14:35,246 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Clamp Product (even when midrail/brace is used) (per Panel) (Clamp Width)' for field 'Frame Colour' 
2025-09-09 07:14:35,246 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition '((1611 <= _CALCULATED_right_height_door and 1111 <= _CALCULATED_largest_right_door_width) or (1631 <= _CALCULATED_largest_right_door_width)) and bx_dbl_hinge_deduction_assistance == 'no'' result: False 
2025-09-09 07:14:35,246 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation 'Clamp Product (even when midrail/brace is used) (per Panel) (Clamp Width)' condition failed, skipping 
2025-09-09 07:14:35,247 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'QC CommandeX Door' for field 'Frame Colour' 
2025-09-09 07:14:35,248 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.22403629466, Cost: 17.9229035728 
2025-09-09 07:14:35,248 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #11: QC CommandeX Door = $17.9229035728 
2025-09-09 07:14:35,248 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Cut Mesh or Sheet' for field 'Frame Colour' 
2025-09-09 07:14:35,249 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.15003976726, Cost: 12.003181380800001 
2025-09-09 07:14:35,249 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #12: Cut Mesh or Sheet = $12.003181380800001 
2025-09-09 07:14:35,249 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Powder Coat Colour' (Q10): visible=False 
2025-09-09 07:14:35,249 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Custom Powder Coat - Colour Name' (Q11): visible=False 
2025-09-09 07:14:35,250 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Custom Powder Coat - Colour Finish' (Q12): visible=False 
2025-09-09 07:14:35,250 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Custom Powder Coat - Colour Code' (Q13): visible=False 
2025-09-09 07:14:35,251 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Door Size Deduction Assistance Required?' (Q14): visible=True 
2025-09-09 07:14:35,251 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Door Size Deduction Assistance Required?' has 0 operation mappings 
2025-09-09 07:14:35,251 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Door Split Type' (Q15): visible=False 
2025-09-09 07:14:35,251 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Opening Height (mm) (Even Split)' (Q16): visible=False 
2025-09-09 07:14:35,252 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Height Deduction (mm) (Even Split)' (Q17): visible=False 
2025-09-09 07:14:35,252 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE Each Door Height (mm) (Even Split)' (Q18): visible=False 
2025-09-09 07:14:35,253 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Opening TOP Width (mm) (Even Split)' (Q19): visible=False 
2025-09-09 07:14:35,253 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'TOP Width Deduction (mm) (Even Split)' (Q20): visible=False 
2025-09-09 07:14:35,254 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE Each Door TOP Width (mm) (Even Split)' (Q21): visible=False 
2025-09-09 07:14:35,254 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Opening MIDDLE Width (mm) (Even Split)' (Q22): visible=False 
2025-09-09 07:14:35,255 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MIDDLE Width Deduction (mm) (Even Split)' (Q23): visible=False 
2025-09-09 07:14:35,255 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE Each Door MIDDLE Width (mm) (Even Split)' (Q24): visible=False 
2025-09-09 07:14:35,255 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Opening BOTTOM Width (mm) (Even Split)' (Q25): visible=False 
2025-09-09 07:14:35,256 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'BOTTOM Width Deduction (mm) (Even Split)' (Q26): visible=False 
2025-09-09 07:14:35,256 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE Each Door BOTTOM Width (mm) (Even Split)' (Q27): visible=False 
2025-09-09 07:14:35,256 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'LEFT Opening Height (mm) (Uneven Split)' (Q28): visible=False 
2025-09-09 07:14:35,257 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Height Deduction (mm) (Uneven Split - Left)' (Q29): visible=False 
2025-09-09 07:14:35,257 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door Height (mm) (Uneven Split)' (Q30): visible=False 
2025-09-09 07:14:35,257 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'LEFT Jamb to Centre TOP Measurement (mm) (Uneven Split)' (Q31): visible=False 
2025-09-09 07:14:35,258 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'TOP LEFT Jamb to Centre Deduction (mm) (Uneven Split)' (Q32): visible=False 
2025-09-09 07:14:35,258 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door TOP Width (mm) (Uneven Split)' (Q33): visible=False 
2025-09-09 07:14:35,258 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'LEFT Jamb to Centre MIDDLE Measurement (mm) (Uneven Split)' (Q34): visible=False 
2025-09-09 07:14:35,259 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MIDDLE LEFT Jamb to Centre Deduction (mm) (Uneven Split)' (Q35): visible=False 
2025-09-09 07:14:35,259 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door MIDDLE Width (mm) (Uneven Split)' (Q36): visible=False 
2025-09-09 07:14:35,260 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'LEFT Jamb to Centre BOTTOM Measurement (mm) (Uneven Split)' (Q37): visible=False 
2025-09-09 07:14:35,260 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'BOTTOM LEFT Jamb to Centre Deduction (mm) (Uneven Split)' (Q38): visible=False 
2025-09-09 07:14:35,260 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door BOTTOM Width (mm) (Uneven Split)' (Q39): visible=False 
2025-09-09 07:14:35,261 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'RIGHT Opening Height (mm) (Uneven Split)' (Q40): visible=False 
2025-09-09 07:14:35,261 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Height Deduction (mm) (Uneven Split - Right)' (Q41): visible=False 
2025-09-09 07:14:35,261 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door Height (mm) (Uneven Split)' (Q42): visible=False 
2025-09-09 07:14:35,262 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'RIGHT Jamb to Centre TOP Measurement (mm) (Uneven Split)' (Q43): visible=False 
2025-09-09 07:14:35,262 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'TOP RIGHT Jamb to Centre Deduction (mm) (Uneven Split)' (Q44): visible=False 
2025-09-09 07:14:35,262 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door TOP Width (mm) (Uneven Split)' (Q45): visible=False 
2025-09-09 07:14:35,263 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'RIGHT Jamb to Centre MIDDLE Measurement (mm) (Uneven Split)' (Q46): visible=False 
2025-09-09 07:14:35,263 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MIDDLE RIGHT Jamb to Centre Deduction (mm) (Uneven Split)' (Q47): visible=False 
2025-09-09 07:14:35,263 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door MIDDLE Width (mm) (Uneven Split)' (Q48): visible=False 
2025-09-09 07:14:35,264 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'RIGHT Jamb to Centre BOTTOM Measurement (mm) (Uneven Split)' (Q49): visible=False 
2025-09-09 07:14:35,264 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'BOTTOM RIGHT Jamb to Centre Deduction (mm) (Uneven Split)' (Q50): visible=False 
2025-09-09 07:14:35,265 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door BOTTOM Width (mm) (Uneven Split)' (Q51): visible=False 
2025-09-09 07:14:35,265 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door Height (mm) (Manual)' (Q52): visible=True 
2025-09-09 07:14:35,266 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door Height (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,266 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door TOP Width (mm) (Manual)' (Q53): visible=True 
2025-09-09 07:14:35,266 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door TOP Width (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,266 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door MIDDLE Width (mm) (Manual)' (Q54): visible=True 
2025-09-09 07:14:35,266 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door MIDDLE Width (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,267 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door BOTTOM Width (mm) (Manual)' (Q55): visible=True 
2025-09-09 07:14:35,267 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door BOTTOM Width (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,267 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door Height (mm) (Manual)' (Q56): visible=True 
2025-09-09 07:14:35,267 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door Height (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,267 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door TOP Width (mm) (Manual)' (Q57): visible=True 
2025-09-09 07:14:35,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door TOP Width (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door MIDDLE Width (mm) (Manual)' (Q58): visible=True 
2025-09-09 07:14:35,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door MIDDLE Width (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door BOTTOM Width (mm) (Manual)' (Q59): visible=True 
2025-09-09 07:14:35,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door BOTTOM Width (mm) (Manual)' has 0 operation mappings 
2025-09-09 07:14:35,268 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Swing' (Q60): visible=True 
2025-09-09 07:14:35,269 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Swing' has 0 operation mappings 
2025-09-09 07:14:35,269 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'T-Section Mullion w/ Mohair (attached to outside of Non-Lock Door)' (Q61): visible=False 
2025-09-09 07:14:35,269 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' (Q62): visible=True 
2025-09-09 07:14:35,269 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' has 0 operation mappings 
2025-09-09 07:14:35,270 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lock Brand' (Q63): visible=True 
2025-09-09 07:14:35,270 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lock Brand' has 0 operation mappings 
2025-09-09 07:14:35,270 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Colour' (Q64): visible=False 
2025-09-09 07:14:35,270 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Cylinder' (Q65): visible=False 
2025-09-09 07:14:35,271 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Height Location' (Q66): visible=False 
2025-09-09 07:14:35,271 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Height (mm) (Top of Cut Out)' (Q67): visible=False 
2025-09-09 07:14:35,271 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Type (Top of Cut Out)' (Q68): visible=False 
2025-09-09 07:14:35,272 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Height (mm) (Centre of Cut Out)' (Q69): visible=False 
2025-09-09 07:14:35,272 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Type (Centre of Cut Out)' (Q70): visible=False 
2025-09-09 07:14:35,272 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Height (mm) (Bottom of Cut Out)' (Q71): visible=False 
2025-09-09 07:14:35,273 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Type (Bottom of Cut Out)' (Q72): visible=False 
2025-09-09 07:14:35,273 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Height (mm) (Centre of Handle Lever)' (Q73): visible=False 
2025-09-09 07:14:35,274 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC - Lock Type (Centre of Handle Lever)' (Q74): visible=False 
2025-09-09 07:14:35,274 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Colour' (Q75): visible=True 
2025-09-09 07:14:35,274 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Colour' has 0 operation mappings 
2025-09-09 07:14:35,274 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Cylinder' (Q76): visible=True 
2025-09-09 07:14:35,275 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Cylinder' has 0 operation mappings 
2025-09-09 07:14:35,275 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height Location' (Q77): visible=True 
2025-09-09 07:14:35,275 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height Location' has 0 operation mappings 
2025-09-09 07:14:35,275 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height (mm) (Top of Cut Out)' (Q78): visible=True 
2025-09-09 07:14:35,275 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height (mm) (Top of Cut Out)' has 0 operation mappings 
2025-09-09 07:14:35,276 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Type (Top of Cut Out)' (Q79): visible=True 
2025-09-09 07:14:35,276 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Type (Top of Cut Out)' has 4 operation mappings 
2025-09-09 07:14:35,276 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Install Centre Lock' for field 'CommandeX Hinged - Lock Type (Top of Cut Out)' 
2025-09-09 07:14:35,278 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.081, Cost: 6.48 
2025-09-09 07:14:35,278 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #13: Install Centre Lock = $6.48 
2025-09-09 07:14:35,279 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Pick Centre Lock Site Kit' for field 'CommandeX Hinged - Lock Type (Top of Cut Out)' 
2025-09-09 07:14:35,280 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.017, Cost: 1.36 
2025-09-09 07:14:35,281 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #14: Pick Centre Lock Site Kit = $1.36 
2025-09-09 07:14:35,281 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'QC Picked Hardware (Lock Site Kit)' for field 'CommandeX Hinged - Lock Type (Top of Cut Out)' 
2025-09-09 07:14:35,283 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.005, Cost: 0.4 
2025-09-09 07:14:35,283 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #15: QC Picked Hardware (Lock Site Kit) = $0.4 
2025-09-09 07:14:35,283 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Wrap Hardware (Lock Site Kit)' for field 'CommandeX Hinged - Lock Type (Top of Cut Out)' 
2025-09-09 07:14:35,285 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.005, Cost: 0.4 
2025-09-09 07:14:35,285 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #16: Wrap Hardware (Lock Site Kit) = $0.4 
2025-09-09 07:14:35,285 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height (mm) (Centre of Cut Out)' (Q80): visible=False 
2025-09-09 07:14:35,286 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Type (Centre of Cut Out)' (Q81): visible=False 
2025-09-09 07:14:35,286 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height (mm) (Bottom of Cut Out)' (Q82): visible=False 
2025-09-09 07:14:35,287 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Type (Bottom of Cut Out)' (Q83): visible=False 
2025-09-09 07:14:35,287 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height (mm) (Centre of Handle Lever)' (Q84): visible=False 
2025-09-09 07:14:35,287 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Type (Centre of Handle Lever)' (Q85): visible=False 
2025-09-09 07:14:35,288 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Colour' (Q86): visible=False 
2025-09-09 07:14:35,288 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Cylinder' (Q87): visible=False 
2025-09-09 07:14:35,288 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Height Location' (Q88): visible=False 
2025-09-09 07:14:35,289 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Height (mm) (Top of Cut Out)' (Q89): visible=False 
2025-09-09 07:14:35,289 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Type (Top of Cut Out)' (Q90): visible=False 
2025-09-09 07:14:35,290 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Height (mm) (Centre of Cut Out)' (Q91): visible=False 
2025-09-09 07:14:35,290 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Type (Centre of Cut Out)' (Q92): visible=False 
2025-09-09 07:14:35,291 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Height (mm) (Bottom of Cut Out)' (Q93): visible=False 
2025-09-09 07:14:35,291 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Type (Bottom of Cut Out)' (Q94): visible=False 
2025-09-09 07:14:35,291 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Height (mm) (Centre of Handle Lever)' (Q95): visible=False 
2025-09-09 07:14:35,292 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 - Lock Type (Centre of Handle Lever)' (Q96): visible=False 
2025-09-09 07:14:35,292 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Colour' (Q97): visible=False 
2025-09-09 07:14:35,293 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Cylinder' (Q98): visible=False 
2025-09-09 07:14:35,293 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Height Location' (Q99): visible=False 
2025-09-09 07:14:35,293 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Height (mm) (Top of Cut Out)' (Q100): visible=False 
2025-09-09 07:14:35,294 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Type (Top of Cut Out)' (Q101): visible=False 
2025-09-09 07:14:35,294 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Height (mm) (Centre of Cut Out)' (Q102): visible=False 
2025-09-09 07:14:35,295 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Type (Centre of Cut Out)' (Q103): visible=False 
2025-09-09 07:14:35,295 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Height (mm) (Bottom of Cut Out)' (Q104): visible=False 
2025-09-09 07:14:35,295 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Type (Bottom of Cut Out)' (Q105): visible=False 
2025-09-09 07:14:35,296 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Height (mm) (Centre of Handle Lever)' (Q106): visible=False 
2025-09-09 07:14:35,296 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 - Lock Type (Centre of Handle Lever)' (Q107): visible=False 
2025-09-09 07:14:35,296 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Colour' (Q108): visible=False 
2025-09-09 07:14:35,297 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Cylinder' (Q109): visible=False 
2025-09-09 07:14:35,297 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Height Location' (Q110): visible=False 
2025-09-09 07:14:35,297 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Height (mm) (Top of Cut Out)' (Q111): visible=False 
2025-09-09 07:14:35,298 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Type (Top of Cut Out)' (Q112): visible=False 
2025-09-09 07:14:35,298 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Height (mm) (Centre of Cut Out)' (Q113): visible=False 
2025-09-09 07:14:35,299 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Type (Centre of Cut Out)' (Q114): visible=False 
2025-09-09 07:14:35,299 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Height (mm) (Bottom of Cut Out)' (Q115): visible=False 
2025-09-09 07:14:35,299 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Type (Bottom of Cut Out)' (Q116): visible=False 
2025-09-09 07:14:35,300 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Height (mm) (Centre of Handle Lever)' (Q117): visible=False 
2025-09-09 07:14:35,300 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco Tasman ESCAPE - Lock Type (Centre of Handle Lever)' (Q118): visible=False 
2025-09-09 07:14:35,300 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Cylinder' (Q119): visible=False 
2025-09-09 07:14:35,301 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Height Location' (Q120): visible=False 
2025-09-09 07:14:35,301 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)' (Q121): visible=False 
2025-09-09 07:14:35,301 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Type (Top of Cut Out)' (Q122): visible=False 
2025-09-09 07:14:35,302 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)' (Q123): visible=False 
2025-09-09 07:14:35,302 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Type (Centre of Cut Out)' (Q124): visible=False 
2025-09-09 07:14:35,303 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)' (Q125): visible=False 
2025-09-09 07:14:35,303 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Type (Bottom of Cut Out)' (Q126): visible=False 
2025-09-09 07:14:35,303 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)' (Q127): visible=False 
2025-09-09 07:14:35,304 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Austral Elegance XC CUT OUT ONLY - Lock Type (Centre of Handle Lever)' (Q128): visible=False 
2025-09-09 07:14:35,304 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Cylinder' (Q129): visible=False 
2025-09-09 07:14:35,305 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Height Location' (Q130): visible=False 
2025-09-09 07:14:35,305 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)' (Q131): visible=False 
2025-09-09 07:14:35,305 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Type (Top of Cut Out)' (Q132): visible=False 
2025-09-09 07:14:35,306 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)' (Q133): visible=False 
2025-09-09 07:14:35,306 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Type (Bottom of Cut Out)' (Q134): visible=False 
2025-09-09 07:14:35,306 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)' (Q135): visible=False 
2025-09-09 07:14:35,307 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Type (Centre of Cut Out)' (Q136): visible=False 
2025-09-09 07:14:35,307 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)' (Q137): visible=False 
2025-09-09 07:14:35,308 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged CUT OUT ONLY - Lock Type (Centre of Handle Lever)' (Q138): visible=False 
2025-09-09 07:14:35,308 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Cylinder' (Q139): visible=False 
2025-09-09 07:14:35,308 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Height Location' (Q140): visible=False 
2025-09-09 07:14:35,309 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)' (Q141): visible=False 
2025-09-09 07:14:35,309 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Type (Top of Cut Out)' (Q142): visible=False 
2025-09-09 07:14:35,309 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)' (Q143): visible=False 
2025-09-09 07:14:35,310 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Type (Centre of Cut Out)' (Q144): visible=False 
2025-09-09 07:14:35,310 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)' (Q145): visible=False 
2025-09-09 07:14:35,310 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Type (Bottom of Cut Out)' (Q146): visible=False 
2025-09-09 07:14:35,311 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)' (Q147): visible=False 
2025-09-09 07:14:35,311 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lockwood 8654 CUT OUT ONLY - Lock Type (Centre of Handle Lever)' (Q148): visible=False 
2025-09-09 07:14:35,312 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Cylinder' (Q149): visible=False 
2025-09-09 07:14:35,312 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Height Location' (Q150): visible=False 
2025-09-09 07:14:35,312 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Top of Cut Out)' (Q151): visible=False 
2025-09-09 07:14:35,313 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Type (Top of Cut Out)' (Q152): visible=False 
2025-09-09 07:14:35,313 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Centre of Cut Out)' (Q153): visible=False 
2025-09-09 07:14:35,314 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Type (Centre of Cut Out)' (Q154): visible=False 
2025-09-09 07:14:35,315 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Bottom of Cut Out)' (Q155): visible=False 
2025-09-09 07:14:35,315 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Type (Bottom of Cut Out)' (Q156): visible=False 
2025-09-09 07:14:35,315 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Height (mm) (Centre of Handle Lever)' (Q157): visible=False 
2025-09-09 07:14:35,316 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Whitco MK2 CUT OUT ONLY - Lock Type (Centre of Handle Lever)' (Q158): visible=False 
2025-09-09 07:14:35,316 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lock or Cut Out Side - OUTSIDE VIEW (Double Door)' (Q159): visible=True 
2025-09-09 07:14:35,316 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lock or Cut Out Side - OUTSIDE VIEW (Double Door)' has 0 operation mappings 
2025-09-09 07:14:35,316 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Non-Lock Door Striker Cut Outs' (Q160): visible=True 
2025-09-09 07:14:35,316 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Non-Lock Door Striker Cut Outs' has 0 operation mappings 
2025-09-09 07:14:35,316 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Flush Bolts & Patio Bolts (Non-Lock Door)' (Q161): visible=True 
2025-09-09 07:14:35,316 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Flush Bolts & Patio Bolts (Non-Lock Door)' has 0 operation mappings 
2025-09-09 07:14:35,317 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Patio Bolt Colour (Non-Lock Door)' (Q162): visible=False 
2025-09-09 07:14:35,317 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail (Conditional Case 1)' (Q163): visible=False 
2025-09-09 07:14:35,318 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 1)' (Q164): visible=True 
2025-09-09 07:14:35,318 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 1)' has 0 operation mappings 
2025-09-09 07:14:35,319 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Standard Frame, Conditional Case 1)' (Q165): visible=True 
2025-09-09 07:14:35,319 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Standard Frame, Conditional Case 1)' has 0 operation mappings 
2025-09-09 07:14:35,319 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Special Frame, Conditional Case 1)' (Q166): visible=False 
2025-09-09 07:14:35,320 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail (Conditional Case 2)' (Q167): visible=True 
2025-09-09 07:14:35,320 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail (Conditional Case 2)' has 0 operation mappings 
2025-09-09 07:14:35,320 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 2)' (Q168): visible=True 
2025-09-09 07:14:35,321 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 2)' has 0 operation mappings 
2025-09-09 07:14:35,321 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Standard Frame, Conditional Case 2)' (Q169): visible=True 
2025-09-09 07:14:35,321 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Standard Frame, Conditional Case 2)' has 0 operation mappings 
2025-09-09 07:14:35,321 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Special Frame, Conditional Case 2)' (Q170): visible=False 
2025-09-09 07:14:35,322 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Number of Security Hinges (Pick Up)' (Q171): visible=True 
2025-09-09 07:14:35,322 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Number of Security Hinges (Pick Up)' has 2 operation mappings 
2025-09-09 07:14:35,322 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Load Door (Delivery) / Customer Collect (Pick Up)' for field 'Number of Security Hinges (Pick Up)' 
2025-09-09 07:14:35,323 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.0330622782, Cost: 2.6449822560000005 
2025-09-09 07:14:35,323 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #17: Load Door (Delivery) / Customer Collect (Pick Up) = $2.6449822560000005 
2025-09-09 07:14:35,323 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Delivery Time - Time allocated to CD1 even for pick up orders' for field 'Number of Security Hinges (Pick Up)' 
2025-09-09 07:14:35,325 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.07, Cost: 5.6000000000000005 
2025-09-09 07:14:35,325 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added field mapping operation #18: Delivery Time - Time allocated to CD1 even for pick up orders = $5.6000000000000005 
2025-09-09 07:14:35,326 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Number of Security Hinges (Deliver/Site)' (Q172): visible=True 
2025-09-09 07:14:35,326 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Number of Security Hinges (Deliver/Site)' has 0 operation mappings 
2025-09-09 07:14:35,326 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (2 Hinges, Pick Up, Double Door)' (Q173): visible=False 
2025-09-09 07:14:35,326 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Bottom Hinge (mm) (2 Hinges, Pick Up, Double Door)' (Q174): visible=False 
2025-09-09 07:14:35,327 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Top Hinge (mm) (2 Hinges, Pick Up, Double Door)' (Q175): visible=False 
2025-09-09 07:14:35,327 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (3 Hinges, Pick Up, Double Door)' (Q176): visible=False 
2025-09-09 07:14:35,328 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Bottom Hinge (mm) (3 Hinges, Pick Up, Double Door)' (Q177): visible=False 
2025-09-09 07:14:35,328 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Middle Hinge (mm) (3 Hinges, Pick Up, Double Door)' (Q178): visible=False 
2025-09-09 07:14:35,328 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Top Hinge (mm) (3 Hinges, Pick Up, Double Door)' (Q179): visible=False 
2025-09-09 07:14:35,329 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (4 Hinges, Pick Up, Double Door)' (Q180): visible=False 
2025-09-09 07:14:35,329 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Bottom Hinge (mm) (4 Hinges, Pick Up, Double Door)' (Q181): visible=False 
2025-09-09 07:14:35,330 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of 2nd Hinge (mm) (4 Hinges, Pick Up, Double Door)' (Q182): visible=False 
2025-09-09 07:14:35,330 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of 3rd Hinge (mm) (4 Hinges, Pick Up, Double Door)' (Q183): visible=False 
2025-09-09 07:14:35,330 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Top Hinge (mm) (4 Hinges, Pick Up, Double Door)' (Q184): visible=False 
2025-09-09 07:14:35,331 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (2 Hinges, Deliver/Site, Double Door)' (Q185): visible=False 
2025-09-09 07:14:35,331 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Bottom Hinge (mm) (2 Hinges, Deliver/Site, Double Door)' (Q186): visible=False 
2025-09-09 07:14:35,332 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Top Hinge (mm) (2 Hinges, Deliver/Site, Double Door)' (Q187): visible=False 
2025-09-09 07:14:35,332 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (3 Hinges, Deliver/Site, Double Door)' (Q188): visible=True 
2025-09-09 07:14:35,332 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (3 Hinges, Deliver/Site, Double Door)' has 1 operation mappings 
2025-09-09 07:14:35,332 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Specific Hinge Locations' for field 'Specific Hinge Locations? (3 Hinges, Deliver/Site, Double Door)' 
2025-09-09 07:14:35,332 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition '(bx_dbl_hinge_num_sec_hinges_deliver in ['3_per_door_loose_drilled'] and bx_dbl_hinge_spec_hinge_loc_3_deliver_dd == 'yes')' result: False 
2025-09-09 07:14:35,333 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation 'Specific Hinge Locations' condition failed, skipping 
2025-09-09 07:14:35,333 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Bottom Hinge (mm) (3 Hinges, Deliver/Site, Double Door)' (Q189): visible=False 
2025-09-09 07:14:35,333 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Middle Hinge (mm) (3 Hinges, Deliver/Site, Double Door)' (Q190): visible=False 
2025-09-09 07:14:35,334 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Top Hinge (mm) (3 Hinges, Deliver/Site, Double Door)' (Q191): visible=False 
2025-09-09 07:14:35,334 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (4 Hinges, Deliver/Site, Double Door)' (Q192): visible=False 
2025-09-09 07:14:35,335 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Bottom Hinge (mm) (4 Hinges, Deliver/Site, Double Door)' (Q193): visible=False 
2025-09-09 07:14:35,336 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of 2nd Hinge (mm) (4 Hinges, Deliver/Site, Double Door)' (Q194): visible=False 
2025-09-09 07:14:35,337 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of 3rd Hinge (mm) (4 Hinges, Deliver/Site, Double Door)' (Q195): visible=False 
2025-09-09 07:14:35,337 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bottom of Door to Centre of Top Hinge (mm) (4 Hinges, Deliver/Site, Double Door)' (Q196): visible=False 
2025-09-09 07:14:35,338 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Type' (Q197): visible=True 
2025-09-09 07:14:35,338 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Type' has 0 operation mappings 
2025-09-09 07:14:35,338 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Standard Hinge)' (Q198): visible=True 
2025-09-09 07:14:35,339 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Standard Hinge)' has 0 operation mappings 
2025-09-09 07:14:35,339 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Safety Prong)' (Q199): visible=True 
2025-09-09 07:14:35,340 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Safety Prong)' has 0 operation mappings 
2025-09-09 07:14:35,340 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Step Hinge)' (Q200): visible=True 
2025-09-09 07:14:35,340 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Step Hinge)' has 0 operation mappings 
2025-09-09 07:14:35,341 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing section: Additional Hardware (9 fields) 
2025-09-09 07:14:35,341 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Packers Quantity' (Q201): visible=True 
2025-09-09 07:14:35,341 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Packers Quantity' has 3 operation mappings 
2025-09-09 07:14:35,341 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Pick Hardware (Security Hinge Packers)' for field 'Security Hinge Packers Quantity' 
2025-09-09 07:14:35,342 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:35,344 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:35,345 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.0, Cost: 0.0 
2025-09-09 07:14:35,345 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Skipped field mapping operation Pick Hardware (Security Hinge Packers): duration = 0 
2025-09-09 07:14:35,345 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Wrap Picked Hardware (Security Hinge Packers)' for field 'Security Hinge Packers Quantity' 
2025-09-09 07:14:35,346 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:35,347 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:35,348 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.0, Cost: 0.0 
2025-09-09 07:14:35,348 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Skipped field mapping operation Wrap Picked Hardware (Security Hinge Packers): duration = 0 
2025-09-09 07:14:35,348 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'QC Picked Hardware (Security Hinge Packers)' for field 'Security Hinge Packers Quantity' 
2025-09-09 07:14:35,349 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:35,350 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:35,351 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.0, Cost: 0.0 
2025-09-09 07:14:35,351 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Skipped field mapping operation QC Picked Hardware (Security Hinge Packers): duration = 0 
2025-09-09 07:14:35,352 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Closer' (Q202): visible=True 
2025-09-09 07:14:35,352 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Closer' has 0 operation mappings 
2025-09-09 07:14:35,352 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Closer Colour (Austral)' (Q203): visible=False 
2025-09-09 07:14:35,353 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Closer Colour (Whitco)' (Q204): visible=False 
2025-09-09 07:14:35,353 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Pet Door' (Q205): visible=True 
2025-09-09 07:14:35,353 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Pet Door' has 2 operation mappings 
2025-09-09 07:14:35,353 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Pick Hardware' for field 'Pet Door' 
2025-09-09 07:14:35,354 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition 'bx_dbl_hinge_pet_door in ['small_pet_door', 'medium_pet_door', 'large_pet_door']' result: False 
2025-09-09 07:14:35,354 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation 'Pick Hardware' condition failed, skipping 
2025-09-09 07:14:35,354 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing field operation mapping: 'Pet Door Installed in SaltWaterSeries / Xceed / BasiX / Flat Plate' for field 'Pet Door' 
2025-09-09 07:14:35,354 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Condition 'bx_dbl_hinge_pet_door in ['small_pet_door', 'medium_pet_door', 'large_pet_door']' result: False 
2025-09-09 07:14:35,355 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ❌ Field operation 'Pet Door Installed in SaltWaterSeries / Xceed / BasiX / Flat Plate' condition failed, skipping 
2025-09-09 07:14:35,355 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Pet Door Colour' (Q206): visible=False 
2025-09-09 07:14:35,355 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Pet Door Location' (Q207): visible=False 
2025-09-09 07:14:35,356 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Pet Door Flexible Flap' (Q208): visible=False 
2025-09-09 07:14:35,356 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Pet Door Surround Infill' (Q209): visible=False 
2025-09-09 07:14:35,357 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing section: Additional Extrusions (20 fields) 
2025-09-09 07:14:35,357 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bug Seal Size' (Q210): visible=True 
2025-09-09 07:14:35,357 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bug Seal Size' has 0 operation mappings 
2025-09-09 07:14:35,357 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Left Bug Seal Length (mm)' (Q211): visible=False 
2025-09-09 07:14:35,358 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Right Bug Seal Length (mm)' (Q212): visible=False 
2025-09-09 07:14:35,358 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Stop Bead Colour' (Q213): visible=True 
2025-09-09 07:14:35,358 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Stop Bead Colour' has 0 operation mappings 
2025-09-09 07:14:35,359 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Stop Bead LEFT Length (mm)' (Q214): visible=False 
2025-09-09 07:14:35,359 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Stop Bead TOP Length (mm)' (Q215): visible=False 
2025-09-09 07:14:35,360 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Stop Bead RIGHT Length (mm)' (Q216): visible=False 
2025-09-09 07:14:35,360 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Adhesive Mohair' (Q217): visible=True 
2025-09-09 07:14:35,361 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Adhesive Mohair' has 0 operation mappings 
2025-09-09 07:14:35,361 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Adhesive Mohair Length (mm)' (Q218): visible=False 
2025-09-09 07:14:35,362 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor Type (Case 1: No Stop Bead/Mohair)' (Q219): visible=True 
2025-09-09 07:14:35,362 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor Type (Case 1: No Stop Bead/Mohair)' has 0 operation mappings 
2025-09-09 07:14:35,362 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor Type (Case 2: Short Leg Required)' (Q220): visible=False 
2025-09-09 07:14:35,362 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor Type (Case 3: General Jamb Adaptor Required)' (Q221): visible=True 
2025-09-09 07:14:35,363 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor Type (Case 3: General Jamb Adaptor Required)' has 0 operation mappings 
2025-09-09 07:14:35,363 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Opening LEFT Height (mm) - SWING VIEW (Jamb Adaptor)' (Q222): visible=False 
2025-09-09 07:14:35,364 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Height Addition (mm) (Jamb Adaptor)' (Q223): visible=False 
2025-09-09 07:14:35,364 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor LEFT Length (mm)' (Q224): visible=False 
2025-09-09 07:14:35,365 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Opening TOP Width (mm) (Jamb Adaptor)' (Q225): visible=False 
2025-09-09 07:14:35,365 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Width Addition (mm) (Jamb Adaptor)' (Q226): visible=False 
2025-09-09 07:14:35,366 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor TOP Length (mm)' (Q227): visible=False 
2025-09-09 07:14:35,367 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Opening RIGHT Height (mm) - SWING VIEW (Jamb Adaptor)' (Q228): visible=False 
2025-09-09 07:14:35,367 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor RIGHT Length (mm)' (Q229): visible=False 
2025-09-09 07:14:35,368 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ========== PROCESSING OPTION OPERATIONS ========== 
2025-09-09 07:14:35,368 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Location' value:  
2025-09-09 07:14:35,383 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Location' 
2025-09-09 07:14:35,384 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Door Jamb Reveal is Greater than 20mm or No Reveal?' value:  
2025-09-09 07:14:35,385 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Door Jamb Reveal is Greater than 20mm or No Reveal?' 
2025-09-09 07:14:35,385 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Will New Screen Door Clear Handle on Existing External Door?' value:  
2025-09-09 07:14:35,385 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Will New Screen Door Clear Handle on Existing External Door?' 
2025-09-09 07:14:35,385 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Will Handle on New Screen Door Clear Existing External Door or Existing Pull/Push Bar Handle?' value:  
2025-09-09 07:14:35,385 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Will Handle on New Screen Door Clear Existing External Door or Existing Pull/Push Bar Handle?' 
2025-09-09 07:14:35,386 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'New Screen Door Swing Path is Clear from any Obstructions?' value:  
2025-09-09 07:14:35,386 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'New Screen Door Swing Path is Clear from any Obstructions?' 
2025-09-09 07:14:35,386 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Quantity' value: 1 
2025-09-09 07:14:35,387 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Quantity' 
2025-09-09 07:14:35,387 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Frame Colour' value: black_custom_matt_gn248a 
2025-09-09 07:14:35,387 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Frame Colour' 
2025-09-09 07:14:35,387 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Black Custom Matt GN248A' (value: black_custom_matt_gn248a) 
2025-09-09 07:14:35,388 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Black Custom Matt GN248A' has 0 operation mappings 
2025-09-09 07:14:35,389 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Door Size Deduction Assistance Required?' value: no 
2025-09-09 07:14:35,390 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Door Size Deduction Assistance Required?' 
2025-09-09 07:14:35,390 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,391 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,395 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door Height (mm) (Manual)' value: 2110 
2025-09-09 07:14:35,396 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE LEFT Door Height (mm) (Manual)' 
2025-09-09 07:14:35,396 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door TOP Width (mm) (Manual)' value: 860 
2025-09-09 07:14:35,397 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE LEFT Door TOP Width (mm) (Manual)' 
2025-09-09 07:14:35,397 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door MIDDLE Width (mm) (Manual)' value: 860 
2025-09-09 07:14:35,397 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE LEFT Door MIDDLE Width (mm) (Manual)' 
2025-09-09 07:14:35,398 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE LEFT Door BOTTOM Width (mm) (Manual)' value: 860 
2025-09-09 07:14:35,399 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE LEFT Door BOTTOM Width (mm) (Manual)' 
2025-09-09 07:14:35,399 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door Height (mm) (Manual)' value: 2110 
2025-09-09 07:14:35,399 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE RIGHT Door Height (mm) (Manual)' 
2025-09-09 07:14:35,399 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door TOP Width (mm) (Manual)' value: 860 
2025-09-09 07:14:35,399 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE RIGHT Door TOP Width (mm) (Manual)' 
2025-09-09 07:14:35,399 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door MIDDLE Width (mm) (Manual)' value: 860 
2025-09-09 07:14:35,400 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE RIGHT Door MIDDLE Width (mm) (Manual)' 
2025-09-09 07:14:35,400 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'MAKE RIGHT Door BOTTOM Width (mm) (Manual)' value: 860 
2025-09-09 07:14:35,400 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'MAKE RIGHT Door BOTTOM Width (mm) (Manual)' 
2025-09-09 07:14:35,400 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Swing' value: outswing 
2025-09-09 07:14:35,400 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Swing' 
2025-09-09 07:14:35,400 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Outswing' (value: outswing) 
2025-09-09 07:14:35,402 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Outswing' has 0 operation mappings 
2025-09-09 07:14:35,402 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' value: yes 
2025-09-09 07:14:35,402 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,403 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Yes' (value: yes) 
2025-09-09 07:14:35,406 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Yes' has 8 operation mappings 
2025-09-09 07:14:35,407 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Cut Extrusions on Upcut Saw' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,409 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.025, Cost: 0.0 
2025-09-09 07:14:35,409 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #1: Cut Extrusions on Upcut Saw = $0.0 
2025-09-09 07:14:35,410 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Load / Unload Extrusion & Set Up 2' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,410 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 0.0 
2025-09-09 07:14:35,411 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #2: CNC Load / Unload Extrusion & Set Up 2 = $0.0 
2025-09-09 07:14:35,411 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Extra Time to Cut French Door Mullion' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,412 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.025, Cost: 0.0 
2025-09-09 07:14:35,412 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #3: Extra Time to Cut French Door Mullion = $0.0 
2025-09-09 07:14:35,412 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,413 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.018, Cost: 0.0 
2025-09-09 07:14:35,413 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #4: CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion = $0.0 
2025-09-09 07:14:35,414 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,415 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.021, Cost: 0.0 
2025-09-09 07:14:35,416 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #5: Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion = $0.0 
2025-09-09 07:14:35,416 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,418 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.028, Cost: 0.0 
2025-09-09 07:14:35,418 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #6: Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion = $0.0 
2025-09-09 07:14:35,418 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Insert Top & Bottom French Door Mullion Caps' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,419 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.056, Cost: 0.0 
2025-09-09 07:14:35,420 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #7: Insert Top & Bottom French Door Mullion Caps = $0.0 
2025-09-09 07:14:35,420 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' for option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' 
2025-09-09 07:14:35,421 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.137, Cost: 0.0 
2025-09-09 07:14:35,421 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #8: Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door = $0.0 
2025-09-09 07:14:35,422 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lock Brand' value: commandex_hinged 
2025-09-09 07:14:35,422 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Lock Brand' 
2025-09-09 07:14:35,422 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'CommandeX Hinged' (value: commandex_hinged) 
2025-09-09 07:14:35,422 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'CommandeX Hinged' has 0 operation mappings 
2025-09-09 07:14:35,424 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Colour' value: black 
2025-09-09 07:14:35,424 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'CommandeX Hinged - Lock Colour' 
2025-09-09 07:14:35,424 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Black' (value: black) 
2025-09-09 07:14:35,425 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Black' has 0 operation mappings 
2025-09-09 07:14:35,425 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Cylinder' value: commandex_5_pin 
2025-09-09 07:14:35,426 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'CommandeX Hinged - Cylinder' 
2025-09-09 07:14:35,426 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'CommandeX 5 Pin' (value: commandex_5_pin) 
2025-09-09 07:14:35,426 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'CommandeX 5 Pin' has 3 operation mappings 
2025-09-09 07:14:35,427 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Pick Hardware' for option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' 
2025-09-09 07:14:35,428 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.017, Cost: 1.36 
2025-09-09 07:14:35,428 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #9: Pick Hardware = $1.36 
2025-09-09 07:14:35,429 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'QC Picked Hardware (Cylinder)' for option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' 
2025-09-09 07:14:35,431 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.005, Cost: 0.4 
2025-09-09 07:14:35,431 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #10: QC Picked Hardware (Cylinder) = $0.4 
2025-09-09 07:14:35,431 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Wrap Hardware (Cylinder)' for option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' 
2025-09-09 07:14:35,432 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.005, Cost: 0.4 
2025-09-09 07:14:35,432 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #11: Wrap Hardware (Cylinder) = $0.4 
2025-09-09 07:14:35,433 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height Location' value: top_of_cut_out 
2025-09-09 07:14:35,433 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'CommandeX Hinged - Lock Height Location' 
2025-09-09 07:14:35,433 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Top of Cut Out' (value: top_of_cut_out) 
2025-09-09 07:14:35,434 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Top of Cut Out' has 0 operation mappings 
2025-09-09 07:14:35,434 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Height (mm) (Top of Cut Out)' value:  
2025-09-09 07:14:35,435 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'CommandeX Hinged - Lock Height (mm) (Top of Cut Out)' 
2025-09-09 07:14:35,435 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'CommandeX Hinged - Lock Type (Top of Cut Out)' value: single 
2025-09-09 07:14:35,435 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'CommandeX Hinged - Lock Type (Top of Cut Out)' 
2025-09-09 07:14:35,435 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Single' (value: single) 
2025-09-09 07:14:35,436 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Single' has 0 operation mappings 
2025-09-09 07:14:35,445 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Lock or Cut Out Side - OUTSIDE VIEW (Double Door)' value: left_hand_door_right_hand_lock 
2025-09-09 07:14:35,445 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Lock or Cut Out Side - OUTSIDE VIEW (Double Door)' 
2025-09-09 07:14:35,445 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Left Hand Door, Right Hand Lock' (value: left_hand_door_right_hand_lock) 
2025-09-09 07:14:35,446 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Left Hand Door, Right Hand Lock' has 0 operation mappings 
2025-09-09 07:14:35,446 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Non-Lock Door Striker Cut Outs' value: yes 
2025-09-09 07:14:35,446 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Non-Lock Door Striker Cut Outs' 
2025-09-09 07:14:35,446 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Yes' (value: yes) 
2025-09-09 07:14:35,447 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Yes' has 2 operation mappings 
2025-09-09 07:14:35,448 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Non-Lock Door Centre Striker Cut Out' for option 'Yes' in field 'Non-Lock Door Striker Cut Outs' 
2025-09-09 07:14:35,451 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.017, Cost: 1.36 
2025-09-09 07:14:35,451 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #12: CNC Non-Lock Door Centre Striker Cut Out = $1.36 
2025-09-09 07:14:35,452 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Load / Unload Extrusion & Set Up' for option 'Yes' in field 'Non-Lock Door Striker Cut Outs' 
2025-09-09 07:14:35,453 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,453 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #13: CNC Load / Unload Extrusion & Set Up = $2.64 
2025-09-09 07:14:35,453 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Flush Bolts & Patio Bolts (Non-Lock Door)' value: top_bottom_flush_bolts 
2025-09-09 07:14:35,453 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,454 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Top & Bottom Flush Bolts' (value: top_bottom_flush_bolts) 
2025-09-09 07:14:35,454 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Top & Bottom Flush Bolts' has 7 operation mappings 
2025-09-09 07:14:35,455 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Flush Bolt Cut Out' for option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,458 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,458 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #14: CNC Flush Bolt Cut Out = $2.64 
2025-09-09 07:14:35,458 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Rivet Flush Bolts into Non-Lock Door' for option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,459 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-09 07:14:35,463 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-09 07:14:35,465 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.117, Cost: 18.720000000000002 
2025-09-09 07:14:35,465 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #15: Rivet Flush Bolts into Non-Lock Door = $18.720000000000002 
2025-09-09 07:14:35,465 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Load / Unload Extrusion & Set Up' for option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,467 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,467 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #16: CNC Load / Unload Extrusion & Set Up = $2.64 
2025-09-09 07:14:35,467 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Load / Unload Extrusion & Set Up' for option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,468 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,468 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #17: CNC Load / Unload Extrusion & Set Up = $2.64 
2025-09-09 07:14:35,468 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Flush Bolt Hole in Top or Btm' for option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,470 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.017, Cost: 1.36 
2025-09-09 07:14:35,471 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #18: CNC Flush Bolt Hole in Top or Btm = $1.36 
2025-09-09 07:14:35,471 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Flush Bolt Hole in Top or Btm' for option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,472 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.017, Cost: 1.36 
2025-09-09 07:14:35,472 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #19: CNC Flush Bolt Hole in Top or Btm = $1.36 
2025-09-09 07:14:35,472 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Flush Bolt Cut Out' for option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' 
2025-09-09 07:14:35,474 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,474 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #20: CNC Flush Bolt Cut Out = $2.64 
2025-09-09 07:14:35,475 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 1)' value:  
2025-09-09 07:14:35,475 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 1)' 
2025-09-09 07:14:35,476 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Standard Frame, Conditional Case 1)' value:  
2025-09-09 07:14:35,476 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Midrail Colour (Standard Frame, Conditional Case 1)' 
2025-09-09 07:14:35,477 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail (Conditional Case 2)' value: yes 
2025-09-09 07:14:35,477 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Midrail (Conditional Case 2)' 
2025-09-09 07:14:35,477 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Yes' (value: yes) 
2025-09-09 07:14:35,478 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Yes' has 6 operation mappings 
2025-09-09 07:14:35,478 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' for option 'Yes' in field 'Midrail (Conditional Case 2)' 
2025-09-09 07:14:35,482 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.142, Cost: 11.36 
2025-09-09 07:14:35,484 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #21: Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion = $11.36 
2025-09-09 07:14:35,485 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Cut Extrusions on Upcut Saw (Midrail)' for option 'Yes' in field 'Midrail (Conditional Case 2)' 
2025-09-09 07:14:35,490 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.05, Cost: 4.0 
2025-09-09 07:14:35,490 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #22: Cut Extrusions on Upcut Saw (Midrail) = $4.0 
2025-09-09 07:14:35,490 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Cut Plugh for CommandeX Midrail' for option 'Yes' in field 'Midrail (Conditional Case 2)' 
2025-09-09 07:14:35,493 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.034, Cost: 2.72 
2025-09-09 07:14:35,493 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #23: Cut Plugh for CommandeX Midrail = $2.72 
2025-09-09 07:14:35,494 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Insert Plugh into CommandeX Midrail' for option 'Yes' in field 'Midrail (Conditional Case 2)' 
2025-09-09 07:14:35,496 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.042, Cost: 3.3600000000000003 
2025-09-09 07:14:35,496 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #24: Insert Plugh into CommandeX Midrail = $3.3600000000000003 
2025-09-09 07:14:35,496 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Extra Time to Assemble Product with CommandeX Midrail' for option 'Yes' in field 'Midrail (Conditional Case 2)' 
2025-09-09 07:14:35,498 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.158, Cost: 12.64 
2025-09-09 07:14:35,498 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #25: Extra Time to Assemble Product with CommandeX Midrail = $12.64 
2025-09-09 07:14:35,498 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Plugh CommandeX Midrail by Hand' for option 'Yes' in field 'Midrail (Conditional Case 2)' 
2025-09-09 07:14:35,500 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.132, Cost: 10.56 
2025-09-09 07:14:35,501 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #26: Plugh CommandeX Midrail by Hand = $10.56 
2025-09-09 07:14:35,501 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 2)' value:  
2025-09-09 07:14:35,501 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Midrail Height (mm) - TOP OF MIDRAIL (Conditional Case 2)' 
2025-09-09 07:14:35,502 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Midrail Colour (Standard Frame, Conditional Case 2)' value: match_door_frame 
2025-09-09 07:14:35,502 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Midrail Colour (Standard Frame, Conditional Case 2)' 
2025-09-09 07:14:35,502 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Match Door Frame (Powder Coat Fee)' (value: match_door_frame) 
2025-09-09 07:14:35,503 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Match Door Frame (Powder Coat Fee)' has 2 operation mappings 
2025-09-09 07:14:35,504 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'QC Mill Finish Extrusions for Powder Coat or QC Buy A Length Extrusions Received' for option 'Match Door Frame (Powder Coat Fee)' in field 'Midrail Colour (Standard Frame, Conditional Case 2)' 
2025-09-09 07:14:35,506 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.034, Cost: 2.72 
2025-09-09 07:14:35,507 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #27: QC Mill Finish Extrusions for Powder Coat or QC Buy A Length Extrusions Received = $2.72 
2025-09-09 07:14:35,507 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Extra Time to Cut Powder Coat Extrusions' for option 'Match Door Frame (Powder Coat Fee)' in field 'Midrail Colour (Standard Frame, Conditional Case 2)' 
2025-09-09 07:14:35,509 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.066, Cost: 5.28 
2025-09-09 07:14:35,509 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #28: Extra Time to Cut Powder Coat Extrusions = $5.28 
2025-09-09 07:14:35,510 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Number of Security Hinges (Pick Up)' value: 0 
2025-09-09 07:14:35,510 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Number of Security Hinges (Pick Up)' 
2025-09-09 07:14:35,510 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: '0' (value: 0) 
2025-09-09 07:14:35,510 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option '0' has 0 operation mappings 
2025-09-09 07:14:35,511 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Number of Security Hinges (Deliver/Site)' value: 3_per_door_loose_drilled 
2025-09-09 07:14:35,511 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,511 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: '3 per Door - Loose with holes drilled (No rivets supplied)' (value: 3_per_door_loose_drilled) 
2025-09-09 07:14:35,511 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option '3 per Door - Loose with holes drilled (No rivets supplied)' has 7 operation mappings 
2025-09-09 07:14:35,512 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Hinge Holes (Lock Door)' for option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,513 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,514 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,515 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,516 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,516 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.012, Cost: 0.96 
2025-09-09 07:14:35,517 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #29: CNC Hinge Holes (Lock Door) = $0.96 
2025-09-09 07:14:35,517 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Hinge Holes (Non-Lock Door)' for option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,518 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,518 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,519 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,519 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,521 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.012, Cost: 0.96 
2025-09-09 07:14:35,521 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #30: CNC Hinge Holes (Non-Lock Door) = $0.96 
2025-09-09 07:14:35,521 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Pick Hinges' for option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,521 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,522 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,523 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,524 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,525 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.03, Cost: 2.4 
2025-09-09 07:14:35,525 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #31: Pick Hinges = $2.4 
2025-09-09 07:14:35,525 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'QC Picked Hardware (x Qty of Hinges)' for option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,526 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,526 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,527 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,528 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,529 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.03, Cost: 2.4 
2025-09-09 07:14:35,529 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #32: QC Picked Hardware (x Qty of Hinges) = $2.4 
2025-09-09 07:14:35,529 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'Wrap Hardware (x Qty of Hinges)' for option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,530 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,530 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,531 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:35,532 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:35,533 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.03, Cost: 2.4 
2025-09-09 07:14:35,533 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #33: Wrap Hardware (x Qty of Hinges) = $2.4 
2025-09-09 07:14:35,533 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Load / Unload Extrusion & Set Up' for option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,535 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,535 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #34: CNC Load / Unload Extrusion & Set Up = $2.64 
2025-09-09 07:14:35,535 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option operation mapping: 'CNC Load / Unload Extrusion & Set Up' for option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' 
2025-09-09 07:14:35,537 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Template calculation - Duration: 0.033, Cost: 2.64 
2025-09-09 07:14:35,537 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ✅ Added option mapping operation #35: CNC Load / Unload Extrusion & Set Up = $2.64 
2025-09-09 07:14:35,539 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Specific Hinge Locations? (3 Hinges, Deliver/Site, Double Door)' value: no 
2025-09-09 07:14:35,540 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Specific Hinge Locations? (3 Hinges, Deliver/Site, Double Door)' 
2025-09-09 07:14:35,540 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,540 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,542 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Type' value: security_hinge 
2025-09-09 07:14:35,542 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Security Hinge Type' 
2025-09-09 07:14:35,542 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Security Hinge' (value: security_hinge) 
2025-09-09 07:14:35,543 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Security Hinge' has 0 operation mappings 
2025-09-09 07:14:35,543 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Standard Hinge)' value: black 
2025-09-09 07:14:35,543 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Security Hinge Colour (Standard Hinge)' 
2025-09-09 07:14:35,543 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Black' (value: black) 
2025-09-09 07:14:35,544 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Black' has 0 operation mappings 
2025-09-09 07:14:35,544 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Safety Prong)' value: black 
2025-09-09 07:14:35,544 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Security Hinge Colour (Safety Prong)' 
2025-09-09 07:14:35,545 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Black' (value: black) 
2025-09-09 07:14:35,545 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Black' has 0 operation mappings 
2025-09-09 07:14:35,546 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Colour (Step Hinge)' value: black 
2025-09-09 07:14:35,546 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Security Hinge Colour (Step Hinge)' 
2025-09-09 07:14:35,546 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'Black' (value: black) 
2025-09-09 07:14:35,546 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'Black' has 0 operation mappings 
2025-09-09 07:14:35,546 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Security Hinge Packers Quantity' value: 0 
2025-09-09 07:14:35,547 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Security Hinge Packers Quantity' 
2025-09-09 07:14:35,547 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Closer' value: no 
2025-09-09 07:14:35,547 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Closer' 
2025-09-09 07:14:35,547 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,547 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,548 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Pet Door' value: no 
2025-09-09 07:14:35,548 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Pet Door' 
2025-09-09 07:14:35,548 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,549 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,549 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Bug Seal Size' value: no 
2025-09-09 07:14:35,550 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Bug Seal Size' 
2025-09-09 07:14:35,550 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,550 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,551 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Stop Bead Colour' value: no 
2025-09-09 07:14:35,551 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Stop Bead Colour' 
2025-09-09 07:14:35,551 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,552 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,553 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Adhesive Mohair' value: no 
2025-09-09 07:14:35,553 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Adhesive Mohair' 
2025-09-09 07:14:35,554 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,554 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,555 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor Type (Case 1: No Stop Bead/Mohair)' value: no 
2025-09-09 07:14:35,555 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 1 matching options for field 'Jamb Adaptor Type (Case 1: No Stop Bead/Mohair)' 
2025-09-09 07:14:35,555 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Processing option: 'No' (value: no) 
2025-09-09 07:14:35,555 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option 'No' has 0 operation mappings 
2025-09-09 07:14:35,556 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field 'Jamb Adaptor Type (Case 3: General Jamb Adaptor Required)' value:  
2025-09-09 07:14:35,556 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Found 0 matching options for field 'Jamb Adaptor Type (Case 3: General Jamb Adaptor Required)' 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ========== FIELD/OPTION MAPPING CALCULATION COMPLETE ========== 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Total operations found: 53 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Field operations: 18 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Option operations: 35 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Total cost: $260.75153600000004 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Quantity multiplier applied: 1.0 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] ========== OPERATION BREAKDOWN ========== 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #1: Receive Material Time = $5.6000000000000005 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #2: CNC Load / Unload Extrusion & Set Up = $2.64 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #3: QC & Colour Consitency CommandeX Door Frame = $3.8919024624 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #4: Cut CommandeX Door Frame = $15.42906316 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #5: Cut Plugh for CommandeX Door Frame = $6.0834591888 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #6: CNC Centre Lock Cut Out = $2.64 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #7: Insert Plugh into CommandeX Door Frame = $16.058820840000003 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #8: Assemble CommandeX Door with Crimped Corners = $33.364561886400004 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #9: Plugh CommandeX Door with Assembly Press = $18.5526612528 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #10: Clamp Product (even when midrail/brace is used) (per Panel) = $4.48 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #11: QC CommandeX Door = $17.9229035728 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #12: Cut Mesh or Sheet = $12.003181380800001 (source: field, field: Frame Colour, option: N/A) 
2025-09-09 07:14:35,558 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #13: Install Centre Lock = $6.48 (source: field, field: CommandeX Hinged - Lock Type (Top of Cut Out), option: N/A) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #14: Pick Centre Lock Site Kit = $1.36 (source: field, field: CommandeX Hinged - Lock Type (Top of Cut Out), option: N/A) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #15: QC Picked Hardware (Lock Site Kit) = $0.4 (source: field, field: CommandeX Hinged - Lock Type (Top of Cut Out), option: N/A) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #16: Wrap Hardware (Lock Site Kit) = $0.4 (source: field, field: CommandeX Hinged - Lock Type (Top of Cut Out), option: N/A) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #17: Load Door (Delivery) / Customer Collect (Pick Up) = $2.6449822560000005 (source: field, field: Number of Security Hinges (Pick Up), option: N/A) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #18: Delivery Time - Time allocated to CD1 even for pick up orders = $5.6000000000000005 (source: field, field: Number of Security Hinges (Pick Up), option: N/A) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #19: Cut Extrusions on Upcut Saw = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #20: CNC Load / Unload Extrusion & Set Up 2 = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #21: Extra Time to Cut French Door Mullion = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #22: CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #23: Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #24: Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #25: Insert Top & Bottom French Door Mullion Caps = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #26: Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door = $0.0 (source: option, field: French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door ), option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #27: Pick Hardware = $1.36 (source: option, field: CommandeX Hinged - Cylinder, option: CommandeX 5 Pin) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #28: QC Picked Hardware (Cylinder) = $0.4 (source: option, field: CommandeX Hinged - Cylinder, option: CommandeX 5 Pin) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #29: Wrap Hardware (Cylinder) = $0.4 (source: option, field: CommandeX Hinged - Cylinder, option: CommandeX 5 Pin) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #30: CNC Non-Lock Door Centre Striker Cut Out = $1.36 (source: option, field: Non-Lock Door Striker Cut Outs, option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #31: CNC Load / Unload Extrusion & Set Up = $2.64 (source: option, field: Non-Lock Door Striker Cut Outs, option: Yes) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #32: CNC Flush Bolt Cut Out = $2.64 (source: option, field: Flush Bolts & Patio Bolts (Non-Lock Door), option: Top & Bottom Flush Bolts) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #33: Rivet Flush Bolts into Non-Lock Door = $18.720000000000002 (source: option, field: Flush Bolts & Patio Bolts (Non-Lock Door), option: Top & Bottom Flush Bolts) 
2025-09-09 07:14:35,559 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #34: CNC Load / Unload Extrusion & Set Up = $2.64 (source: option, field: Flush Bolts & Patio Bolts (Non-Lock Door), option: Top & Bottom Flush Bolts) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #35: CNC Load / Unload Extrusion & Set Up = $2.64 (source: option, field: Flush Bolts & Patio Bolts (Non-Lock Door), option: Top & Bottom Flush Bolts) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #36: CNC Flush Bolt Hole in Top or Btm = $1.36 (source: option, field: Flush Bolts & Patio Bolts (Non-Lock Door), option: Top & Bottom Flush Bolts) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #37: CNC Flush Bolt Hole in Top or Btm = $1.36 (source: option, field: Flush Bolts & Patio Bolts (Non-Lock Door), option: Top & Bottom Flush Bolts) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #38: CNC Flush Bolt Cut Out = $2.64 (source: option, field: Flush Bolts & Patio Bolts (Non-Lock Door), option: Top & Bottom Flush Bolts) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #39: Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion = $11.36 (source: option, field: Midrail (Conditional Case 2), option: Yes) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #40: Cut Extrusions on Upcut Saw (Midrail) = $4.0 (source: option, field: Midrail (Conditional Case 2), option: Yes) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #41: Cut Plugh for CommandeX Midrail = $2.72 (source: option, field: Midrail (Conditional Case 2), option: Yes) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #42: Insert Plugh into CommandeX Midrail = $3.3600000000000003 (source: option, field: Midrail (Conditional Case 2), option: Yes) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #43: Extra Time to Assemble Product with CommandeX Midrail = $12.64 (source: option, field: Midrail (Conditional Case 2), option: Yes) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #44: Plugh CommandeX Midrail by Hand = $10.56 (source: option, field: Midrail (Conditional Case 2), option: Yes) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #45: QC Mill Finish Extrusions for Powder Coat or QC Buy A Length Extrusions Received = $2.72 (source: option, field: Midrail Colour (Standard Frame, Conditional Case 2), option: Match Door Frame (Powder Coat Fee)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #46: Extra Time to Cut Powder Coat Extrusions = $5.28 (source: option, field: Midrail Colour (Standard Frame, Conditional Case 2), option: Match Door Frame (Powder Coat Fee)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #47: CNC Hinge Holes (Lock Door) = $0.96 (source: option, field: Number of Security Hinges (Deliver/Site), option: 3 per Door - Loose with holes drilled (No rivets supplied)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #48: CNC Hinge Holes (Non-Lock Door) = $0.96 (source: option, field: Number of Security Hinges (Deliver/Site), option: 3 per Door - Loose with holes drilled (No rivets supplied)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #49: Pick Hinges = $2.4 (source: option, field: Number of Security Hinges (Deliver/Site), option: 3 per Door - Loose with holes drilled (No rivets supplied)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #50: QC Picked Hardware (x Qty of Hinges) = $2.4 (source: option, field: Number of Security Hinges (Deliver/Site), option: 3 per Door - Loose with holes drilled (No rivets supplied)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #51: Wrap Hardware (x Qty of Hinges) = $2.4 (source: option, field: Number of Security Hinges (Deliver/Site), option: 3 per Door - Loose with holes drilled (No rivets supplied)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #52: CNC Load / Unload Extrusion & Set Up = $2.64 (source: option, field: Number of Security Hinges (Deliver/Site), option: 3 per Door - Loose with holes drilled (No rivets supplied)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_DETAILED] Operation #53: CNC Load / Unload Extrusion & Set Up = $2.64 (source: option, field: Number of Security Hinges (Deliver/Site), option: 3 per Door - Loose with holes drilled (No rivets supplied)) 
2025-09-09 07:14:35,560 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] ========== UI_FIELD_OPTION_MAPPING SUMMARY ========== 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] Method: UI_FIELD_OPTION_MAPPING 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] Total operations: 53 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] Total cost: $260.75153600000004 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] Quantity multiplier: 1.0 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] Source type breakdown: 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   field: 18 operations, $155.55 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]     Operations: Receive Material Time, CNC Load / Unload Extrusion & Set Up, QC & Colour Consitency CommandeX Door Frame, Cut CommandeX Door Frame, Cut Plugh for CommandeX Door Frame, CNC Centre Lock Cut Out, Insert Plugh into CommandeX Door Frame, Assemble CommandeX Door with Crimped Corners, Plugh CommandeX Door with Assembly Press, Clamp Product (even when midrail/brace is used) (per Panel), QC CommandeX Door, Cut Mesh or Sheet, Install Centre Lock, Pick Centre Lock Site Kit, QC Picked Hardware (Lock Site Kit), Wrap Hardware (Lock Site Kit), Load Door (Delivery) / Customer Collect (Pick Up), Delivery Time - Time allocated to CD1 even for pick up orders 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   option: 35 operations, $105.20 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]     Operations: Cut Extrusions on Upcut Saw, CNC Load / Unload Extrusion & Set Up 2, Extra Time to Cut French Door Mullion, CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion, Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion, Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion, Insert Top & Bottom French Door Mullion Caps, Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door, Pick Hardware, QC Picked Hardware (Cylinder), Wrap Hardware (Cylinder), CNC Non-Lock Door Centre Striker Cut Out, CNC Load / Unload Extrusion & Set Up, CNC Flush Bolt Cut Out, Rivet Flush Bolts into Non-Lock Door, CNC Load / Unload Extrusion & Set Up, CNC Load / Unload Extrusion & Set Up, CNC Flush Bolt Hole in Top or Btm, CNC Flush Bolt Hole in Top or Btm, CNC Flush Bolt Cut Out, Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion, Cut Extrusions on Upcut Saw (Midrail), Cut Plugh for CommandeX Midrail, Insert Plugh into CommandeX Midrail, Extra Time to Assemble Product with CommandeX Midrail, Plugh CommandeX Midrail by Hand, QC Mill Finish Extrusions for Powder Coat or QC Buy A Length Extrusions Received, Extra Time to Cut Powder Coat Extrusions, CNC Hinge Holes (Lock Door), CNC Hinge Holes (Non-Lock Door), Pick Hinges, QC Picked Hardware (x Qty of Hinges), Wrap Hardware (x Qty of Hinges), CNC Load / Unload Extrusion & Set Up, CNC Load / Unload Extrusion & Set Up 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] Top 10 most expensive operations: 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #1: Assemble CommandeX Door with Crimped Corners = $33.36 (source: field) 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #2: Rivet Flush Bolts into Non-Lock Door = $18.72 (source: option) 
2025-09-09 07:14:35,561 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #3: Plugh CommandeX Door with Assembly Press = $18.55 (source: field) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #4: QC CommandeX Door = $17.92 (source: field) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #5: Insert Plugh into CommandeX Door Frame = $16.06 (source: field) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #6: Cut CommandeX Door Frame = $15.43 (source: field) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #7: Extra Time to Assemble Product with CommandeX Midrail = $12.64 (source: option) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #8: Cut Mesh or Sheet = $12.00 (source: field) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #9: Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion = $11.36 (source: option) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON]   #10: Plugh CommandeX Midrail by Hand = $10.56 (source: option) 
2025-09-09 07:14:35,562 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COMPARISON] ========== UI_FIELD_OPTION_MAPPING SUMMARY END ========== 
2025-09-09 07:14:35,958 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Dependency graph: {'_CALCULATED_deduction_assistance': [], '_CALCULATED_door_split_type': [], '_CALCULATED_lock_height': [], '_CALCULATED_manual_left_height': [], '_CALCULATED_manual_right_height': [], '_CALCULATED_height_calculation_method': [], '_CALCULATED_is_even_split': ['_CALCULATED_door_split_type'], '_CALCULATED_is_manual_mode': ['_CALCULATED_height_calculation_method'], '_CALCULATED_is_uneven_split': ['_CALCULATED_door_split_type'], '_CALCULATED_largest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_smallest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_halfway_point': ['_CALCULATED_smallest_door_height'], '_CALCULATED_halfway_minus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_16': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_32': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_height_minus_1000': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1003': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1019': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1090': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1098': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1137': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1153': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1169': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1248': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_270': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_317': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_330': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_333': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_349': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_428': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_740': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_787': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_800': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_803': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_819': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_898': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_940': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_987': ['_CALCULATED_smallest_door_height'], '_CALCULATED_Largest_Sum_Door_Width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_even_bottom_width': [], '_CALCULATED_even_middle_width': [], '_CALCULATED_even_top_width': [], '_CALCULATED_jamb_adaptor_left_length_mm': [], '_CALCULATED_jamb_adaptor_top_length_mm': [], '_CALCULATED_largest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_left_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_right_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_Clamp_Product': ['_CALCULATED_left_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width'], '_CALCULATED_left_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_manual_formula': ['_CALCULATED_smallest_door_height', '_CALCULATED_manual_right_height', '_CALCULATED_manual_left_height'], '_CALCULATED_right_Clamp_Product': ['_CALCULATED_largest_right_door_width', '_CALCULATED_right_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_right_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_right_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_smallest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_mesh_required': ['_CALCULATED_largest_door_width', '_CALCULATED_largest_door_height'], '_CALCULATED_mesh_width': ['_CALCULATED_largest_door_width', '_CALCULATED_mesh_required'], '_CALCULATED_mesh_height': ['_CALCULATED_largest_door_height', '_CALCULATED_mesh_required'], '_CALCULATED_midrail_case1': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_mesh_series': ['_CALCULATED_mesh_required'], '_CALCULATED_midrail_case2': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_midrail_height': [], '_CALCULATED_mesh_area': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_required': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_type': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required'], '_CALCULATED_mesh_area_m2': ['_CALCULATED_mesh_area'], '_CALCULATED_mesh_perimeter': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_aspect_ratio': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_size_category': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_required']} 
2025-09-09 07:14:35,958 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Improved topological sort successful: ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
2025-09-09 07:14:35,959 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Starting calculation with 71 fields in dependency order 
2025-09-09 07:14:36,336 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Maximum calculation passes (10) reached. Some fields may not be calculated. 
2025-09-09 07:14:36,336 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_operation_type could not be calculated after 10 passes 
2025-09-09 07:14:36,336 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_size_category could not be calculated after 10 passes 
2025-09-09 07:14:36,336 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Calculation completed in 10 passes. 69/71 fields calculated successfully. 
2025-09-09 07:14:37,587 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Maximum calculation passes (10) reached. Some fields may not be calculated. 
2025-09-09 07:14:37,587 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_operation_type could not be calculated after 10 passes 
2025-09-09 07:14:37,587 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_size_category could not be calculated after 10 passes 
2025-09-09 07:14:37,587 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Calculation completed in 10 passes. 69/71 fields calculated successfully. 
2025-09-09 07:14:45,717 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Dependency graph: {'_CALCULATED_deduction_assistance': [], '_CALCULATED_door_split_type': [], '_CALCULATED_lock_height': [], '_CALCULATED_manual_left_height': [], '_CALCULATED_manual_right_height': [], '_CALCULATED_height_calculation_method': [], '_CALCULATED_is_even_split': ['_CALCULATED_door_split_type'], '_CALCULATED_is_manual_mode': ['_CALCULATED_height_calculation_method'], '_CALCULATED_is_uneven_split': ['_CALCULATED_door_split_type'], '_CALCULATED_largest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_smallest_door_height': ['_CALCULATED_manual_left_height', '_CALCULATED_height_calculation_method', '_CALCULATED_manual_right_height'], '_CALCULATED_halfway_point': ['_CALCULATED_smallest_door_height'], '_CALCULATED_halfway_minus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_16': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_32': ['_CALCULATED_halfway_point'], '_CALCULATED_halfway_plus_79': ['_CALCULATED_halfway_point'], '_CALCULATED_height_minus_1000': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1003': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1019': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1090': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1098': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1137': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1153': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1169': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_1248': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_270': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_317': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_330': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_333': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_349': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_428': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_740': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_787': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_800': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_803': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_819': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_898': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_940': ['_CALCULATED_smallest_door_height'], '_CALCULATED_height_minus_987': ['_CALCULATED_smallest_door_height'], '_CALCULATED_Largest_Sum_Door_Width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_even_bottom_width': [], '_CALCULATED_even_middle_width': [], '_CALCULATED_even_top_width': [], '_CALCULATED_jamb_adaptor_left_length_mm': [], '_CALCULATED_jamb_adaptor_top_length_mm': [], '_CALCULATED_largest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_left_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_largest_right_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_Clamp_Product': ['_CALCULATED_left_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width'], '_CALCULATED_left_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_left_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_manual_formula': ['_CALCULATED_smallest_door_height', '_CALCULATED_manual_right_height', '_CALCULATED_manual_left_height'], '_CALCULATED_right_Clamp_Product': ['_CALCULATED_largest_right_door_width', '_CALCULATED_right_height_door', '_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_right_door_middle_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_right_height_door': ['_CALCULATED_height_calculation_method'], '_CALCULATED_smallest_door_width': ['_CALCULATED_height_calculation_method'], '_CALCULATED_mesh_required': ['_CALCULATED_largest_door_width', '_CALCULATED_largest_door_height'], '_CALCULATED_mesh_width': ['_CALCULATED_largest_door_width', '_CALCULATED_mesh_required'], '_CALCULATED_mesh_height': ['_CALCULATED_largest_door_height', '_CALCULATED_mesh_required'], '_CALCULATED_midrail_case1': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_mesh_series': ['_CALCULATED_mesh_required'], '_CALCULATED_midrail_case2': ['_CALCULATED_largest_door_height', '_CALCULATED_largest_door_width'], '_CALCULATED_midrail_height': [], '_CALCULATED_mesh_area': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_required': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_operation_type': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required'], '_CALCULATED_mesh_area_m2': ['_CALCULATED_mesh_area'], '_CALCULATED_mesh_perimeter': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_aspect_ratio': ['_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height'], '_CALCULATED_mesh_size_category': ['_CALCULATED_mesh_area', '_CALCULATED_mesh_required']} 
2025-09-09 07:14:45,718 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Improved topological sort successful: ['_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_uneven_split', '_CALCULATED_is_manual_mode', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_manual_formula', '_CALCULATED_left_Clamp_Product', '_CALCULATED_right_Clamp_Product', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case2', '_CALCULATED_mesh_series', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
2025-09-09 07:14:45,718 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Starting calculation with 71 fields in dependency order 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Maximum calculation passes (10) reached. Some fields may not be calculated. 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_door_split_type could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_lock_height could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_calculation_method could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_largest_door_height could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_smallest_door_height could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_halfway_point could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_halfway_minus_79 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_halfway_plus_16 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_halfway_plus_32 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_halfway_plus_79 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1000 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1003 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1019 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1090 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1098 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1137 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1153 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1169 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_1248 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_270 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_317 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_330 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_333 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_349 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_428 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_740 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_787 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_800 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_803 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_819 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_898 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_940 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_height_minus_987 could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_even_bottom_width could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_even_middle_width could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_even_top_width could not be calculated after 10 passes 
2025-09-09 07:14:47,666 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_jamb_adaptor_left_length_mm could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_jamb_adaptor_top_length_mm could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_Largest_Sum_Door_Width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_door_width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_largest_door_width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_largest_left_door_width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_largest_right_door_width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_left_door_middle_width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_left_height_door could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_right_door_middle_width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_right_height_door could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_smallest_door_width could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_left_Clamp_Product could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_right_Clamp_Product could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_required could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_midrail_case1 could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_midrail_case2 could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_operation_required could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_operation_type could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Field _CALCULATED_mesh_size_category could not be calculated after 10 passes 
2025-09-09 07:14:47,667 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_calculated_field: [DEPENDENCY_DEBUG] Calculation completed in 10 passes. 15/71 fields calculated successfully. 
2025-09-09 07:14:47,883 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-09 07:14:47,899 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:47,899 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:47,902 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:47,902 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:47,905 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:47,906 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:47,909 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:47,909 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:47,910 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-09 07:14:47,910 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-09 07:14:47,913 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:47,914 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-09 07:14:47,915 252259 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
