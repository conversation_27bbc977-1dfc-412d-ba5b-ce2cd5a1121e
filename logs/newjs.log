web.assets_web.min.js:22170 [DimensionRangesEditor] Widget mounted for field width_ranges
web.assets_web.min.js:22170 [DimensionRangesEditor] Props: {readonly: false, id: 'width_ranges_0', name: 'width_ranges', record: Proxy(Record)}
web.assets_web.min.js:22170 [DimensionRangesEditor] Record data: Proxy(Object) {name: 'DM1 QC & Colour Consitency Comm - Subcategory - Window Screen SWS', product_template_id: Proxy(Array), matrix_id: Proxy(Array), is_sale_price_matrix: false, height_calculated_field_id: false, …}
web.assets_web.min.js:22172 [DimensionRangesEditor] Loading ranges for field width_ranges: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Full record data: Proxy(Object) {name: 'DM1 QC & Colour Consitency Comm - Subcategory - Window Screen SWS', product_template_id: Proxy(Array), matrix_id: Proxy(Array), is_sale_price_matrix: false, height_calculated_field_id: false, …}
web.assets_web.min.js:22172 [DimensionRangesEditor] Converted JSON string: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Parsed raw ranges: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Normalized ranges: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}}
web.assets_web.min.js:22172 [DimensionRangesEditor] Found 17 ranges, forcing re-render
web.assets_web.min.js:22170 [DimensionRangesEditor] Widget mounted for field height_ranges
web.assets_web.min.js:22170 [DimensionRangesEditor] Props: {readonly: false, id: 'height_ranges_0', name: 'height_ranges', record: Proxy(Record)}
web.assets_web.min.js:22170 [DimensionRangesEditor] Record data: Proxy(Object) {name: 'DM1 QC & Colour Consitency Comm - Subcategory - Window Screen SWS', product_template_id: Proxy(Array), matrix_id: Proxy(Array), is_sale_price_matrix: false, height_calculated_field_id: false, …}
web.assets_web.min.js:22172 [DimensionRangesEditor] Loading ranges for field height_ranges: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Full record data: Proxy(Object) {name: 'DM1 QC & Colour Consitency Comm - Subcategory - Window Screen SWS', product_template_id: Proxy(Array), matrix_id: Proxy(Array), is_sale_price_matrix: false, height_calculated_field_id: false, …}
web.assets_web.min.js:22172 [DimensionRangesEditor] Converted JSON string: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Parsed raw ranges: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Normalized ranges: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}}
web.assets_web.min.js:22172 [DimensionRangesEditor] Found 17 ranges, forcing re-render
web.assets_web.min.js:22132 [PriceMatrixWidget] Rendering matrix: {heights: 17, widths: 17, container: table.matrix-table}
web.assets_web.min.js:22170 [DimensionRangesEditor] Delayed reload for field width_ranges
web.assets_web.min.js:22172 [DimensionRangesEditor] Loading ranges for field width_ranges: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Full record data: Proxy(Object) {name: 'DM1 QC & Colour Consitency Comm - Subcategory - Window Screen SWS', product_template_id: Proxy(Array), matrix_id: Proxy(Array), is_sale_price_matrix: false, height_calculated_field_id: false, …}
web.assets_web.min.js:22172 [DimensionRangesEditor] Converted JSON string: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Parsed raw ranges: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Normalized ranges: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}}
web.assets_web.min.js:22172 [DimensionRangesEditor] Found 17 ranges, forcing re-render
web.assets_web.min.js:22170 [DimensionRangesEditor] Delayed reload for field height_ranges
web.assets_web.min.js:22172 [DimensionRangesEditor] Loading ranges for field height_ranges: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Full record data: Proxy(Object) {name: 'DM1 QC & Colour Consitency Comm - Subcategory - Window Screen SWS', product_template_id: Proxy(Array), matrix_id: Proxy(Array), is_sale_price_matrix: false, height_calculated_field_id: false, …}
web.assets_web.min.js:22172 [DimensionRangesEditor] Converted JSON string: [{"min": 345, "max": 345, "label": "345"}, {"min": 346, "max": 495, "label": "495"}, {"min": 496, "max": 645, "label": "645"}, {"min": 646, "max": 795, "label": "795"}, {"min": 796, "max": 945, "label": "945"}, {"min": 946, "max": 1045, "label": "1045"}, {"min": 1046, "max": 1245, "label": "1245"}, {"min": 1246, "max": 1345, "label": "1345"}, {"min": 1346, "max": 1545, "label": "1545"}, {"min": 1546, "max": 1845, "label": "1845"}, {"min": 1846, "max": 2045, "label": "2045"}, {"min": 2046, "max": 2145, "label": "2145"}, {"min": 2146, "max": 2445, "label": "2445"}, {"min": 2446, "max": 2645, "label": "2645"}, {"min": 2646, "max": 3045, "label": "3045"}, {"min": 3046, "max": 3695, "label": "3695"}, {"min": 3696, "max": 4020, "label": "4020"}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Parsed raw ranges: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
web.assets_web.min.js:22172 [DimensionRangesEditor] Normalized ranges: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}}
web.assets_web.min.js:22172 [DimensionRangesEditor] Found 17 ranges, forcing re-render
web.assets_web.min.js:22166 [PriceMatrixWidget] Scrolling container found: {element: div.matrix-scroll-wrapper, classes: 'matrix-scroll-wrapper', styles: {…}}
web.assets_web.min.js:22166 [PriceMatrixWidget] Table element: {element: tbody, classes: '', styles: {…}}
web.assets_web.min.js:22166 [PriceMatrixWidget] Width comparison: {tableWidth: 1610, containerWidth: 1610, parentWidth: 1625, shouldScroll: false, tableOffsetWidth: 1610, …}
web.assets_web.min.js:22166 [PriceMatrixWidget] Table is wider than 1200px, forcing container constraint
web.assets_web.min.js:22128 [PriceMatrixWidget] Auto-refresh detected change for field matrix_data
web.assets_web.min.js:22132 [PriceMatrixWidget] Rendering matrix: {heights: 17, widths: 17, container: table.matrix-table}
