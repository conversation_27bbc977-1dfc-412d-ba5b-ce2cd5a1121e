#!/usr/bin/env python3
"""
Test script to verify calculated fields are working
"""
import requests
import json

def test_calculated_fields():
    """Test if calculated fields are being calculated correctly"""
    
    # Test field values (same as in our test case)
    field_values = {
        "bx_dbl_hinge_location": "brisbane",
        "bx_dbl_hinge_jamb_reveal_gt_20mm": "yes_gt_20mm",
        "bx_dbl_hinge_new_door_clear_handle": "yes",
        "bx_dbl_hinge_handle_clear_existing": "yes",
        "bx_dbl_hinge_swing_path_clear": "yes",
        "bx_dbl_hinge_quantity": 1,
        "bx_dbl_hinge_frame_colour": "black_anodise_15mm",
        "bx_dbl_hinge_mesh_type": "pet_mesh",
        "bx_dbl_hinge_door_height_mm": 2100,
        "bx_dbl_hinge_door_width_mm": 1200,
        "bx_dbl_hinge_num_sec_hinges_pickup": 0,
        "bx_dbl_hinge_num_sec_hinges_deliver": "3_per_door_loose_drilled",
        "bx_dbl_hinge_sec_hinge_packers_qty": 0
    }
    
    print("=== TESTING CALCULATED FIELDS ===")
    print(f"Input field values: height={field_values.get('bx_dbl_hinge_door_height_mm')}, width={field_values.get('bx_dbl_hinge_door_width_mm')}")
    
    # Test calculated fields endpoint
    try:
        response = requests.post('http://localhost:8069/config_matrix/calculate_field_values', 
                               json={
                                   "jsonrpc": "2.0",
                                   "method": "call",
                                   "params": {
                                       "template_id": 24,
                                       "field_values": field_values
                                   }
                               },
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result and 'result' in result['result']:
                calculated_fields = result['result']['result']
                print(f"✅ Calculated fields response received: {len(calculated_fields)} fields")
                
                # Check for the specific fields we need
                height = calculated_fields.get('_CALCULATED_largest_door_height')
                width = calculated_fields.get('_CALCULATED_largest_door_width')
                
                print(f"_CALCULATED_largest_door_height: {height}")
                print(f"_CALCULATED_largest_door_width: {width}")
                
                if height and width:
                    print("✅ SUCCESS: Both calculated fields are available!")
                    
                    # Test midrail visibility condition
                    condition1 = height <= 2510 and width <= 1310
                    condition2 = height <= 1310
                    visibility = condition1 or condition2
                    
                    print(f"Midrail Case 2 visibility test:")
                    print(f"  Condition 1: {height} <= 2510 && {width} <= 1310 = {condition1}")
                    print(f"  Condition 2: {height} <= 1310 = {condition2}")
                    print(f"  Final visibility: {condition1} OR {condition2} = {visibility}")
                    
                    if visibility:
                        print("✅ Midrail (Conditional Case 2) field SHOULD be visible")
                    else:
                        print("❌ Midrail (Conditional Case 2) field should NOT be visible")
                else:
                    print("❌ FAILED: Calculated fields are missing")
                    print(f"Available calculated fields: {list(calculated_fields.keys())}")
            else:
                print(f"❌ Unexpected response format: {result}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing calculated fields: {e}")
    
    # Test operation costs endpoint
    print("\n=== TESTING OPERATION COSTS ===")
    try:
        response = requests.post('http://localhost:8069/config_matrix/calculate_operation_costs', 
                               json={
                                   "jsonrpc": "2.0",
                                   "method": "call",
                                   "params": {
                                       "template_id": 24,
                                       "field_values": field_values
                                   }
                               },
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result:
                operation_result = result['result']
                if operation_result.get('success'):
                    operations = operation_result.get('operations', [])
                    total_cost = operation_result.get('total_cost', 0)
                    print(f"✅ Operation costs calculated: {len(operations)} operations, ${total_cost}")
                    
                    # Check for midrail operations
                    midrail_operations = [op for op in operations if 'midrail' in op.get('name', '').lower()]
                    print(f"Midrail operations found: {len(midrail_operations)}")
                    for op in midrail_operations:
                        print(f"  - {op.get('name', 'Unknown')}: ${op.get('cost', 0)}")
                else:
                    print(f"❌ Operation costs failed: {operation_result.get('error')}")
            else:
                print(f"❌ Unexpected response format: {result}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing operation costs: {e}")

if __name__ == "__main__":
    test_calculated_fields()
