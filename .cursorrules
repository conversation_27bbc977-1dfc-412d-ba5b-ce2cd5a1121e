# Cursor Rules for Odoo Development

## Code Style and Standards
- Always follow PEP 8 for Python files
- Use proper Odoo 18 patterns and avoid deprecated methods
- Follow Odoo standard conventions for all development

## Requirements Clarification
- When receiving unclear requirements, ask clarifying questions before implementation
- Create cursor rules to enforce this behavior for future development

## Documentation Standards
- Place all new documentation files in the module's 'docs' folder
- Follow comprehensive documentation practices for all modules

## @api.onchange Best Practices - CRITICAL RULE
- **NEVER put business logic in @api.onchange methods**
- @api.onchange only triggers in UI during user interaction
- @api.onchange does NOT run during:
  - create() or write() operations
  - Server-side automation
  - Scheduled actions
  - Data imports via CSV/Excel
  - API calls and external integrations
- **ALWAYS use standalone methods for business logic**
- Call standalone methods from:
  - @api.onchange (for UI interactions)
  - create() and write() (for backend operations)
  - Scheduled actions and automation
  - Data imports and API integrations
- **Separate UI logic (@api.onchange) from backend logic (create/write)**
- Use @api.depends for computed fields, @api.onchange for UI interactions
- Document standalone methods with clear usage instructions
- Test both UI and backend scenarios for business logic 