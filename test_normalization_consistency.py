#!/usr/bin/env python3
"""
Test script to verify field value normalization and UI/backend consistency
"""
import requests
import j<PERSON>

def test_ui_backend_consistency():
    """Test that UI and backend calculations produce consistent results after normalization"""
    
    # Test field values that previously caused differences
    # These values simulate what comes from the UI (strings) vs backend (mixed types)
    ui_field_values = {
        "bx_dbl_hinge_location": "brisbane",
        "bx_dbl_hinge_jamb_reveal_gt_20mm": "yes_gt_20mm",
        "bx_dbl_hinge_new_door_clear_handle": "yes",
        "bx_dbl_hinge_handle_clear_existing": "yes",
        "bx_dbl_hinge_swing_path_clear": "yes",
        "bx_dbl_hinge_quantity": "1",  # String (UI format)
        "bx_dbl_hinge_frame_colour": "black_anodise_15mm",
        "bx_dbl_hinge_mesh_type": "pet_mesh",
        "bx_dbl_hinge_door_height_mm": "2100",  # String (UI format)
        "bx_dbl_hinge_door_width_mm": "1200",   # String (UI format)
        "bx_dbl_hinge_num_sec_hinges_pickup": "0",  # String (UI format)
        "bx_dbl_hinge_num_sec_hinges_deliver": "3_per_door_loose_drilled",
        "bx_dbl_hinge_sec_hinge_packers_qty": "0"  # String (UI format)
    }
    
    backend_field_values = {
        "bx_dbl_hinge_location": "brisbane",
        "bx_dbl_hinge_jamb_reveal_gt_20mm": "yes_gt_20mm",
        "bx_dbl_hinge_new_door_clear_handle": "yes",
        "bx_dbl_hinge_handle_clear_existing": "yes",
        "bx_dbl_hinge_swing_path_clear": "yes",
        "bx_dbl_hinge_quantity": 1,  # Integer (backend format)
        "bx_dbl_hinge_frame_colour": "black_anodise_15mm",
        "bx_dbl_hinge_mesh_type": "pet_mesh",
        "bx_dbl_hinge_door_height_mm": 2100,  # Integer (backend format)
        "bx_dbl_hinge_door_width_mm": 1200,   # Integer (backend format)
        "bx_dbl_hinge_num_sec_hinges_pickup": 0,  # Integer (backend format)
        "bx_dbl_hinge_num_sec_hinges_deliver": "3_per_door_loose_drilled",
        "bx_dbl_hinge_sec_hinge_packers_qty": 0  # Integer (backend format)
    }
    
    print("=== TESTING UI/BACKEND CONSISTENCY WITH NORMALIZATION ===")
    print("Testing field value normalization to ensure consistent calculations...")
    print()
    
    # Test UI calculation (field/option mapping)
    ui_result = test_operation_costs(ui_field_values, "UI_FIELD_OPTION_MAPPING", "ui_field_option_mapping")
    
    # Test backend calculation (save config)
    backend_result = test_operation_costs(backend_field_values, "BACKEND_SAVE_CONFIG", "save_config")
    
    # Compare results
    if ui_result and backend_result:
        print("\n=== CONSISTENCY COMPARISON ===")
        
        ui_operations = ui_result.get('operations', [])
        ui_total = ui_result.get('total_cost', 0)
        
        backend_operations = backend_result.get('operations', [])
        backend_total = backend_result.get('total_cost', 0)
        
        print(f"UI Results:      {len(ui_operations)} operations, Total: ${ui_total}")
        print(f"Backend Results: {len(backend_operations)} operations, Total: ${backend_total}")
        
        # Check if results are now consistent
        operation_count_match = len(ui_operations) == len(backend_operations)
        cost_match = abs(ui_total - backend_total) < 0.01  # Allow for small floating point differences
        
        print(f"\nOperation Count Match: {'✅' if operation_count_match else '❌'}")
        print(f"Total Cost Match:      {'✅' if cost_match else '❌'}")
        
        if operation_count_match and cost_match:
            print("\n🎉 SUCCESS! UI and Backend calculations are now consistent!")
            print("   Field value normalization has resolved the differences.")
            return True
        else:
            print("\n❌ INCONSISTENCY DETECTED!")
            print("   Differences still exist between UI and backend calculations.")
            
            # Show detailed comparison
            print("\n=== DETAILED OPERATION COMPARISON ===")
            print("UI Operations:")
            for i, op in enumerate(ui_operations, 1):
                print(f"  {i}. {op.get('name', 'Unknown')}: ${op.get('cost', 0)} (source: {op.get('source_type', 'unknown')})")
            
            print("\nBackend Operations:")
            for i, op in enumerate(backend_operations, 1):
                print(f"  {i}. {op.get('name', 'Unknown')}: ${op.get('cost', 0)} (source: {op.get('source_type', 'unknown')})")
            
            return False
    else:
        print("\n❌ FAILED! Could not retrieve results from UI or backend calculations.")
        return False


def test_operation_costs(field_values, context_name, calculation_context):
    """Test operation costs calculation with specific context"""
    
    print(f"\n--- Testing {context_name} ---")
    print(f"Field value types: quantity={type(field_values.get('bx_dbl_hinge_quantity'))}, height={type(field_values.get('bx_dbl_hinge_door_height_mm'))}")
    
    try:
        response = requests.post('http://localhost:8069/config_matrix/calculate_operation_costs', 
                               json={
                                   "jsonrpc": "2.0",
                                   "method": "call",
                                   "params": {
                                       "template_id": 24,
                                       "field_values": field_values,
                                       "calculation_context": calculation_context
                                   }
                               },
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result and result['result'].get('success'):
                operation_result = result['result']
                operations = operation_result.get('operations', [])
                total_cost = operation_result.get('total_cost', 0)
                
                print(f"✅ {context_name}: {len(operations)} operations, ${total_cost}")
                
                # Show first few operations for debugging
                for i, op in enumerate(operations[:3], 1):
                    print(f"   {i}. {op.get('name', 'Unknown')}: ${op.get('cost', 0)}")
                if len(operations) > 3:
                    print(f"   ... and {len(operations) - 3} more operations")
                
                return operation_result
            else:
                error = result.get('result', {}).get('error', 'Unknown error')
                print(f"❌ {context_name} failed: {error}")
                return None
        else:
            print(f"❌ {context_name} HTTP Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ {context_name} Exception: {e}")
        return None


def test_normalization_logging():
    """Test that normalization logging is working"""
    
    print("\n=== TESTING NORMALIZATION LOGGING ===")
    print("This test should generate normalization logs in the Odoo server...")
    
    # Test with mixed data types to trigger normalization
    mixed_field_values = {
        "bx_dbl_hinge_quantity": "5",      # String -> should normalize to int
        "bx_dbl_hinge_door_height_mm": "2100.5",  # String -> should normalize to float
        "bx_dbl_hinge_door_width_mm": 1200,       # Already int -> should stay int
        "test_boolean_field": "true",      # String -> should normalize to bool
        "test_empty_field": "",            # Empty string -> should get default value
        "_CALCULATED_some_field": 42.5     # Calculated field -> should be preserved
    }
    
    # Make a request that will trigger normalization
    try:
        response = requests.post('http://localhost:8069/config_matrix/calculate_operation_costs', 
                               json={
                                   "jsonrpc": "2.0",
                                   "method": "call",
                                   "params": {
                                       "template_id": 24,
                                       "field_values": mixed_field_values,
                                       "calculation_context": "ui_field_option_mapping"
                                   }
                               },
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            print("✅ Normalization test request completed successfully")
            print("   Check the Odoo server logs for [NORMALIZATION] entries")
            print("   You should see field type conversions and normalization details")
        else:
            print(f"❌ Normalization test failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Normalization test exception: {e}")


if __name__ == "__main__":
    print("Testing Field Value Normalization and UI/Backend Consistency")
    print("=" * 70)
    
    # Test consistency
    success = test_ui_backend_consistency()
    
    # Test normalization logging
    test_normalization_logging()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 OVERALL SUCCESS! Field value normalization is working correctly!")
        print("   UI and backend calculations are now consistent.")
    else:
        print("💥 ISSUES DETECTED! Further investigation may be needed.")
        print("   Check the Odoo server logs for detailed normalization information.")
    
    print("\nTo see detailed normalization logs, check the Odoo server output for:")
    print("   [NORMALIZATION] entries - showing field type conversions")
    print("   [TEMPLATE_COST_DEBUG] entries - showing template calculations")
    print("   [OPERATION_COSTS_DETAILED] entries - showing operation cost summaries")
