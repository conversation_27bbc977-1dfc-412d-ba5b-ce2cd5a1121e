#!/usr/bin/env python3
"""
Log Filter Script for Odoo Debug Tags

This script filters log files to extract only entries containing specific debug tags
like [BOM_PREVIEW_DEBUG], [QUANTITY_DEBUG], etc.

Usage:
    python log_filter.py input.log output.log --tags BOM_PREVIEW_DEBUG QUANTITY_DEBUG
    python log_filter.py input.log output.log --tags BOM_PREVIEW_DEBUG --case-sensitive
    python log_filter.py input.log output.log --tags ".*_DEBUG" --regex
"""

import argparse
import re
import sys
from pathlib import Path
from typing import List, Set, Pattern
import logging

# Configure logging for the script itself
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LogFilter:
    """Log filter class for extracting specific debug tags from log files."""
    
    def __init__(self, tags: List[str], use_regex: bool = False, case_sensitive: bool = False):
        """
        Initialize the log filter.
        
        Args:
            tags: List of tags to filter for
            use_regex: Whether to treat tags as regex patterns
            case_sensitive: Whether tag matching should be case sensitive
        """
        self.tags = tags
        self.use_regex = use_regex
        self.case_sensitive = case_sensitive
        self.compiled_patterns: List[Pattern] = []
        
        # Compile patterns for efficient matching
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile tag patterns for efficient matching."""
        flags = 0 if self.case_sensitive else re.IGNORECASE
        
        for tag in self.tags:
            if self.use_regex:
                # Treat as regex pattern
                try:
                    pattern = re.compile(tag, flags)
                    self.compiled_patterns.append(pattern)
                    logger.info(f"Compiled regex pattern: {tag}")
                except re.error as e:
                    logger.error(f"Invalid regex pattern '{tag}': {e}")
                    sys.exit(1)
            else:
                # Escape special characters and treat as literal
                escaped_tag = re.escape(tag)
                pattern = re.compile(escaped_tag, flags)
                self.compiled_patterns.append(pattern)
                logger.info(f"Compiled literal pattern: {tag}")
    
    def matches_any_tag(self, line: str) -> bool:
        """
        Check if a line matches any of the configured tags.
        
        Args:
            line: Log line to check
            
        Returns:
            True if line matches any tag, False otherwise
        """
        for pattern in self.compiled_patterns:
            if pattern.search(line):
                return True
        return False
    
    def filter_log_file(self, input_file: Path, output_file: Path) -> dict:
        """
        Filter log file and write matching lines to output file.
        
        Args:
            input_file: Path to input log file
            output_file: Path to output log file
            
        Returns:
            Dictionary with filtering statistics
        """
        stats = {
            'total_lines': 0,
            'matching_lines': 0,
            'tags_found': set(),
            'files_processed': 0
        }
        
        try:
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as infile:
                with open(output_file, 'w', encoding='utf-8') as outfile:
                    for line_num, line in enumerate(infile, 1):
                        stats['total_lines'] += 1
                        
                        if self.matches_any_tag(line):
                            stats['matching_lines'] += 1
                            outfile.write(line)
                            
                            # Extract and record which tags were found
                            for pattern in self.compiled_patterns:
                                match = pattern.search(line)
                                if match:
                                    stats['tags_found'].add(match.group())
            
            stats['files_processed'] = 1
            logger.info(f"Successfully filtered {input_file} -> {output_file}")
            
        except FileNotFoundError:
            logger.error(f"Input file not found: {input_file}")
            raise
        except PermissionError:
            logger.error(f"Permission denied accessing file: {input_file}")
            raise
        except Exception as e:
            logger.error(f"Error processing file {input_file}: {e}")
            raise
        
        return stats


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Filter log files to extract entries with specific debug tags",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Filter for specific debug tags
  python log_filter.py input.log output.log --tags BOM_PREVIEW_DEBUG QUANTITY_DEBUG
  
  # Use regex pattern to match all debug tags
  python log_filter.py input.log output.log --tags ".*_DEBUG" --regex
  
  # Case-sensitive matching
  python log_filter.py input.log output.log --tags BOM_PREVIEW_DEBUG --case-sensitive
  
  # Multiple specific tags
  python log_filter.py input.log output.log --tags BOM_PREVIEW_DEBUG QUANTITY_DEBUG PRICING_DEBUG
        """
    )
    
    parser.add_argument(
        'input_file',
        type=Path,
        help='Input log file path'
    )
    
    parser.add_argument(
        'output_file',
        type=Path,
        help='Output filtered log file path'
    )
    
    parser.add_argument(
        '--tags',
        nargs='+',
        required=True,
        help='Debug tags to filter for (e.g., BOM_PREVIEW_DEBUG QUANTITY_DEBUG)'
    )
    
    parser.add_argument(
        '--regex',
        action='store_true',
        help='Treat tags as regex patterns instead of literal strings'
    )
    
    parser.add_argument(
        '--case-sensitive',
        action='store_true',
        help='Perform case-sensitive matching'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    
    parser.add_argument(
        '--stats',
        action='store_true',
        help='Show detailed statistics after filtering'
    )
    
    return parser.parse_args()


def print_statistics(stats: dict, tags: List[str]):
    """Print filtering statistics."""
    print("\n" + "="*60)
    print("FILTERING STATISTICS")
    print("="*60)
    print(f"Total lines processed: {stats['total_lines']:,}")
    print(f"Matching lines found: {stats['matching_lines']:,}")
    print(f"Files processed: {stats['files_processed']}")
    
    if stats['total_lines'] > 0:
        percentage = (stats['matching_lines'] / stats['total_lines']) * 100
        print(f"Match percentage: {percentage:.2f}%")
    
    print(f"\nTags searched for: {', '.join(tags)}")
    print(f"Tags actually found: {', '.join(sorted(stats['tags_found']))}")
    
    if stats['matching_lines'] == 0:
        print("\n⚠️  No matching lines found. Check your tag patterns.")
    else:
        print(f"\n✅ Successfully extracted {stats['matching_lines']} matching lines.")


def main():
    """Main function."""
    args = parse_arguments()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate input file
    if not args.input_file.exists():
        logger.error(f"Input file does not exist: {args.input_file}")
        sys.exit(1)
    
    # Create output directory if it doesn't exist
    args.output_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Initialize log filter
    try:
        log_filter = LogFilter(
            tags=args.tags,
            use_regex=args.regex,
            case_sensitive=args.case_sensitive
        )
    except Exception as e:
        logger.error(f"Failed to initialize log filter: {e}")
        sys.exit(1)
    
    # Filter the log file
    try:
        logger.info(f"Starting log filtering...")
        logger.info(f"Input file: {args.input_file}")
        logger.info(f"Output file: {args.output_file}")
        logger.info(f"Tags: {', '.join(args.tags)}")
        logger.info(f"Regex mode: {args.regex}")
        logger.info(f"Case sensitive: {args.case_sensitive}")
        
        stats = log_filter.filter_log_file(args.input_file, args.output_file)
        
        # Print statistics if requested
        if args.stats or args.verbose:
            print_statistics(stats, args.tags)
        
        logger.info("Log filtering completed successfully!")
        
    except Exception as e:
        logger.error(f"Log filtering failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
